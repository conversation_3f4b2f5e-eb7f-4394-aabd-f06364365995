<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PersonalTaskComment extends Model
{
    protected $fillable = [
        'task_id',
        'user_id',
        'comment',
        'parent_id',
        'comment_reaction',
    ];

    protected $casts = [
        'comment_reaction' => 'array',
    ];

    /**
     * Get the task that owns the comment
     */
    public function task()
    {
        return $this->belongsTo('App\Models\PersonalTask', 'task_id');
    }

    /**
     * Get the user that created the comment
     */
    public function user()
    {
        return $this->belongsTo('App\Models\User', 'user_id');
    }

    /**
     * Get the parent comment
     */
    public function parent()
    {
        return $this->belongsTo('App\Models\PersonalTaskComment', 'parent_id');
    }

    /**
     * Get the replies to this comment
     */
    public function replies()
    {
        return $this->hasMany('App\Models\PersonalTaskComment', 'parent_id')->orderBy('created_at', 'ASC');
    }

    /**
     * Check if this is a reply
     */
    public function isReply()
    {
        return !is_null($this->parent_id);
    }
}
