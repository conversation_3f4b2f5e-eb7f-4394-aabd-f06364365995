<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LeadComment extends Model
{
    protected $fillable = [
        'lead_id',
        'user_id',
        'comment',
        'comment_reaction',
        'created_by',
        'parent_id',
    ];

    protected $casts = [
        'comment_reaction' => 'array'
    ];

    public function lead()
    {
        return $this->belongsTo(Lead::class);

    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Add these relationships for replies
    public function replies()
    {
        return $this->hasMany(LeadComment::class, 'parent_id');
    }

    public function parent()
    {
        return $this->belongsTo(LeadComment::class, 'parent_id');
    }

    
    // Get reaction counts from JSON column
    public function getReactionCounts()
    {
        $reactions = $this->comment_reaction ?? [];
        $counts = [];
        
        foreach ($reactions as $userId => $data) {
            $reaction = $data['reaction'] ?? $data; // Handle both old and new format
            if (!isset($counts[$reaction])) {
                $counts[$reaction] = 0;
            }
            $counts[$reaction]++;
        }
        
        return $counts;
    }

    // Add or update user reaction with username
    public function addReaction($userId, $reaction, $username)
    {
        $reactions = $this->comment_reaction ?? [];
        $reactions[$userId] = [
            'reaction' => $reaction,
            'username' => $username
        ];
        $this->comment_reaction = $reactions;
        $this->save();
    }

    // Remove user reaction
    public function removeReaction($userId)
    {
        $reactions = $this->comment_reaction ?? [];
        unset($reactions[$userId]);
        $this->comment_reaction = $reactions;
        $this->save();
    }

    // Get user's current reaction
    public function getUserReaction($userId)
    {
        $reactions = $this->comment_reaction ?? [];
        return isset($reactions[$userId]) ? $reactions[$userId]['reaction'] : null;
    }

    // Get all reactions with usernames
    public function getReactionsWithUsers()
    {
        return $this->comment_reaction ?? [];
    }
}