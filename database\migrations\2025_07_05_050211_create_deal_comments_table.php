<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDealCommentsTable extends Migration
{
    public function up()
    {
        Schema::create('deal_comments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('deal_id');
            $table->unsignedBigInteger('user_id');
            $table->text('comment');
            $table->json('comment_reaction')->nullable();
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->integer('created_by');
            $table->timestamps();
            
            $table->foreign('deal_id')->references('id')->on('deals')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('parent_id')->references('id')->on('deal_comments')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('deal_comments');
    }
}