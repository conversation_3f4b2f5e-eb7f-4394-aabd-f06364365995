<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if custom_fields column exists in calendar_events, if not add it
        if (!Schema::hasColumn('calendar_events', 'custom_fields')) {
            Schema::table('calendar_events', function (Blueprint $table) {
                $table->json('custom_fields')->nullable()->after('date_override');
            });
        }

        // Check if custom_field_value column exists in calendar_events, if not add it
        if (!Schema::hasColumn('calendar_events', 'custom_field_value')) {
            Schema::table('calendar_events', function (Blueprint $table) {
                $table->json('custom_field_value')->nullable()->after('custom_fields');
            });
        }

        // Remove custom_fields and time columns from bookings table if they exist
        Schema::table('bookings', function (Blueprint $table) {
            if (Schema::hasColumn('bookings', 'custom_fields')) {
                $table->dropColumn('custom_fields');
            }
            if (Schema::hasColumn('bookings', 'time')) {
                $table->dropColumn('time');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove custom fields from calendar_events table
        Schema::table('calendar_events', function (Blueprint $table) {
            $table->dropColumn(['custom_fields', 'custom_field_value']);
        });

        // Add back custom_fields and time columns to bookings table
        Schema::table('bookings', function (Blueprint $table) {
            $table->json('custom_fields')->nullable();
            $table->time('time');
        });
    }
};
