<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SystemAdminCompany extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'email',
        'phone',
        'address',
        'logo',
        'is_active',
        'created_by'
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the system admin who created this company
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get all staff members (users) belonging to this company
     */
    public function staff()
    {
        return $this->hasMany(User::class, 'system_admin_company_id');
    }

    /**
     * Get all system admin staff (type = 'system admin') in this company
     */
    public function systemAdminStaff()
    {
        return $this->hasMany(User::class, 'system_admin_company_id')
                    ->where('type', 'system admin');
    }

    /**
     * Get all super admin staff in this company
     */
    public function superAdminStaff()
    {
        return $this->hasMany(User::class, 'system_admin_company_id')
                    ->where('type', 'super admin');
    }

    /**
     * Scope a query to only include active companies
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the company logo URL
     */
    public function getLogoUrlAttribute()
    {
        if ($this->logo) {
            return asset('storage/' . $this->logo);
        }
        return asset('assets/images/default-company-logo.png');
    }
}
