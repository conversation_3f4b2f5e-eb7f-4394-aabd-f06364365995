@extends('layouts.admin')
@php
    $profile = \App\Models\Utility::get_file('uploads/avatar');
@endphp
@section('page-title')
    @if (\Auth::user()->type == 'super admin')
        {{ __('Manage Clients') }}
    @elseif (\Auth::user()->type == 'system admin' || \Auth::user()->type == 'staff')
        {{ __('Manage White Label') }}
    @else
        {{ __('Manage User') }}
    @endif
@endsection
@section('breadcrumb')
    <li class="breadcrumb-item">
        <a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a>
    </li>
    @if (\Auth::user()->type == 'super admin')
        <li class="breadcrumb-item">{{ __('Companies') }}</li>
    @elseif (\Auth::user()->type == 'system admin' || \Auth::user()->type == 'staff')
        <li class="breadcrumb-item">{{ __('Super Admins') }}</li>
    @else
        <li class="breadcrumb-item">{{ __('User') }}</li>
    @endif
@endsection
@section('action-btn')
    <div class="float-end">
        @if (\Auth::user()->type == 'company' || \Auth::user()->type == 'HR')
        <a href="{{ route('user.userlog') }}"
            class="{{ Request::segment(1) == 'user' }}"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            title="{{ __('User Logs History') }}"
            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                    border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;margin-right:0.25rem;"
            onmouseover="this.style.transform='scale(1.1)'"
            onmouseout="this.style.transform='scale(1)'">
            <i class="ti ti-user-check" style="font-size:16px;"></i>
        </a>
        @endif
        @if ((\Auth::user()->type == 'system admin') || (\Auth::user()->type == 'staff' && \Auth::user()->can('create white label users')) || (\Auth::user()->type != 'system admin' && \Auth::user()->type != 'staff' && \Auth::user()->can('create user')))
            @if(\Auth::user()->type == 'company')
                <a href="{{ route('employees.create') }}"
                    class="btn btn-primary"
                    data-bs-toggle="tooltip"
                    data-title="{{ __('Create Employee') }}"
                    title="{{ __('Create Employee') }}"
                    style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                            border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                            box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;margin-right:0.25rem;"
                    onmouseover="this.style.transform='scale(1.1)'"
                    onmouseout="this.style.transform='scale(1)'">
                    <i class="ti ti-plus" style="font-size:16px;"></i>
                </a>
            @else
                <a href="#"
                    data-size="lg"
                    data-url="{{ route('users.create') }}"
                    data-ajax-popup="true"
                    data-bs-toggle="tooltip"
                    data-title="{{ \Auth::user()->type == 'super admin' ? __('Create Company') : ((\Auth::user()->type == 'system admin' || \Auth::user()->type == 'staff') ? __('Create Super Admin') : __('Create User')) }}"
                    title="{{ \Auth::user()->type == 'super admin' ? __('Create Company') : ((\Auth::user()->type == 'system admin' || \Auth::user()->type == 'staff') ? __('Create Super Admin') : __('Create User')) }}"
                    style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                            border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                            box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;margin-right:0.25rem;"
                    onmouseover="this.style.transform='scale(1.1)'"
                    onmouseout="this.style.transform='scale(1)'">
                    <i class="ti ti-plus" style="font-size:16px;"></i>
                </a>
            @endif
        @endif
    </div>
@endsection
@section('content')
<div class="table-responsive">
    <table class="table table-striped align-middle mb-0">
        <thead class="table-light">
            <tr>
                <th>{{ __('Name') }}</th>
                <th>{{ __('Email') }}</th>
                <th>{{ __('Type') }}</th>
                <th>{{ __('Last Login') }}</th>
                <th>{{ __('Status') }}</th>
                <th>{{ __('Actions') }}</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($users as $user)
                <tr>
                    <td class="fw-semibold">
                        <div class="d-flex align-items-center gap-2">
                            <img src="{{ !empty($user->avatar) ? Utility::get_file('uploads/avatar/') . $user->avatar : asset(Storage::url('uploads/avatar/avatar.png')) }}" alt="user-image" class="rounded-circle border border-primary" style="width:36px;height:36px;object-fit:cover;">
                            <span>{{ $user->name }}</span>
                        </div>
                    </td>
                    <td class="text-break">{{ $user->email }}</td>
                    <td>
                        @if (\Auth::user()->type == 'super admin')
                            <span class="badge bg-primary">{{ !empty($user->currentPlan) ? $user->currentPlan->name : '' }}</span>
                        @else
                            <span class="badge bg-primary">{{ ucfirst($user->type) }}</span>
                        @endif
                    </td>
                    <td>
                        @if($user->last_login_at)
                            {{ \Carbon\Carbon::parse($user->last_login_at)->format('M d, Y \a\t H:i') }}
                        @else
                            <span class="text-muted">{{ __('Never logged in') }}</span>
                        @endif
                    </td>
                    <td>
                        @if ($user->delete_status == 0)
                            <span class="badge bg-danger">{{ __('Soft Deleted') }}</span>
                        @else
                            <span class="badge bg-success">{{ __('Active') }}</span>
                        @endif
                    </td>
                    <td>
                        <div class="btn-group">
                            @if ((\Auth::user()->type == 'system admin') || (\Auth::user()->type == 'staff' && \Auth::user()->can('edit white label users')) || (\Auth::user()->type != 'system admin' && \Auth::user()->type != 'staff' && \Auth::user()->can('edit user')))
                            <a href="#"
                                data-size="lg"
                                data-url="{{ route('users.edit', $user->id) }}"
                                data-ajax-popup="true"
                                data-bs-toggle="tooltip"
                                title="{{ __('Edit') }}"
                                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
                                onmouseover="this.style.transform='scale(1.1)'"
                                onmouseout="this.style.transform='scale(1)'">
                                <i class="ti ti-pencil"></i>
                            </a>
                            @endif
                            @if ((\Auth::user()->type == 'system admin') || (\Auth::user()->type == 'staff' && \Auth::user()->can('delete white label users')) || (\Auth::user()->type != 'system admin' && \Auth::user()->type != 'staff' && \Auth::user()->can('delete user')))
                                {!! Form::open(['method' => 'DELETE', 'route' => ['users.destroy', $user['id']], 'id' => 'delete-form-' . $user['id'], 'style' => 'display:inline-block']) !!}
                                <button type="button"
                                    class="border-0 bs-pass-para-delete"
                                    data-bs-toggle="tooltip"
                                    title="{{ __('Delete') }}"
                                    data-confirm-text="{{ __('Are you sure you want to delete this user?') }}"
                                    style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;border-radius:50%;background:white;color:#dc2626;border:1.5px solid #dc2626;box-shadow:0 4px 6px rgba(0,0,0,0.05);transition:transform 0.2s ease-in-out; margin-left:5px;"
                                    onmouseover="this.style.transform='scale(1.1)'"
                                    onmouseout="this.style.transform='scale(1)'">
                                    <i class="ti ti-trash"></i>
                                </button>
                                {!! Form::close() !!}
                                @if ($user->delete_status != 0)
                                    {!! Form::open(['method' => 'DELETE', 'route' => ['users.force-destroy', $user['id']], 'id' => 'force-delete-form-' . $user['id'], 'style' => 'display:inline-block']) !!}
                                    <button type="button"
                                        class="bs-pass-para-force"
                                        data-bs-toggle="tooltip"
                                        title="{{ __('Force Delete') }}"
                                        data-confirm-text="This will force delete the user even if modules have dependency errors. Are you sure?"
                                        style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;border-radius:50%;background:white;color:#dc2626;border:1.5px solid #dc2626;box-shadow:0 4px 6px rgba(0,0,0,0.05);transition:transform 0.2s ease-in-out; margin-left:5px;"
                                        onmouseover="this.style.transform='scale(1.1)'"
                                        onmouseout="this.style.transform='scale(1)'">
                                        <i class="ti ti-alert-triangle"></i>
                                    </button>
                                    {!! Form::close() !!}
                                @endif
                            @endif
                            @if (Auth::user()->type == 'super admin' && $user->type == 'company')
                            <a href="{{ route('login.with.company', $user->id) }}"
                                data-bs-toggle="tooltip"
                                title="{{ __('Login') }}"
                                style="display:inline-flex;align-items:center;gap:8px;padding:0 16px;height:40px;border-radius:999px;
                                        background:linear-gradient(to right, #065f46, #0f766e);color:white;font-weight:500;
                                        box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out; margin-left:5px;"
                                onmouseover="this.style.transform='scale(1.05)'"
                                onmouseout="this.style.transform='scale(1)'">
                                <i class="ti ti-replace" style="font-size:16px;"></i>
                                <span>{{ __('Login') }}</span>
                            </a>
                            @endif
                            @if ((Auth::user()->type == 'system admin' || (Auth::user()->type == 'staff' && Auth::user()->can('manage white label users'))) && $user->type == 'super admin')
                            <a href="{{ route('login.with.company', $user->id) }}"
                                data-bs-toggle="tooltip"
                                title="{{ __('Login') }}"
                                style="display:inline-flex;align-items:center;gap:8px;padding:0 16px;height:40px;border-radius:999px;background:linear-gradient(to right, #065f46, #0f766e);color:white;font-weight:500;box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out; margin-left:5px;"
                                onmouseover="this.style.transform='scale(1.05)'"
                                onmouseout="this.style.transform='scale(1)'">
                                <i class="ti ti-replace" style="font-size:16px;"></i>
                                <span>{{ __('Login') }}</span>
                            </a>
                            @endif
                            @if ($user->is_enable_login == 1)
                            <a href="{{ route('users.login', \Crypt::encrypt($user->id)) }}"
                                title="{{ __('Login Disable') }}"
                                data-bs-toggle="tooltip"
                                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;border-radius:50%;background:white;color:#eab308;border:1.5px solid #eab308;box-shadow:0 4px 6px rgba(0,0,0,0.05);transition:transform 0.2s ease-in-out; margin-left:5px;"
                                onmouseover="this.style.transform='scale(1.1)'"
                                onmouseout="this.style.transform='scale(1)'">
                                <i class="ti ti-road-sign"></i>
                            </a>
                            @elseif ($user->is_enable_login == 0 && $user->password == null)
                            <a href="#"
                                data-url="{{ route('users.reset', \Crypt::encrypt($user->id)) }}"
                                data-ajax-popup="true"
                                data-size="md"
                                data-bs-toggle="tooltip"
                                title="{{ __('New Password') }}"
                                class="login_enable"
                                style="display:inline-flex;align-items:center;gap:8px;padding:0 16px;height:40px;border-radius:999px;
                                        background:linear-gradient(to right, #065f46, #0f766e);color:white;font-weight:500;
                                        box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out; margin-left:5px;"
                                onmouseover="this.style.transform='scale(1.05)'"
                                onmouseout="this.style.transform='scale(1)'">
                                <i class="ti ti-road-sign" style="font-size:16px;"></i>
                                <span>{{ __('Reset') }}</span>
                            </a>
                            @else
                            <a href="{{ route('users.login', \Crypt::encrypt($user->id)) }}"
                                data-bs-toggle="tooltip"
                                title="{{ __('Login Enable') }}"
                                style="display:inline-flex;align-items:center;gap:8px;padding:0 16px;height:40px;border-radius:999px;
                                        background:linear-gradient(to right, #065f46, #0f766e);color:white;font-weight:500;
                                        box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out; margin-left:5px;"
                                onmouseover="this.style.transform='scale(1.05)'"
                                onmouseout="this.style.transform='scale(1)'">
                                <i class="ti ti-road-sign" style="font-size:16px;"></i>
                            </a>
                            @endif
                        </div>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
</div>

<!-- Force Delete Confirmation Modal -->
<div class="modal fade" id="forceDeleteModal" tabindex="-1" aria-labelledby="forceDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="forceDeleteModalLabel">
                    <i class="ti ti-alert-triangle text-danger me-2"></i>
                    {{ __('Force Delete Confirmation') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger d-flex align-items-center mb-3">
                    <i class="ti ti-alert-circle me-2"></i>
                    <div>
                        <strong>{{ __('Warning!') }}</strong> {{ __('This action cannot be undone.') }}
                    </div>
                </div>
                
                <p id="forceDeleteConfirmText" class="mb-3">
                    {{ __('This will force delete the user even if modules have dependency errors. Are you sure?') }}
                </p>
                
                <div class="mb-3">
                    <label for="forceDeleteInput" class="form-label">
                        {{ __('Type') }} <strong class="text-danger">DELETE</strong> {{ __('to confirm:') }}
                    </label>
                    <input type="text" 
                           class="form-control" 
                           id="forceDeleteInput" 
                           placeholder="{{ __('Type DELETE to confirm') }}"
                           autocomplete="off">
                    <div id="forceDeleteError" class="invalid-feedback" style="display: none;">
                        {{ __('Please type DELETE exactly as shown above.') }}
                    </div>
                </div>
                
                <div class="text-muted small">
                    <i class="ti ti-info-circle me-1"></i>
                    {{ __('This confirmation is required for security purposes.') }}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ __('Cancel') }}
                </button>
                <button type="button" class="btn btn-danger" id="forceDeleteConfirmBtn" disabled>
                    <i class="ti ti-trash me-1"></i>
                    {{ __('Force Delete') }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Standard Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="ti ti-alert-triangle text-danger me-2"></i>
                    {{ __('Delete Confirmation') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger d-flex align-items-center mb-3">
                    <i class="ti ti-alert-circle me-2"></i>
                    <div>
                        <strong>{{ __('Warning!') }}</strong> {{ __('This action cannot be undone.') }}
                    </div>
                </div>
                <p id="deleteConfirmText" class="mb-3">
                    {{ __('Are you sure you want to delete this user?') }}
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ __('Cancel') }}
                </button>
                <button type="button" class="btn btn-danger" id="deleteConfirmBtn">
                    <i class="ti ti-trash me-1"></i>
                    {{ __('Delete') }}
                </button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('script-page')
    <script>
        $(document).on('change', '#password_switch', function() {
            if ($(this).is(':checked')) {
                $('.ps_div').removeClass('d-none');
                $('#password').attr("required", true);

            } else {
                $('.ps_div').addClass('d-none');
                $('#password').val(null);
                $('#password').removeAttr("required");
            }
        });
        $(document).on('click', '.login_enable', function() {
            setTimeout(function() {
                $('.modal-body').append($('<input>', {
                    type: 'hidden',
                    val: 'true',
                    name: 'login_enable'
                }));
            }, 2000);
        });

        
        // Force delete modal functionality (inline fallback)
        $(document).on('click', '.bs-pass-para-force', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            console.log('Force delete clicked (jQuery)');
            
            var el = $(this);
            var form = el.closest('form');
            var confirmText = el.data('confirm-text') || 'This will force delete the user even if modules have dependency errors. Are you sure?';
            
            // Set the confirmation text in the modal
            $('#forceDeleteConfirmText').text(confirmText);
            
            // Clear any previous input and error states
            var input = $('#forceDeleteInput');
            var errorMsg = $('#forceDeleteError');
            var confirmBtn = $('#forceDeleteConfirmBtn');
            
            input.val('');
            input.removeClass('is-invalid is-valid');
            errorMsg.hide();
            confirmBtn.prop('disabled', true);
            
            // Store the form reference for later submission
            $('#forceDeleteModal').attr('data-form-id', form.attr('id'));
            
            // Show the modal
            $('#forceDeleteModal').modal('show');
            
            // Focus on input when modal is shown
            $('#forceDeleteModal').on('shown.bs.modal', function() {
                input.focus();
            });
        });
        
        // Handle input validation
        $(document).on('input', '#forceDeleteInput', function() {
            var input = $(this);
            var confirmBtn = $('#forceDeleteConfirmBtn');
            var errorMsg = $('#forceDeleteError');
            
            if (input.val() === 'DELETE') {
                input.removeClass('is-invalid').addClass('is-valid');
                errorMsg.hide();
                confirmBtn.prop('disabled', false);
            } else {
                input.removeClass('is-valid');
                if (input.val().length > 0) {
                    input.addClass('is-invalid');
                    errorMsg.show();
                } else {
                    input.removeClass('is-invalid');
                    errorMsg.hide();
                }
                confirmBtn.prop('disabled', true);
            }
        });
        
        // Handle confirmation button click
        $(document).on('click', '#forceDeleteConfirmBtn', function() {
            var input = $('#forceDeleteInput');
            if (input.val() === 'DELETE') {
                var modal = $('#forceDeleteModal');
                var formId = modal.attr('data-form-id');
                var form = $('#' + formId);
                
                console.log('Submitting form:', formId, form);
                
                modal.modal('hide');
                if (form.length) {
                    form.submit();
                } else {
                    console.error('Form not found:', formId);
                }
            }
        });

        // Standard delete modal functionality
        $(document).on('click', '.bs-pass-para-delete', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var el = $(this);
            var form = el.closest('form');
            var confirmText = el.data('confirm-text') || 'Are you sure you want to delete this user?';
            // Set the confirmation text in the modal
            $('#deleteConfirmText').text(confirmText);
            // Store the form reference for later submission
            $('#deleteModal').attr('data-form-id', form.attr('id'));
            // Show the modal
            $('#deleteModal').modal('show');
        });
        // Handle confirmation button click for standard delete
        $(document).on('click', '#deleteConfirmBtn', function() {
            var modal = $('#deleteModal');
            var formId = modal.attr('data-form-id');
            var form = $('#' + formId);
            modal.modal('hide');
            if (form.length) {
                form.submit();
            } else {
                console.error('Form not found:', formId);
            }
        });
    </script>
    <script src="{{ asset('js/user-delete-confirmation.js') }}"></script>
@endpush
