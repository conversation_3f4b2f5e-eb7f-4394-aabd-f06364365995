<?php

require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Debugging company dashboard login issue...\n";
echo "==========================================\n";

try {
    // Test company user
    $companyUser = \App\Models\User::where('email', '<EMAIL>')->first();
    
    if (!$companyUser) {
        echo "❌ Company user not found\n";
        exit;
    }
    
    echo "✅ Testing company user: {$companyUser->name}\n";
    echo "   Type: {$companyUser->type}\n";
    echo "   Active: " . ($companyUser->is_active ? "YES" : "NO") . "\n";
    echo "   Login Enabled: " . ($companyUser->is_enable_login ? "YES" : "NO") . "\n";
    echo "   Delete Status: {$companyUser->delete_status}\n";
    
    // Check permissions
    echo "\n✅ Checking dashboard permissions:\n";
    
    $dashboardPermissions = [
        'show account dashboard',
        'show project dashboard', 
        'show hrm dashboard',
        'show crm dashboard',
        'show pos dashboard'
    ];
    
    foreach ($dashboardPermissions as $permission) {
        $hasPermission = $companyUser->can($permission);
        echo "   - {$permission}: " . ($hasPermission ? "✅ YES" : "❌ NO") . "\n";
    }
    
    // Check what the dashboard controller logic would do
    echo "\n✅ Dashboard Controller Logic Test:\n";
    
    if ($companyUser->type != 'client' && $companyUser->type != 'company') {
        echo "   ❌ User would be excluded from main dashboard (type check failed)\n";
    } else {
        echo "   ✅ User passes type check\n";
        
        if ($companyUser->can('show account dashboard')) {
            echo "   ✅ User can access account dashboard\n";
        } elseif ($companyUser->can('show project dashboard')) {
            echo "   ✅ User can access project dashboard\n";
        } elseif ($companyUser->can('show hrm dashboard')) {
            echo "   ✅ User can access hrm dashboard\n";
        } elseif ($companyUser->can('show crm dashboard')) {
            echo "   ✅ User can access crm dashboard\n";
        } elseif ($companyUser->can('show pos dashboard')) {
            echo "   ✅ User can access pos dashboard\n";
        } else {
            echo "   ❌ User has no dashboard permissions - this is the problem!\n";
        }
    }
    
    // Check what data would be loaded for dashboard
    echo "\n✅ Testing dashboard data loading:\n";
    
    try {
        // Test if we can load the data that dashboard needs
        $announcements = \App\Models\Announcement::where('created_by', $companyUser->creatorId())->limit(5)->get();
        echo "   ✅ Announcements: {$announcements->count()}\n";
        
        $meetings = \App\Models\Meeting::where('created_by', $companyUser->creatorId())->limit(5)->get();
        echo "   ✅ Meetings: {$meetings->count()}\n";
        
        // Check if Employee model exists and can be queried
        if (class_exists('\App\Models\Employee')) {
            $employees = \App\Models\Employee::where('created_by', $companyUser->creatorId())->get();
            echo "   ✅ Employees: {$employees->count()}\n";
        } else {
            echo "   ⚠️  Employee model not found\n";
        }
        
    } catch (\Exception $e) {
        echo "   ❌ Error loading dashboard data: {$e->getMessage()}\n";
    }
    
    // Check session and authentication flow
    echo "\n✅ Authentication Flow Check:\n";
    
    // Check RouteServiceProvider HOME constant
    $homeRoute = \App\Providers\RouteServiceProvider::HOME;
    echo "   Default HOME route: {$homeRoute}\n";
    
    // Check what route company users should be redirected to
    echo "   Company users should redirect to: /account-dashboard (based on AuthenticatedSessionController)\n";
    
    // Check if the route exists
    try {
        $routeExists = \Route::has('dashboard');
        echo "   Dashboard route exists: " . ($routeExists ? "YES" : "NO") . "\n";
        
        if ($routeExists) {
            $route = \Route::getRoutes()->getByName('dashboard');
            echo "   Dashboard route URI: " . $route->uri() . "\n";
            echo "   Dashboard route action: " . $route->getActionName() . "\n";
        }
    } catch (\Exception $e) {
        echo "   Error checking routes: {$e->getMessage()}\n";
    }
    
    // Suggest fixes
    echo "\n🔧 Suggested Fixes:\n";
    
    if (!$companyUser->can('show account dashboard')) {
        echo "   1. Add 'show account dashboard' permission to company role\n";
    }
    
    if ($companyUser->type == 'company') {
        echo "   2. Fix dashboard controller logic to properly handle company users\n";
    }
    
    echo "   3. Check if there are any session/cache issues causing first login to fail\n";
    echo "   4. Verify that company users have proper role assignments\n";
    
} catch (\Exception $e) {
    echo "❌ Error: {$e->getMessage()}\n";
    echo "Stack trace: {$e->getTraceAsString()}\n";
}

echo "\nDone!\n";
