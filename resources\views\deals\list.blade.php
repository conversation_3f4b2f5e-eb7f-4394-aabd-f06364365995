@extends('layouts.admin')
@section('page-title')
    {{__('Manage Deals')}} @if($pipeline) - {{$pipeline->name}} @endif
@endsection

@push('css-page')
    <link rel="stylesheet" href="{{asset('css/summernote/summernote-bs4.css')}}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .communication-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
        }

        .communication-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            text-decoration: none;
        }

        .communication-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            color: #fff;
        }

        .communication-btn.call { background-color: #28a745; }
        .communication-btn.sms { background-color: #17a2b8; }
        .communication-btn.email { background-color: #6f42c1; }
        .communication-btn.source { background-color: #fd7e14; }

        .communication-btn i {
            font-size: 14px;
        }

        .list-group-item {
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .list-group-item:hover {
            background-color: #f8f9fa;
        }

        .list-group-item.text-muted {
            cursor: not-allowed;
        }

        .list-group-item.text-muted:hover {
            background-color: transparent;
        }
    </style>
@endpush

@push('script-page')
    <script src="{{asset('css/summernote/summernote-bs4.js')}}"></script>
    <script>
        $(document).on("change", ".change-pipeline select[name=default_pipeline_id]", function () {
            $('#change-pipeline').submit();
        });

        function openSourcesModal(sources) {
            const sourcesList = document.getElementById('sources-list');
            sourcesList.innerHTML = ''; // Clear previous sources

            if (sources && sources.length > 0) {
                sources.forEach(source => {
                    const listItem = document.createElement('li');
                    listItem.className = 'list-group-item';
                    listItem.textContent = source.name;
                    sourcesList.appendChild(listItem);
                });
            } else {
                const listItem = document.createElement('li');
                listItem.className = 'list-group-item';
                listItem.textContent = '{{ __('No sources found for this deal.') }}';
                sourcesList.appendChild(listItem);
            }

            const modal = new bootstrap.Modal(document.getElementById('sources-modal'));
            modal.show();
        }

        $(document).on('click', '.email-popup, .sms-popup', function(e) {
            e.preventDefault();
            var modal = $('#communication-modal');
            var title = modal.find('.modal-title');
            var body = modal.find('.modal-body .list-group');
            body.empty();

            var clientPhone = $(this).data('client-phone');
            var clientEmail = $(this).data('client-email');
            var whatsappPhone = String(clientPhone).replace(/[^\d]/g, '');

            if ($(this).hasClass('sms-popup')) {
                title.text('{{ __('Send Message') }}');
            } else {
                title.text('{{ __('Send Email') }}');
            }

            if(clientPhone || clientEmail) {
                if(clientPhone) {
                    body.append('<li class="list-group-item" style="cursor: pointer;" onclick="window.open(\'https://wa.me/' + whatsappPhone + '\', \'_blank\')"><span class="d-flex align-items-center"><i class="fab fa-whatsapp me-2" style="color: #25D366;"></i> {{ __('WhatsApp') }}</span></li>');
                    body.append('<li class="list-group-item" style="cursor: pointer;" onclick="window.open(\'sms:' + clientPhone + '\')"><span class="d-flex align-items-center"><i class="ti ti-message-circle me-2" style="color: #007bff;"></i> {{ __('Default SMS App') }}</span></li>');
                } else {
                     body.append('<li class="list-group-item text-muted d-flex align-items-center" style="cursor: not-allowed;"><i class="fab fa-whatsapp me-2" style="color: #25D366;"></i> {{ __('WhatsApp (No phone number)') }}</li>');
                     body.append('<li class="list-group-item text-muted d-flex align-items-center" style="cursor: not-allowed;"><i class="ti ti-message-circle me-2" style="color: #007bff;"></i> {{ __('Default SMS App (No phone number)') }}</li>');
                }
                if(clientEmail) {
                    body.append('<li class="list-group-item" style="cursor: pointer;" onclick="window.open(\'https://mail.google.com/\', \'_blank\')"><span class="d-flex align-items-center"><i class="ti ti-mail me-2" style="color: #46a5e0;"></i> {{ __('Gmail') }}</span></li>');
                } else {
                    body.append('<li class="list-group-item text-muted d-flex align-items-center" style="cursor: not-allowed;"><i class="ti ti-mail me-2" style="color: #46a5e0;"></i> {{ __('Gmail (No email)') }}</li>');
                }

                body.append('<li class="list-group-item"><a href="#" class="d-flex align-items-center"><i class="ti ti-cloud me-2"></i> {{ __('Cloud Email Service') }}</a></li>');

            } else {
                body.append('<li class="list-group-item text-center">{{ __('No contact information available.') }}</li>');
            }

            modal.modal('show');
        });
    </script>
@endpush

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item">{{__('Deal')}}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        <a href="{{ route('deals.index') }}" data-bs-toggle="tooltip" title="{{__('Kanban View')}}" class="btn btn-sm bg-light-blue-subtitle me-1">
            <i class="ti ti-layout-grid"></i>
        </a>
        <a href="#" data-size="md"  data-bs-toggle="tooltip" title="{{__('Import')}}" data-url="{{ route('deals.import') }}" data-ajax-popup="true" data-title="{{__('Import Deal CSV file')}}" class="btn btn-sm bg-brown-subtitle me-1">
            <i class="ti ti-file-import"></i>
        </a>
        <a href="{{route('deals.export')}}" data-bs-toggle="tooltip" title="{{__('Export')}}" class="btn btn-sm btn-secondary me-1">
            <i class="ti ti-file-export"></i>
        </a>
        <a href="#" data-size="lg" data-url="{{ route('deals.create') }}" data-ajax-popup="true" data-bs-toggle="tooltip" title="{{__('Create New Deal')}}" data-title="{{__('Create Deal')}}" class="btn btn-sm btn-primary me-1">
            <i class="ti ti-plus"></i>
        </a>
    </div>
@endsection

@section('content')
    @if($pipeline)
        <div class="row">
            <div class="col-xl-12">
                <div class="card">
                    <div class="card-body table-border-style">
                        <div class="table-responsive">
                            <table class="table datatable">
                                <thead>
                                <tr>
                                    <th>{{__('Name')}}</th>
                                    <th>{{__('Price')}}</th>
                                    <th>{{__('Stage')}}</th>
                                    <th>{{__('Users')}}</th>
                                    <th>{{__('Contact')}}</th>
                                    <th>{{__('Action')}}</th>
                                </tr>
                                </thead>
                                <tbody>
                                @if(count($deals) > 0)
                                    @foreach ($deals as $deal)
                                        <tr>
                                            <td>{{ $deal->name }}</td>
                                            <td>{{ \Auth::user()->priceFormat($deal->price) }}</td>
                                            <td>{{  !empty($deal->stage)?$deal->stage->name:'-' }}</td>
                                            <td>
                                                @foreach($deal->users as $user)
                                                    <a href="#" class="btn btn-sm mr-1 p-0 rounded-circle">
                                                        <img alt="image" data-toggle="tooltip" data-original-title="{{$user->name}}" @if($user->avatar) src="{{asset('/storage/uploads/avatar/'.$user->avatar)}}" @else src="{{asset('/storage/uploads/avatar/avatar.png')}}" @endif class="rounded-circle " width="25" height="25">
                                                    </a>
                                                @endforeach
                                            </td>
                                            <td>
                                                <?php
                                                $sources = $deal->sources();
                                                ?>
                                                <div class="communication-buttons">
                                                    <a href="tel:{{ $deal->phone ?? '' }}"
                                                    class="btn btn-sm d-flex align-items-center justify-content-center"
                                                    style="
                                                        background: linear-gradient(135deg, #198754, #157347);
                                                        border: none;
                                                        border-radius: 8px;
                                                        padding: 8px 12px;
                                                        transition: all 0.3s ease;
                                                        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                                    "
                                                    onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                    onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0,0,0,0.1)'"
                                                    data-bs-toggle="tooltip"
                                                    title="Call">
                                                        <i class="ti ti-phone-call text-white" style="font-size: 16px;"></i>
                                                    </a>

                                                    {{-- SMS Button --}}
                                                    <button class="btn btn-sm d-flex align-items-center justify-content-center sms-popup"
                                                            style="
                                                                background: linear-gradient(135deg, #fd7e14, #e8590c);
                                                                border: none;
                                                                border-radius: 8px;
                                                                padding: 8px 12px;
                                                                transition: all 0.3s ease;
                                                                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                                            "
                                                            onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                            onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0,0,0,0.1)'"
                                                            data-bs-toggle="tooltip"
                                                            title="SMS"
                                                            data-client-phone="{{ $deal->phone ?? '' }}"
                                                            data-client-email="{{ $deal->email ?? '' }}">
                                                        <i class="ti ti-message-circle text-white" style="font-size: 16px;"></i>
                                                    </button>

                                                    {{-- Email Button --}}
                                                    <button class="btn btn-sm d-flex align-items-center justify-content-center email-popup"
                                                            style="
                                                                background: linear-gradient(135deg, #0d6efd, #0b5ed7);
                                                                border: none;
                                                                border-radius: 8px;
                                                                padding: 8px 12px;
                                                                transition: all 0.3s ease;
                                                                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                                            "
                                                            onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                            onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0,0,0,0.1)'"
                                                            data-bs-toggle="tooltip"
                                                            title="Email"
                                                            data-client-phone="{{ $deal->phone ?? '' }}"
                                                            data-client-email="{{ $deal->email ?? '' }}">
                                                        <i class="ti ti-mail text-white" style="font-size: 16px;"></i>
                                                    </button>

                                                    {{-- Sources Button --}}
                                                    <button class="btn btn-sm d-flex align-items-center justify-content-center"
                                                            style="
                                                                background: linear-gradient(135deg, #6f42c1, #5f3dc4);
                                                                border: none;
                                                                border-radius: 8px;
                                                                padding: 8px 12px;
                                                                transition: all 0.3s ease;
                                                                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                                            "
                                                            onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                            onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0,0,0,0.1)'"
                                                            data-bs-toggle="tooltip"
                                                            title="Sources ({{ count($sources) }})"
                                                            onclick='openSourcesModal(@json($sources))'>
                                                        <i class="ti ti-social text-white" style="font-size: 16px;"></i>
                                                    </button>

                                                </div>
                                            </td>
                                            @if(Auth::user()->type != 'client')
                                                <td class="Action">
                                                    <span>
                                                    @can('view deal')
                                                            @if($deal->is_active)
                                                            <div class="action-btn me-2">
                                                                <a href="{{ route('deals.show', $deal->id) }}"
                                                                class="mx-3 btn btn-sm d-flex align-items-center justify-content-center"
                                                                style="
                                                                    background: linear-gradient(135deg, #14532d, #065f46);
                                                                    border: none;
                                                                    border-radius: 8px;
                                                                    padding: 8px 12px;
                                                                    transition: all 0.3s ease;
                                                                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                                                "
                                                                onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                                onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0,0,0,0.1)'"
                                                                data-size="xl"
                                                                data-bs-toggle="tooltip"
                                                                title="{{ __('View') }}"
                                                                data-title="{{ __('Deal Detail') }}">
                                                                    <i class="ti ti-eye text-white" style="font-size: 16px;"></i>
                                                                </a>
                                                            </div>

                                                            @endif
                                                        @endcan
                                                        @can('edit deal')
                                                        <div class="action-btn me-2" style="margin-left: 4px;">
                                                            <a href="#"
                                                            class="mx-3 btn btn-sm d-flex align-items-center justify-content-center"
                                                            style="
                                                                background: linear-gradient(135deg, #14532d, #065f46);
                                                                border: none;
                                                                border-radius: 8px;
                                                                padding: 8px 12px;
                                                                transition: all 0.3s ease;
                                                                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                                            "
                                                            onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                            onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0,0,0,0.1)'"
                                                            data-url="{{ route('deals.edit', $deal->id) }}"
                                                            data-ajax-popup="true"
                                                            data-size="xl"
                                                            data-bs-toggle="tooltip"
                                                            title="{{ __('Edit') }}"
                                                            data-title="{{ __('Deal Edit') }}">
                                                                <i class="ti ti-pencil text-white" style="font-size: 16px;"></i>
                                                            </a>
                                                        </div>

                                                        @endcan
                                                        @can('delete deal')
                                                        <div class="action-btn" style="margin-left: 4px;">
                                                            {!! Form::open(['method' => 'DELETE', 'route' => ['deals.destroy', $deal->id], 'id' => 'delete-form-' . $deal->id]) !!}
                                                                <a href="#"
                                                                onclick="event.preventDefault(); document.getElementById('delete-form-{{ $deal->id }}').submit();"
                                                                class="mx-3 btn btn-sm d-flex align-items-center justify-content-center"
                                                                style="
                                                                    background: linear-gradient(135deg, #dc3545, #b02a37);
                                                                    border: none;
                                                                    border-radius: 8px;
                                                                    padding: 8px 12px;
                                                                    transition: all 0.3s ease;
                                                                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                                                "
                                                                onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                                onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0,0,0,0.1)'"
                                                                data-bs-toggle="tooltip"
                                                                title="{{ __('Delete') }}">
                                                                    <i class="ti ti-trash text-white" style="font-size: 16px;"></i>
                                                                </a>
                                                            {!! Form::close() !!}
                                                        </div>

                                                        @endcan
                                                    </span>
                                                </td>
                                            @endif
                                        </tr>
                                    @endforeach
                                @else
                                    <tr class="font-style">
                                        <td colspan="6" class="text-center">{{ __('No data available in table') }}</td>
                                    </tr>
                                @endif

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Communication Modal -->
    <div class="modal fade" id="communication-modal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="list-group list-group-flush">
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Sources Modal -->
    <div class="modal fade" id="sources-modal" tabindex="-1" aria-labelledby="sources-modal-label" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="sources-modal-label">{{ __('Deal Sources') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="list-group" id="sources-list">
                        <!-- Sources will be dynamically inserted here -->
                    </ul>
                </div>
            </div>
        </div>
    </div>

@endsection