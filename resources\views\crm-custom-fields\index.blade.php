@extends('layouts.admin')

@section('page-title')
    {{ __('CRM System Setup') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('CRM System Setup') }}</li>
@endsection

@section('content')
    @include('layouts.crm_horizontal_menu')
    <div class="card">
        <div class="card-header d-flex justify-content-between">
            <h5>{{ __('Custom Fields') }}</h5>
            <a href="#" class="btn btn-sm btn-primary"
               data-url="{{ route('crmCustomField.create') }}"
               data-ajax-popup="true"
               data-title="{{ __('Create Custom Field') }}">
                <i class="ti ti-plus text-white"></i>
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table datatable">
                    <thead>
                        <tr>
                            <th>{{ __('Name') }}</th>
                            <th>{{ __('Type') }}</th>
                            <th>{{ __('Module') }}</th>
                            <th>{{ __('Unique Key') }}</th>
                            <th width="10%">{{ __('Action') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($custom_fields as $field)
                            @if($field->module === 'Leads')
                                <tr>
                                    <td>{{ $field->name }}</td>
                                    <td>{{ ucfirst($field->type) }}</td>
                                    <td>{{ $field->module }}</td>
                                    <td>{{ $field->unique_key }}</td>
                                    <td class="Action">
                                        <span class="d-flex align-items-center">
                                            {{-- Edit --}}
                                            <div class="action-btn me-2">
                                                <a href="#"
                                                   class="btn btn-sm bg-info me-1"
                                                   data-url="{{ route('custom-field.edit', $field->id) }}"
                                                   data-ajax-popup="true"
                                                   data-title="{{ __('Edit Custom Field') }}"
                                                   data-bs-toggle="tooltip"
                                                   title="{{ __('Edit') }}">
                                                    <i class="ti ti-pencil text-white"></i>
                                                </a>
                                            </div>
                                            {{-- Delete --}}
                                            <div class="action-btn">
                                                {!! Form::open(['method' => 'DELETE', 'route' => ['crmCustomField.destroy', $field->id], 'id' => 'delete-form-'.$field->id, 'style' => 'display: inline;']) !!}
                                                <a href="#"
                                                   class="btn btn-sm bg-danger"
                                                   data-bs-toggle="tooltip"
                                                   title="{{ __('Delete') }}"
                                                   data-confirm="{{ __('Are You Sure?').'|'.__('This action can not be undone. Do you want to continue?') }}"
                                                   data-confirm-yes="document.getElementById('delete-form-{{$field->id}}').submit();">
                                                    <i class="ti ti-trash text-white"></i>
                                                </a>
                                                {!! Form::close() !!}
                                            </div>
                                        </span>
                                    </td>
                                </tr>
                            @endif
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    document.addEventListener("DOMContentLoaded", function () {
        const nameInput = document.getElementById('field_name');
        const moduleSelect = document.getElementById('module');
        const uniqueKeyInput = document.getElementById('unique_key');

        function slugify(text) {
            return text
                .toString()
                .toLowerCase()
                .trim()
                .replace(/\s+/g, '_')
                .replace(/[^\w\-]+/g, '')
                .replace(/\_\_+/g, '_')
                .replace(/^_+/, '')
                .replace(/_+$/, '');
        }

        function generateUniqueKey() {
            const name = slugify(nameInput.value || '');
            const module = slugify(moduleSelect.value || '');
            if (name && module) {
                uniqueKeyInput.value = `{{'${module}.${name}'}}`;
            } else {
                uniqueKeyInput.value = '';
            }
        }

        if (nameInput && moduleSelect && uniqueKeyInput) {
            nameInput.addEventListener('input', generateUniqueKey);
            moduleSelect.addEventListener('change', generateUniqueKey);
            generateUniqueKey();
        }
    });
</script>
@endpush
