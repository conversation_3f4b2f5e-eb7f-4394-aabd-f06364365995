{{-- resources/views/crm-custom-fields/edit.blade.php --}}
{{ Form::model($crmCustomField, ['route' => ['crmCustomField.update', $crmCustomField->id], 'method' => 'PUT', 'class' => 'needs-validation', 'novalidate']) }}
<div class="modal-body">
    <div class="row">
        <div class="form-group col-md-12">
            {{ Form::label('name', __('Custom Field Name'), ['class' => 'form-label']) }}<x-required></x-required>
            {{ Form::text('name', null, ['class' => 'form-control', 'required' => 'required']) }}
        </div>
        <div class="form-group col-md-12">
            {{ Form::label('type', __('Type'), ['class' => 'form-label']) }}<x-required></x-required>
            {{ Form::select('type', $types, null, ['class' => 'form-control select', 'required' => 'required', 'id' => 'type']) }}
        </div>

        {{-- Dynamic Options Input --}}
        <div class="form-group col-md-12" id="options-container" style="display:none;">
            {{ Form::label('options[]', __('Options (for checkbox/radio/select/multiselect)'), ['class' => 'form-label']) }}
            <div id="options-list">
                @if($crmCustomField->options && count($crmCustomField->options) > 0)
                    @foreach($crmCustomField->options as $option)
                        <div class="d-flex mb-2">
                            {{ Form::text('options[]', $option, ['class' => 'form-control me-2', 'placeholder' => 'Option']) }}
                            <button type="button" class="btn btn-sm btn-danger remove-option">-</button>
                        </div>
                    @endforeach
                @else
                    <div class="d-flex mb-2">
                        {{ Form::text('options[]', null, ['class' => 'form-control me-2', 'placeholder' => 'Option 1']) }}
                        <button type="button" class="btn btn-sm btn-success add-option">+</button>
                    </div>
                @endif
            </div>
            @if($crmCustomField->options && count($crmCustomField->options) > 0)
                <button type="button" class="btn btn-sm btn-success add-option mt-2">Add Option</button>
            @endif
        </div>

        <div class="form-group col-md-12">
            {{ Form::label('module', __('Module'), ['class' => 'form-label']) }}<x-required></x-required>
            {{ Form::select('module', $modules, null, ['class' => 'form-control select', 'required' => 'required']) }}
        </div>
    </div>
</div>
<div class="modal-footer">
    <input type="button" value="{{ __('Cancel') }}" class="btn btn-secondary" data-bs-dismiss="modal">
    <input type="submit" value="{{ __('Update') }}" class="btn btn-primary">
</div>
{{ Form::close() }}

<script>
// Use event delegation to handle dynamically loaded content - but only for CRM custom fields
$(document).on('change', '#commonModal select[name="type"]', function() {
    // Only proceed if we're in the CRM custom fields form (check for options-container)
    if ($('#commonModal #options-container').length === 0) {
        return; // Not the CRM custom fields form, exit early
    }

    console.log('CRM Custom Fields - Type select changed:', $(this).val());
    const selected = $(this).val();
    const optionsContainer = $('#commonModal #options-container');

    if (selected === 'checkbox' || selected === 'radio' || selected === 'select' || selected === 'multiselect') {
        optionsContainer.show();
        console.log('Showing options container');
    } else {
        optionsContainer.hide();
        console.log('Hiding options container');
    }
});

// Handle add/remove option buttons with event delegation
$(document).on('click', '#commonModal .add-option', function(e) {
    e.preventDefault();
    console.log('Add option clicked');
    const optionsList = $('#commonModal #options-list');
    const newOption = `
        <div class="d-flex mb-2">
            <input type="text" name="options[]" class="form-control me-2" placeholder="Option">
            <button type="button" class="btn btn-sm btn-danger remove-option">-</button>
        </div>
    `;
    optionsList.append(newOption);
});

$(document).on('click', '#commonModal .remove-option', function(e) {
    e.preventDefault();
    console.log('Remove option clicked');
    $(this).closest('.d-flex').remove();
});

// Initialize when modal is shown - but only for CRM custom fields
$(document).on('shown.bs.modal', '#commonModal', function() {
    // Only proceed if we're in the CRM custom fields form (check for options-container)
    if ($('#commonModal #options-container').length === 0) {
        return; // Not the CRM custom fields form, exit early
    }

    console.log('CRM Custom Fields modal shown, initializing form');
    const typeSelect = $('#commonModal select[name="type"]');
    if (typeSelect.length) {
        // Trigger initial state
        typeSelect.trigger('change');
    }
});
</script>
