@extends('layouts.admin')
@section('page-title')
    {{__('Manage Deal Stages')}}
@endsection
@push('script-page')
    <script src="{{ asset('js/jquery-ui.min.js') }}"></script>
    <script>
        $(function () {
            $(".sortable").sortable();
            $(".sortable").disableSelection();
            $(".sortable").sortable({
                stop: function () {
                    var order = [];
                    $(this).find('li').each(function (index, data) {
                        order[index] = $(data).attr('data-id');
                    });
                    $.ajax({
                        url: "{{route('stages.order')}}",
                        data: {order: order, _token: $('meta[name="csrf-token"]').attr('content')},
                        type: 'POST',
                        success: function (data) {},
                        error: function (data) {
                            data = data.responseJSON;
                            show_toastr('Error', data.error, 'error')
                        }
                    })
                }
            });
        });
    </script>
@endpush
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item">{{__('Deal Stage')}}</li>
@endsection
@section('action-btn')
    <div class="float-end">
        <a href="#" data-size="md" data-url="{{ route('stages.create') }}" data-ajax-popup="true" data-bs-toggle="tooltip" title="{{__('Create Deal Stage')}}" class="btn btn-sm btn-primary">
            <i class="ti ti-plus"></i>
        </a>
    </div>
@endsection
@section('content')
    @include('layouts.crm_horizontal_menu')
    <div class="row justify-content-center">
        <div class="p-3 card w-100">
            <ul class="nav nav-pills nav-fill" id="pills-tab" role="tablist">
                @php($i=0)
                @foreach($pipelines as $key => $pipeline)
                    <li class="nav-item" role="presentation">
                        <button class="nav-link @if($i==0) active @endif" id="pills-user-tab-1" data-bs-toggle="pill"
                                data-bs-target="#tab{{$key}}" type="button">{{$pipeline['name']}}
                        </button>
                    </li>
                    @php($i++)
                @endforeach
            </ul>
        </div>
        <div class="card w-100">
            <div class="card-body">
                <div class="tab-content" id="pills-tabContent">
                    @php($i=0)
                    @foreach($pipelines as $key => $pipeline)
                        <div class="tab-pane fade show @if($i==0) active @endif" id="tab{{$key}}" role="tabpanel" aria-labelledby="pills-user-tab-1">
                            <ul class="list-unstyled list-group sortable stage">
                                @foreach ($pipeline['stages'] as $stage)
                                    <li class="d-flex align-items-center justify-content-between list-group-item" data-id="{{$stage->id}}">
                                        <h6 class="mb-0">
                                            <i class="me-3 ti ti-arrows-maximize " data-feather="move"></i>
                                            <span>{{$stage->name}}</span>
                                        </h6>
                                        <span class="float-end">
                                            @can('edit stage')
                                                <div class="action-btn me-2">
                                                <a href="#" class="mx-3 btn btn-sm align-items-center bg-info" data-url="{{ URL::to('stages/'.$stage->id.'/edit') }}" data-ajax-popup="true" data-size="md" data-bs-toggle="tooltip" title="{{__('Edit')}}" data-title="{{__('Edit Deal Stages')}}">
                                                    <i class="ti ti-pencil text-white"></i>
                                                </a>
                                            </div>
                                            @endcan
                                            @if(count($pipeline['stages']))
                                                @can('delete stage')
                                                    <div class="action-btn ">
                                                        {!! Form::open(['method' => 'DELETE', 'route' => ['stages.destroy', $stage->id]]) !!}
                                                        <a href="#" class="mx-3 btn btn-sm  align-items-center bs-pass-para bg-danger" data-bs-toggle="tooltip" title="{{__('Delete')}}"><i class="ti ti-trash text-white"></i></a>
                                                        {!! Form::close() !!}
                                                    </div>
                                                @endcan
                                            @endif
                                    </span>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                        @php($i++)
                    @endforeach
                </div>
                <p class=" mt-4"><strong>{{__('Note')}} : </strong><b>{{__('You can easily change order of deal stage using drag & drop.')}}</b></p>
            </div>
        </div>
    </div>
@endsection
