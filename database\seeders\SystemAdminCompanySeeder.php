<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\SystemAdminCompany;
use Illuminate\Support\Facades\DB;

class SystemAdminCompanySeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Get all existing system admins
        $systemAdmins = User::where('type', 'system admin')->get();

        if ($systemAdmins->isEmpty()) {
            $this->command->info('No system admins found. Creating a default system admin first...');
            
            // Create a default system admin if none exists
            $defaultSystemAdmin = User::create([
                'name' => 'Default System Admin',
                'email' => '<EMAIL>',
                'password' => bcrypt('<EMAIL>'),
                'type' => 'system admin',
                'lang' => 'en',
                'avatar' => '',
                'created_by' => 0,
                'email_verified_at' => now(),
            ]);

            // Assign system admin role
            $systemAdminRole = \Spatie\Permission\Models\Role::where('name', 'system admin')->first();
            if ($systemAdminRole) {
                $defaultSystemAdmin->assignRole($systemAdminRole);
            }

            $systemAdmins = collect([$defaultSystemAdmin]);
            $this->command->info('Default system admin created: ' . $defaultSystemAdmin->email);
        }

        $this->command->info('Found ' . $systemAdmins->count() . ' system admin(s). Creating companies...');

        foreach ($systemAdmins as $systemAdmin) {
            // Check if this system admin already has a company
            if ($systemAdmin->system_admin_company_id) {
                $this->command->info('System admin "' . $systemAdmin->name . '" already has a company assigned.');
                continue;
            }

            // Create a company for this system admin
            $companyData = [
                'name' => $systemAdmin->company_name ?? ($systemAdmin->name . ' Company'),
                'description' => $systemAdmin->company_description ?? ('Company for ' . $systemAdmin->name),
                'email' => $systemAdmin->email,
                'phone' => null,
                'address' => null,
                'logo' => null,
                'is_active' => true,
                'created_by' => $systemAdmin->id,
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $company = SystemAdminCompany::create($companyData);

            // Update the system admin to be associated with their company
            $systemAdmin->update(['system_admin_company_id' => $company->id]);

            $this->command->info('Created company "' . $company->name . '" for system admin "' . $systemAdmin->name . '"');

            // Update any existing super admins created by this system admin to be part of the same company
            $superAdmins = User::where('type', 'super admin')
                              ->where('created_by', $systemAdmin->id)
                              ->whereNull('system_admin_company_id')
                              ->get();

            if ($superAdmins->count() > 0) {
                User::where('type', 'super admin')
                    ->where('created_by', $systemAdmin->id)
                    ->whereNull('system_admin_company_id')
                    ->update(['system_admin_company_id' => $company->id]);

                $this->command->info('Updated ' . $superAdmins->count() . ' super admin(s) to be part of company "' . $company->name . '"');
            }
        }

        $this->command->info('SystemAdminCompanySeeder completed successfully!');
        
        // Display summary
        $totalCompanies = SystemAdminCompany::count();
        $systemAdminsWithCompanies = User::where('type', 'system admin')
                                        ->whereNotNull('system_admin_company_id')
                                        ->count();
        $superAdminsWithCompanies = User::where('type', 'super admin')
                                       ->whereNotNull('system_admin_company_id')
                                       ->count();

        $this->command->info('Summary:');
        $this->command->info('- Total companies created: ' . $totalCompanies);
        $this->command->info('- System admins with companies: ' . $systemAdminsWithCompanies);
        $this->command->info('- Super admins with companies: ' . $superAdminsWithCompanies);
    }
}
