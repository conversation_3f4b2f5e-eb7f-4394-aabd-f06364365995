<?php
// Debug script to test logo URL generation

echo "=== LOGO DEBUG TEST ===\n";

// Test file path
$testFile = 'storage/uploads/logo/62-favicon.png';
$fullPath = 'd:/OMX/omx-new-saas/' . $testFile;

echo "Test file path: $testFile\n";
echo "Full file path: $fullPath\n";
echo "File exists: " . (file_exists($fullPath) ? 'YES' : 'NO') . "\n";

// Test URL generation using environment variable
$baseUrl = $_ENV['APP_URL'] ?? 'http://localhost:8000';
$testUrl = $baseUrl . '/storage/uploads/logo/62-favicon.png';
echo "Generated URL: $testUrl\n";

// Test storage_path function equivalent
$storagePath = 'd:/OMX/omx-new-saas/storage/uploads/logo/62-favicon.png';
echo "Storage path: $storagePath\n";
echo "Storage file exists: " . (file_exists($storagePath) ? 'YES' : 'NO') . "\n";

echo "\n=== END DEBUG ===\n";
