<?php

require_once __DIR__ . '/vendor/autoload.php';

// Initialize Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Clearing Permission Cache ===\n\n";

try {
    // Clear Spatie permission cache
    app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
    echo "✅ Spatie permission cache cleared\n";
    
    // Clear Laravel cache
    \Illuminate\Support\Facades\Cache::flush();
    echo "✅ Laravel cache cleared\n";
    
    echo "\n=== Cache Clear Complete ===\n";

} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
