@extends('layouts.admin')
@section('page-title')
    {{__('Dashboard')}}
@endsection
@section('action-btn')
<div class="float-end">
    <a href="#"
       class="btn btn-sm text-white d-flex align-items-center gap-1"
       data-bs-toggle="modal"
       data-bs-target="#dashboardSwitcherModal"
       data-bs-toggle="tooltip"
       title="{{ __('Change Dashboard') }}"
       style="background: linear-gradient(135deg, #1b5e20, #0d47a1); border: none;">
        <i class="ti ti-layout-dashboard"></i>
        <span class="d-none d-md-inline"><i class="ti ti-settings" style="margin-right: 5px;"></i>{{ __('Dashboard') }}</span>
    </a>
</div>

    <!-- Dashboard Switcher Modal -->
    <div class="modal fade" id="dashboardSwitcherModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('Select Dashboard') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="dashboard-option card h-100 cursor-pointer" data-dashboard="account">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <i class="ti ti-wallet fs-1 text-primary"></i>
                                    </div>
                                    <h5 class="mb-0">{{ __('Account Dashboard') }}</h5>
                                    <small class="text-muted">{{ __('View financial overview') }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="dashboard-option card h-100 cursor-pointer" data-dashboard="crm">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <i class="ti ti-users fs-1 text-info"></i>
                                    </div>
                                    <h5 class="mb-0">{{ __('CRM Dashboard') }}</h5>
                                    <small class="text-muted">{{ __('Customer relationship management') }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="dashboard-option card h-100 cursor-pointer" data-dashboard="hrm">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <i class="ti ti-id fs-1 text-warning"></i>
                                    </div>
                                    <h5 class="mb-0">{{ __('HRM Dashboard') }}</h5>
                                    <small class="text-muted">{{ __('Human resource management') }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="dashboard-option card h-100 cursor-pointer" data-dashboard="pos">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <i class="ti ti-cash fs-1 text-success"></i>
                                    </div>
                                    <h5 class="mb-0">{{ __('POS Dashboard') }}</h5>
                                    <small class="text-muted">{{ __('Point of sale system') }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="dashboard-option card h-100 cursor-pointer" data-dashboard="project">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <i class="fas fa-project-diagram fs-1 text-primary"></i>
                                    </div>
                                    <h5 class="mb-0">{{ __('Project Dashboard') }}</h5>
                                    <small class="text-muted">{{ __('Project management') }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('css-page')
<style>
    .apexcharts-yaxis
    {
        transform: translate(20px, 0px) !important;
    }
</style>
<script>
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Dashboard Switcher
    document.addEventListener('DOMContentLoaded', function() {
        // Get all dashboard options
        const dashboardOptions = document.querySelectorAll('.dashboard-option');
        
        // Add click event to each option
        dashboardOptions.forEach(option => {
            option.addEventListener('click', function() {
                const dashboardType = this.getAttribute('data-dashboard');
                
                // Store the selected dashboard in localStorage
                localStorage.setItem('selectedDashboard', dashboardType);
                
                // Determine the route based on dashboard type
                let route = '{{ route("dashboard") }}';
                
                switch(dashboardType) {
                    case 'crm':
                        route = '{{ route("crm.dashboard") }}';
                        break;
                    case 'hrm':
                        route = '{{ route("hrm.dashboard") }}';
                        break;
                    case 'pos':
                        route = '{{ route("pos.dashboard") }}';
                        break;
                    case 'project':
                        route = '{{ route("project.dashboard") }}';
                        break;
                            // Default is account dashboard
                }
                
                // Redirect to the selected dashboard
                window.location.href = route;
            });
        });
        
        // Highlight current dashboard in modal when opened
        const modal = document.getElementById('dashboardSwitcherModal');
        if (modal) {
            modal.addEventListener('show.bs.modal', function () {
                const currentPath = window.location.pathname;
                dashboardOptions.forEach(option => {
                    const dashboardType = option.getAttribute('data-dashboard');
                    if (
                        (dashboardType === 'account' && currentPath.includes('account-dashboard')) ||
                        (dashboardType === 'crm' && currentPath.includes('crm-dashboard')) ||
                        (dashboardType === 'hrm' && currentPath.includes('hrm-dashboard')) ||
                        (dashboardType === 'pos' && currentPath.includes('pos-dashboard')) ||
                        (dashboardType === 'project' && currentPath.includes('project-dashboard'))  
                    ) {
                        option.classList.add('border-primary');
                    } else {
                        option.classList.remove('border-primary');
                    }
                });
            });
        }
    });
</script>
@endpush

@push('script-page')
    <script>
        (function() {
            var options = {
                chart: {
                    height: 350,
                    type: 'area',
                    toolbar: {
                        show: false,
                    },
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    width: 2,
                    curve: 'smooth'
                },
                series: [{
                    name: '{{ __('Purchase') }}',
                    data: {!! json_encode($purchasesArray['value']) !!}
                    // data:  [70,270,80,245,115,260,135,280,70,215]

                },
                    {
                        name: '{{ __('POS') }}',
                        data: {!! json_encode($posesArray['value']) !!}

                        // data:  [100,300,100,260,140,290,150,300,100,250]

                    },
                ],
                xaxis: {
                    categories: {!! json_encode($purchasesArray['label']) !!},
                    title: {
                        text: '{{ __('Days') }}'
                    }
                },
                colors: ['#ff3a6e', '#6fd943'],

                grid: {
                    strokeDashArray: 4,
                },
                legend: {
                    show: false,
                },
                // markers: {
                //     size: 4,
                //     colors: ['#ffa21d', '#FF3A6E'],
                //     opacity: 0.9,
                //     strokeWidth: 2,
                //     hover: {
                //         size: 7,
                //     }
                // },
                yaxis: {
                    title: {
                        text: '{{ __('Amount') }}',
                        offsetX: 30,
                        offsetY: -35,
                    },
                }
            };
            var chart = new ApexCharts(document.querySelector("#traffic-chart"), options);
            chart.render();
        })();

    </script>
@endpush
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item">{{__('POS')}}</li>
@endsection
@section('content')
    <div class="row gy-4">
        <div class="col-xxl-3 col-xl-4 col-sm-6 col-12 pos-dash-card">
            <div class="pos-card-inner card mb-0 p-3 d-flex flex-column justify-content-between" style="height: 100%;border-top: 4px solid #2e7d32;">
                <!-- Top Content -->
                <div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <i class="fas fa-cash-register fa-2x" style="color: #2e7d32;"></i>
                        <div class="text-end d-flex align-items-center justify-content-end gap-2">
                            <span>{{ __('Total :') }}</span>
                            <h4 class="mb-0">{{$pos_data['monthlyPosAmount']}}</h4>
                        </div>
                    </div>
                </div>
                <!-- Bottom H2 Link -->
                <div class="mt-4 pt-2">
                    <h2 class="h5 text-black m-0">
                        <a href="{{ route('pos.report') }}" class="text-black">
                            {{ __(' POS Of This Month ') }}<i class="fas fa-external-link-alt" style="font-size: 0.8rem;"></i>
                        </a>
                    </h2>
                </div>
            </div>
        </div>
        <div class="col-xxl-3 col-xl-4 col-sm-6 col-12 pos-dash-card">
            <div class="pos-card-inner card mb-0 p-3 d-flex flex-column justify-content-between" style="height: 100%;border-top: 4px solid #2e7d32;">
                <!-- Top Content -->
                <div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <i class="fas fa-cash-register fa-2x" style="color: #2e7d32;"></i>
                        <div class="text-end d-flex align-items-center justify-content-end gap-2">
                            <span>{{ __('Total :') }}</span>
                            <h4 class="mb-0">{{$pos_data['totalPosAmount']}}</h4>
                        </div>
                    </div>
                </div>
                <!-- Bottom H2 Link -->
                <div class="mt-auto pt-2">
                    <h2 class="h5 text-black m-0">
                        <a href="{{ route('pos.report') }}" class="text-black">
                            {{ __(' Total POS ') }}<i class="fas fa-external-link-alt" style="font-size: 0.8rem;"></i>
                        </a>
                    </h2>
                </div>
            </div>
        </div>
        <div class="col-xxl-3 col-xl-4 col-sm-6 col-12 pos-dash-card">
            <div class="pos-card-inner card mb-0 p-3 d-flex flex-column justify-content-between" style="height: 100%;border-top: 4px solid #2e7d32;">
                <!-- Top Content -->
                <div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <i class="fas fa-shopping-cart fa-2x" style="color: #2e7d32;"></i>
                        <div class="text-end d-flex align-items-center justify-content-end gap-2">
                            <span>{{ __('Total :') }}</span>
                            <h4 class="mb-0">{{$pos_data['monthlyPurchaseAmount']}}</h4>
                        </div>
                    </div>
                </div>
                <!-- Bottom H2 Link -->
                <div class="mt-auto pt-2">
                    <h2 class="h5 text-black m-0">
                        <a href="{{ route('purchase.index') }}" class="text-black">
                            {{ __('Purchase Of This Month ') }}<i class="fas fa-external-link-alt" style="font-size: 0.8rem;"></i>
                        </a>
                    </h2>
                </div>
            </div>
        </div>
        <div class="col-xxl-3 col-xl-4 col-sm-6 col-12 pos-dash-card">
            <div class="pos-card-inner card mb-0 p-3 d-flex flex-column justify-content-between" style="height: 100%;border-top: 4px solid #2e7d32;">
                <!-- Top Content -->
                <div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <i class="fas fa-shopping-cart fa-2x" style="color: #2e7d32;"></i> <!-- Pink icon -->
                        <div class="text-end d-flex align-items-center justify-content-end gap-2">
                            <span>{{ __('Total :') }}</span>
                            <h4 class="mb-0">{{ $pos_data['totalPurchaseAmount'] }}</h4>
                        </div>
                    </div>
                </div>

                <!-- Bottom Link -->
                <div class="mt-auto pt-2">
                    <h2 class="h5 text-black m-0">
                        <a href="{{ route('purchase.index') }}" class="text-black">
                            {{ __('Purchase Amount') }}
                            <i class="fas fa-external-link-alt" style="font-size: 0.8rem;"></i>
                        </a>
                    </h2>
                </div>
            </div>
        </div>

        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row ">
                        <div class="col-6">
                            <h5><i class="fas fa-shopping-cart me-2" style="color:#2E7D32; margin-right: 5px;"></i>{{ __('Purchase Vs POS Report') }}</h5>
                        </div>
                        <div class="col-6 text-end">
                            <h6>{{ __('Last 10 Days') }}</h6>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div id="traffic-chart"></div>
                </div>
            </div>
        </div>

    </div>
@endsection
