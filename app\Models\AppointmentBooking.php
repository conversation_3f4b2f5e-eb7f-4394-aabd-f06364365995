<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AppointmentBooking extends Model
{
    protected $fillable = [
        'event_id',
        'event_location',
        'event_location_value',
        'event_date',
        'time_zone',
        'time_slots'
    ];

    protected $casts = [
        'event_date' => 'date'
    ];

    /**
     * Relationship to CalendarEvent
     */
    public function event(): BelongsTo
    {
        return $this->belongsTo(CalendarEvent::class, 'event_id');
    }

    /**
     * Alternative relationship name for clarity
     */
    public function calendarEvent(): BelongsTo
    {
        return $this->belongsTo(CalendarEvent::class, 'event_id');
    }
}
