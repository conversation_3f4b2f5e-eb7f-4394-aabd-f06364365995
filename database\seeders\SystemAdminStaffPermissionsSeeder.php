<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;

class SystemAdminStaffPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $this->command->info('Creating System Admin Staff Permissions...');

        // Define permissions for each system admin sidebar module
        $modulePermissions = [
            // Dashboard
            'dashboard' => [
                'view system admin dashboard',
            ],
            
            // White Label Management
            'white_label' => [
                'view white label',
                'manage white label users',
                'create white label users',
                'edit white label users',
                'delete white label users',
            ],
            
            // Sub-accounts Management
            'sub_accounts' => [
                'view sub accounts',
                'manage sub accounts',
                'create sub accounts',
                'edit sub accounts',
                'delete sub accounts',
            ],
            
            // Staff Management (only for system admins, not staff)
            'staff_management' => [
                'view staff management',
                'manage staff',
                'create staff',
                'edit staff',
                'delete staff',
                'manage staff permissions',
            ],
            
            // Role Management
            'role_management' => [
                'view role management',
                'manage roles',
                'create roles',
                'edit roles',
                'delete roles',
            ],
            
            // Plan Management
            'plan_management' => [
                'view plan management',
                'manage plans',
                'create plans',
                'edit plans',
                'delete plans',
            ],
            
            // Pricing Plans
            'pricing_plans' => [
                'view pricing plans',
                'manage pricing plans',
                'create pricing plans',
                'edit pricing plans',
                'delete pricing plans',
            ],
            
            // Support System
            'support_system' => [
                'view support system',
                'manage support tickets',
                'create support tickets',
                'edit support tickets',
                'delete support tickets',
            ],
            

        ];

        // Create permissions
        foreach ($modulePermissions as $module => $permissions) {
            $this->command->info("Creating permissions for: " . ucfirst(str_replace('_', ' ', $module)));
            
            foreach ($permissions as $permission) {
                Permission::firstOrCreate([
                    'name' => $permission,
                    'guard_name' => 'web'
                ]);
                
                $this->command->info("  ✓ " . $permission);
            }
        }

        $this->command->info('System Admin Staff Permissions created successfully!');
        
        // Display summary
        $totalPermissions = Permission::count();
        $this->command->info("Total permissions in system: " . $totalPermissions);
    }
}
