<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\ModuleIntegration;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Auth;

// Initialize Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Sidebar Rendering Test ===\n\n";

try {
    // Get a company user
    $companyUser = User::where('type', 'company')->first();
    if (!$companyUser) {
        echo "❌ No company user found\n";
        exit;
    }

    echo "Testing sidebar rendering for company user: {$companyUser->name}\n\n";

    // Simulate authentication
    Auth::login($companyUser);

    // Test the conditions that should show automatish in sidebar
    echo "1. Testing sidebar conditions...\n";
    
    // Check Gate permission
    $hasGatePermission = Gate::check('access automatish');
    echo "   - Gate::check('access automatish'): " . ($hasGatePermission ? 'PASS' : 'FAIL') . "\n";
    
    // Check module integration
    $automatishModule = \App\Models\ModuleIntegration::where('name', 'Automatish')->where('enabled', true)->first();
    echo "   - Automatish module enabled: " . ($automatishModule ? 'PASS' : 'FAIL') . "\n";
    echo "   - SSO endpoint configured: " . ($automatishModule && $automatishModule->sso_endpoint ? 'PASS' : 'FAIL') . "\n";
    
    // Check user type condition
    $isCompanyUser = Auth::user()->type != 'super admin' && Auth::user()->type != 'system admin' && Auth::user()->type != 'staff';
    echo "   - User type condition (not super admin/system admin/staff): " . ($isCompanyUser ? 'PASS' : 'FAIL') . "\n";
    
    // Overall condition
    $shouldShow = $hasGatePermission && $automatishModule && $automatishModule->sso_endpoint && $isCompanyUser;
    echo "   - Overall condition: " . ($shouldShow ? 'PASS - Automatish SHOULD appear' : 'FAIL - Automatish should NOT appear') . "\n\n";

    // Test route existence
    echo "2. Testing route availability...\n";
    try {
        $routeUrl = route('company.modules.automatish');
        echo "   - Route 'company.modules.automatish': AVAILABLE ($routeUrl)\n";
    } catch (Exception $e) {
        echo "   - Route 'company.modules.automatish': NOT AVAILABLE - " . $e->getMessage() . "\n";
    }

    echo "\n3. Summary:\n";
    if ($shouldShow) {
        echo "✅ Automatish should appear in the sidebar for company users\n";
        echo "   - Section: Company users (not super admin/system admin/staff)\n";
        echo "   - Location: After OMX Flow module\n";
        echo "   - Route: company.modules.automatish\n";
        echo "   - Icon: ti-robot\n";
    } else {
        echo "❌ Automatish will NOT appear in the sidebar\n";
        echo "   - Check the failed conditions above\n";
    }

} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Test Complete ===\n";
