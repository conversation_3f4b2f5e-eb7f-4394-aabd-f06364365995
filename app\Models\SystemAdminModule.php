<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SystemAdminModule extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'description',
        'is_active',
        'sort_order'
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the permissions associated with this module
     */
    public function permissions()
    {
        return $this->hasMany(SuperAdminModulePermission::class, 'module_id');
    }

    /**
     * Get all super admins who have access to this module
     */
    public function superAdmins()
    {
        return $this->belongsToMany(User::class, 'super_admin_module_permissions', 'module_id', 'super_admin_id')
            ->withPivot('has_access', 'permissions')
            ->withTimestamps();
    }

    /**
     * Scope a query to only include active modules
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get modules ordered by sort_order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc');
    }
}
