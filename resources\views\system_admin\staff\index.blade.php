@extends('layouts.admin')

@section('page-title')
    {{ __('Staff Management') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('system-admin.dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('Staff Management') }}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        <a href="{{ route('system-admin.staff.create') }}" class="btn btn-sm btn-primary">
            <i class="ti ti-plus"></i> {{ __('Create Staff') }}
        </a>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-header card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table" id="pc-dt-simple">
                            <thead>
                                <tr>
                                    <th>{{ __('Name') }}</th>
                                    <th>{{ __('Email') }}</th>
                                    <th>{{ __('Status') }}</th>
                                    <th>{{ __('Company') }}</th>
                                    <th>{{ __('Created At') }}</th>
                                    <th width="200px">{{ __('Action') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($staff as $member)
                                    <tr>
                                        <td>{{ $member->name }}</td>
                                        <td>{{ $member->email }}</td>
                                        <td>
                                            <span class="badge bg-success">
                                                {{ __('Active Staff') }}
                                            </span>
                                        </td>
                                        <td>{{ $company->name }}</td>
                                        <td>{{ $member->created_at->format('M d, Y') }}</td>
                                        <td class="Action">
                                            <span>
                                                <div class="action-btn bg-warning ms-2">
                                                    <a href="{{ route('system-admin.staff.permissions', $member->id) }}"
                                                       class="mx-3 btn btn-sm align-items-center"
                                                       data-bs-toggle="tooltip"
                                                       title="{{ __('Manage Permissions') }}">
                                                        <i class="ti ti-shield text-white"></i>
                                                    </a>
                                                </div>

                                                <div class="action-btn bg-info ms-2">
                                                    <a href="{{ route('system-admin.staff.edit', $member->id) }}"
                                                       class="mx-3 btn btn-sm align-items-center"
                                                       data-bs-toggle="tooltip"
                                                       title="{{ __('Edit') }}">
                                                        <i class="ti ti-pencil text-white"></i>
                                                    </a>
                                                </div>

                                                @if($member->id !== auth()->id())
                                                <div class="action-btn bg-danger ms-2">
                                                    <form method="POST" action="{{ route('system-admin.staff.destroy', $member->id) }}"
                                                          style="display: inline;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit"
                                                                class="mx-3 btn btn-sm align-items-center bs-pass-para"
                                                                data-bs-toggle="tooltip"
                                                                title="{{ __('Delete') }}"
                                                                onclick="return confirm('{{ __('Are you sure you want to delete this staff member?') }}')">
                                                            <i class="ti ti-trash text-white"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                                @endif
                                            </span>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
