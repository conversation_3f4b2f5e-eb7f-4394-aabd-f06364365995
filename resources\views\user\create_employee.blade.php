@extends('layouts.admin')

@section('page-title')
    {{ __('Create Employee') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ route('employee.index') }}">{{ __('Employee') }}</a></li>
    <li class="breadcrumb-item">{{ __('Create Employee') }}</li>
@endsection

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{{ __('Create Employee') }}</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('employees.store') }}" class="needs-validation" novalidate>
                    @csrf
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name" class="form-label">{{ __('Name') }} <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="email" class="form-label">{{ __('Email') }} <span class="text-danger">*</span></label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email') }}" required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="password" class="form-label">{{ __('Password') }} <span class="text-danger">*</span></label>
                                <input type="password" class="form-control @error('password') is-invalid @enderror" id="password" name="password" required minlength="6">
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="branch_id" class="form-label">{{ __('Branch') }}</label>
                                <select class="form-control" id="branch_id" name="branch_id">
                                    <option value="">{{ __('Select Branch') }}</option>
                                    @foreach($branches as $id => $name)
                                        <option value="{{ $id }}" {{ old('branch_id') == $id ? 'selected' : '' }}>{{ $name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="department_id" class="form-label">{{ __('Department') }}</label>
                                <select class="form-control" id="department_id" name="department_id">
                                    <option value="">{{ __('Select Department') }}</option>
                                    @foreach($departments as $id => $name)
                                        <option value="{{ $id }}" {{ old('department_id') == $id ? 'selected' : '' }}>{{ $name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="designation_id" class="form-label">{{ __('Designation') }}</label>
                                <select class="form-control" id="designation_id" name="designation_id">
                                    <option value="">{{ __('Select Designation') }}</option>
                                    @foreach($designations as $id => $name)
                                        <option value="{{ $id }}" {{ old('designation_id') == $id ? 'selected' : '' }}>{{ $name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="dob" class="form-label">{{ __('Date of Birth') }}</label>
                                <input type="date" class="form-control" id="dob" name="dob" value="{{ old('dob') }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="gender" class="form-label">{{ __('Gender') }}</label>
                                <select class="form-control" id="gender" name="gender">
                                    <option value="">{{ __('Select Gender') }}</option>
                                    <option value="male" {{ old('gender') == 'male' ? 'selected' : '' }}>{{ __('Male') }}</option>
                                    <option value="female" {{ old('gender') == 'female' ? 'selected' : '' }}>{{ __('Female') }}</option>
                                    <option value="other" {{ old('gender') == 'other' ? 'selected' : '' }}>{{ __('Other') }}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="phone" class="form-label">{{ __('Phone') }}</label>
                                <input type="text" class="form-control" id="phone" name="phone" value="{{ old('phone') }}">
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="address" class="form-label">{{ __('Address') }}</label>
                                <textarea class="form-control" id="address" name="address" rows="2">{{ old('address') }}</textarea>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="col-12 mt-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5>{{ __('Module Permissions') }}</h5>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-primary me-2" id="selectAllModules">{{ __('Select All Modules') }}</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="deselectAllModules">{{ __('Deselect All') }}</button>
                            </div>
                        </div>
                        <hr>
                        @foreach($availableModules as $moduleKey => $moduleData)
                            @php
                                $createPermissions = [];
                                $readPermissions = [];
                                $deletePermissions = [];
                                $otherPermissions = [];
                                if (is_array($moduleData) && isset($moduleData['permissions'])) {
                                    foreach($moduleData['permissions'] as $permission) {
                                        if (str_contains($permission, 'create')) {
                                            $createPermissions[] = $permission;
                                        } elseif (str_contains($permission, 'view') || str_contains($permission, 'show') || str_contains($permission, 'dashboard')) {
                                            $readPermissions[] = $permission;
                                        } elseif (str_contains($permission, 'delete')) {
                                            $deletePermissions[] = $permission;
                                        } else {
                                            $otherPermissions[] = $permission;
                                        }
                                    }
                                }
                            @endphp
                            <div class="card mb-3">
                                <div class="card-header">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="form-check">
                                            <input class="form-check-input module-checkbox" type="checkbox" id="module_{{ $moduleKey }}" name="modules[{{ $moduleKey }}]" data-module="{{ $moduleKey }}">
                                            <label class="form-check-label fw-bold" for="module_{{ $moduleKey }}">
                                                {{ is_array($moduleData) && isset($moduleData['name']) ? $moduleData['name'] : $moduleKey }}
                                            </label>
                                        </div>
                                        <div class="btn-group" role="group" style="display: none;" id="selectButtons_{{ $moduleKey }}">
                                            <button type="button" class="btn btn-sm btn-outline-success select-all-module" data-module="{{ $moduleKey }}">{{ __('Select All') }}</button>
                                            <button type="button" class="btn btn-sm btn-outline-warning select-create" data-module="{{ $moduleKey }}">{{ __('Create') }}</button>
                                            <button type="button" class="btn btn-sm btn-outline-info select-read" data-module="{{ $moduleKey }}">{{ __('Read') }}</button>
                                            <button type="button" class="btn btn-sm btn-outline-danger select-delete" data-module="{{ $moduleKey }}">{{ __('Delete') }}</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body permissions-section" id="permissions_{{ $moduleKey }}" style="display: none;">
                                    @if(count($createPermissions) > 0)
                                        <div class="permission-group mb-4">
                                            <h6 class="text-success mb-2"><i class="fas fa-plus-circle"></i> {{ __('Create Permissions') }}</h6>
                                            <div class="row">
                                                @foreach($createPermissions as $permission)
                                                    <div class="col-md-4 mb-2">
                                                        <div class="form-check">
                                                            <input class="form-check-input permission-checkbox create-permission" type="checkbox" id="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}" name="permissions[{{ $moduleKey }}][]" value="{{ $permission }}" data-module="{{ $moduleKey }}" data-type="create">
                                                            <label class="form-check-label" for="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}">{{ ucwords(str_replace('_', ' ', $permission)) }}</label>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                    @if(count($readPermissions) > 0)
                                        <div class="permission-group mb-4">
                                            <h6 class="text-info mb-2"><i class="fas fa-eye"></i> {{ __('Read/View Permissions') }}</h6>
                                            <div class="row">
                                                @foreach($readPermissions as $permission)
                                                    <div class="col-md-4 mb-2">
                                                        <div class="form-check">
                                                            <input class="form-check-input permission-checkbox read-permission" type="checkbox" id="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}" name="permissions[{{ $moduleKey }}][]" value="{{ $permission }}" data-module="{{ $moduleKey }}" data-type="read">
                                                            <label class="form-check-label" for="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}">{{ ucwords(str_replace('_', ' ', $permission)) }}</label>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                    @if(count($deletePermissions) > 0)
                                        <div class="permission-group mb-4">
                                            <h6 class="text-danger mb-2"><i class="fas fa-trash"></i> {{ __('Delete Permissions') }}</h6>
                                            <div class="row">
                                                @foreach($deletePermissions as $permission)
                                                    <div class="col-md-4 mb-2">
                                                        <div class="form-check">
                                                            <input class="form-check-input permission-checkbox delete-permission" type="checkbox" id="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}" name="permissions[{{ $moduleKey }}][]" value="{{ $permission }}" data-module="{{ $moduleKey }}" data-type="delete">
                                                            <label class="form-check-label" for="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}">{{ ucwords(str_replace('_', ' ', $permission)) }}</label>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                    @if(count($otherPermissions) > 0)
                                        <div class="permission-group mb-4">
                                            <h6 class="text-secondary mb-2"><i class="fas fa-cog"></i> {{ __('Other Permissions') }}</h6>
                                            <div class="row">
                                                @foreach($otherPermissions as $permission)
                                                    <div class="col-md-4 mb-2">
                                                        <div class="form-check">
                                                            <input class="form-check-input permission-checkbox other-permission" type="checkbox" id="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}" name="permissions[{{ $moduleKey }}][]" value="{{ $permission }}" data-module="{{ $moduleKey }}" data-type="other">
                                                            <label class="form-check-label" for="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}">{{ ucwords(str_replace('_', ' ', $permission)) }}</label>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                    <div class="row mt-4">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">{{ __('Create Employee') }}</button>
                            <a href="{{ route('users.index') }}" class="btn btn-secondary">{{ __('Cancel') }}</a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('script-page')
    <script>
        $(document).ready(function() {
            // Module checkbox toggle
            $('.module-checkbox').on('change', function() {
                var module = $(this).data('module');
                var permissionsSection = $('#permissions_' + module);
                var permissionCheckboxes = permissionsSection.find('.permission-checkbox');
                var selectButtons = $('#selectButtons_' + module);
                
                if ($(this).is(':checked')) {
                    permissionsSection.show();
                    selectButtons.show();
                } else {
                    permissionsSection.hide();
                    selectButtons.hide();
                    permissionCheckboxes.prop('checked', false);
                }
            });

            // If any permission is checked, ensure module is checked
            $('.permission-checkbox').on('change', function() {
                var module = $(this).data('module');
                var moduleCheckbox = $('#module_' + module);
                var permissionCheckboxes = $('input[name="permissions[' + module + '][]"]');
                var checkedPermissions = permissionCheckboxes.filter(':checked');
                var selectButtons = $('#selectButtons_' + module);
                
                if (checkedPermissions.length > 0) {
                    moduleCheckbox.prop('checked', true);
                    $('#permissions_' + module).show();
                    selectButtons.show();
                } else {
                    moduleCheckbox.prop('checked', false);
                    $('#permissions_' + module).hide();
                    selectButtons.hide();
                }
            });

            // Select All Modules
            $('#selectAllModules').on('click', function() {
                $('.module-checkbox').prop('checked', true).trigger('change');
            });

            // Deselect All Modules
            $('#deselectAllModules').on('click', function() {
                $('.module-checkbox').prop('checked', false).trigger('change');
            });

            // Select All permissions for a specific module
            $('.select-all-module').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .permission-checkbox').prop('checked', true);
            });

            // Select Create permissions for a specific module
            $('.select-create').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .create-permission').prop('checked', true);
            });

            // Select Read permissions for a specific module
            $('.select-read').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .read-permission').prop('checked', true);
            });

            // Select Delete permissions for a specific module
            $('.select-delete').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .delete-permission').prop('checked', true);
            });
        });
    </script>
@endpush 