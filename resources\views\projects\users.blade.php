@if($project->users->count() > 0)
    @foreach($project->users as $user)
        <div class="member-item p-3 mb-3 rounded-3" style="background: rgba(255, 255, 255, 0.7); border: 1px solid rgba(255, 255, 255, 0.3); transition: all 0.3s ease;">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center gap-3">
                    <!-- Text Avatar -->
                    <div class="user-avatar-text" style="
                        width: 48px;
                        height: 48px;
                        background: var(--theme-color);
                        border-radius: 12px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-weight: 700;
                        font-size: 1.1rem;
                        color: white;
                        text-transform: uppercase;
                        box-shadow: 0 4px 12px rgba(var(--theme-color-rgb), 0.3);
                        border: 2px solid rgba(255, 255, 255, 0.2);
                        position: relative;
                        overflow: hidden;
                    ">
                        <span style="position: relative; z-index: 2;">{{ substr($user->name, 0, 2) }}</span>
                        <div style="
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(0, 0, 0, 0.1) 100%);
                            z-index: 1;
                        "></div>
                    </div>

                    <!-- User Info -->
                    <div class="flex-grow-1">
                        <h6 class="mb-1 fw-semibold text-dark">{{ $user->name }}</h6>
                        <div class="d-flex align-items-center gap-2">
                            <small class="text-muted">{{ $user->email }}</small>
                            @if($user->type)
                                <span class="badge" style="
                                    background: rgba(var(--theme-color-rgb), 0.1);
                                    color: var(--theme-color);
                                    font-size: 0.7rem;
                                    padding: 0.25rem 0.5rem;
                                    border-radius: 6px;
                                    font-weight: 600;
                                    text-transform: capitalize;
                                ">{{ $user->type }}</span>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Action Button -->
                <div class="d-flex align-items-center gap-2">
                    @can('delete project user')
                        {!! Form::open(['method' => 'DELETE', 'route' => ['projects.user.destroy', [$project->id, $user->id]], 'class' => 'd-inline']) !!}
                        <button type="submit" class="btn btn-sm btn-outline-danger bs-pass-para" style="
                            border-radius: 8px;
                            padding: 0.4rem 0.6rem;
                            border: 1px solid rgba(220, 53, 69, 0.3);
                            background: rgba(220, 53, 69, 0.05);
                            transition: all 0.3s ease;
                        " data-bs-toggle="tooltip" title="{{ __('Remove from project') }}">
                            <i class="ti ti-trash" style="font-size: 0.875rem;"></i>
                        </button>
                        {!! Form::close() !!}
                    @endcan
                </div>
            </div>
        </div>
    @endforeach
@else
    <div class="text-center py-5">
        <div class="info-icon-large d-inline-flex mb-3">
            <i class="ti ti-users"></i>
        </div>
        <h6 class="text-muted mb-2">{{ __('No Team Members Yet') }}</h6>
        <p class="text-muted small mb-0">{{ __('Invite team members to collaborate on this project') }}</p>
    </div>
@endif
