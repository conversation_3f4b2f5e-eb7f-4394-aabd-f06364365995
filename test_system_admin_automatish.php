<?php

/**
 * Test script to verify System Admin user creation syncs to Automatish
 * This tests the SystemAdminController methods that were just updated
 * 
 * Usage: php test_system_admin_automatish.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\ModuleIntegration;
use App\Http\Controllers\SystemAdminController;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== System Admin Automatish Integration Test ===\n\n";

// Check if Automatish module exists
$automatishModule = ModuleIntegration::where('name', 'Automatish')->first();

if (!$automatishModule) {
    echo "❌ ERROR: Automatish module not found in module_integrations table\n";
    exit(1);
}

echo "✅ Automatish module configuration:\n";
echo "   - Name: {$automatishModule->name}\n";
echo "   - Base URL: {$automatishModule->base_url}\n";
echo "   - Sync Endpoint: {$automatishModule->sync_endpoint}\n";
echo "   - Enabled: " . ($automatishModule->enabled ? 'Yes' : 'No') . "\n\n";

// Create a system admin user to test with
$systemAdmin = User::where('type', 'system admin')->first();
if (!$systemAdmin) {
    echo "❌ ERROR: No system admin user found\n";
    echo "Please create a system admin user first.\n";
    exit(1);
}

echo "✅ Found system admin user: {$systemAdmin->email}\n\n";

// Test the SystemAdminController syncUserToAutomatish method directly
echo "🔄 Testing SystemAdminController syncUserToAutomatish method...\n";

// Create a test user to sync
$testUser = new User();
$testUser->name = 'System Admin Test User';
$testUser->email = 'sysadmin.test.' . time() . '@example.com';
$testUser->password = bcrypt('testpassword123');
$testUser->type = 'super admin';
$testUser->created_by = $systemAdmin->id;
$testUser->save();

echo "✅ Created test user:\n";
echo "   - ID: {$testUser->id}\n";
echo "   - Name: {$testUser->name}\n";
echo "   - Email: {$testUser->email}\n";
echo "   - Type: {$testUser->type}\n";
echo "   - Password (plain): testpassword123\n\n";

// Test the sync method using reflection to access private method
$controller = new SystemAdminController();
$reflection = new ReflectionClass($controller);
$method = $reflection->getMethod('syncUserToAutomatish');
$method->setAccessible(true);

echo "📤 Expected payload to be sent to Automatish:\n";
$expectedPayload = [
    'fullName' => $testUser->name,
    'email' => $testUser->email,
    'password' => 'testpassword123'
];
echo json_encode($expectedPayload, JSON_PRETTY_PRINT) . "\n\n";

echo "🔄 Calling SystemAdminController->syncUserToAutomatish()...\n";
echo "Check your Laravel logs for detailed information.\n\n";

try {
    $result = $method->invoke($controller, $testUser, 'testpassword123');
    
    if ($result) {
        echo "✅ SystemAdminController sync completed successfully!\n";
    } else {
        echo "❌ SystemAdminController sync failed!\n";
    }
} catch (Exception $e) {
    echo "❌ Exception during SystemAdminController sync: " . $e->getMessage() . "\n";
}

echo "\n📋 To see the full logs, run:\n";
echo "tail -f storage/logs/laravel.log | grep -A 20 -B 5 'SystemAdminController.*Automatish'\n\n";

echo "🔍 Log entries to look for:\n";
echo "1. 'SystemAdminController: Attempting to sync user to Automatish'\n";
echo "2. 'Sending request to Automatish' (from ModuleIntegrationController)\n";
echo "3. 'Automatish response received' (from ModuleIntegrationController)\n";
echo "4. 'SystemAdminController: Automatish sync result'\n\n";

// Clean up test user
$testUser->delete();
echo "🧹 Test user cleaned up.\n";

echo "\n=== Test Complete ===\n";
echo "The SystemAdminController now includes Automatish sync in:\n";
echo "1. storeSuperAdmin() - When creating super admin users\n";
echo "2. storeCompany() - When creating company users\n";
echo "3. SystemAdminStaffController->store() - When creating staff users\n\n";

echo "Next steps:\n";
echo "1. Try creating a subaccount through the system admin interface\n";
echo "2. Check the logs to see the sync happening\n";
echo "3. Verify the user appears in Automatish\n";
