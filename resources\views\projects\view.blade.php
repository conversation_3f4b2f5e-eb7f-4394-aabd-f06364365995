@extends('layouts.admin')
@section('page-title')
    {{ ucwords($project->project_name) }}
@endsection

@push('css-page')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dragula/3.7.3/dragula.min.css">
<style>
    /* Modern Minimalistic Project Details */
    :root {
        --primary-gradient: linear-gradient(135deg, var(--theme-color) 0%, rgba(var(--theme-color-rgb), 0.8) 100%);
        --success-gradient: linear-gradient(135deg, var(--theme-color) 0%, rgba(var(--theme-color-rgb), 0.7) 100%);
        --warning-gradient: linear-gradient(135deg, var(--theme-color) 0%, rgba(var(--theme-color-rgb), 0.6) 100%);
        --info-gradient: linear-gradient(135deg, var(--theme-color) 0%, rgba(var(--theme-color-rgb), 0.5) 100%);
        --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        --card-shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.12);
        --border-radius: 20px;
        --border-radius-sm: 12px;
        --spacing: 2rem;
    }

    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    }

    /* Container and Layout */
    .project-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    /* Modern Cards */
    .modern-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;
        position: relative;
    }

    .modern-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: var(--primary-gradient);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .modern-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--card-shadow-hover);
    }

    .modern-card:hover::before {
        opacity: 1;
    }

    /* Hero Section */
    .project-hero {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
        backdrop-filter: blur(30px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--border-radius);
        padding: 3rem;
        margin-bottom: var(--spacing);
        position: relative;
        overflow: hidden;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
    }

    .project-hero::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    .project-avatar-modern {
        width: 80px;
        height: 80px;
        background: var(--theme-color);
        border-radius: var(--border-radius-sm);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8px 25px rgba(var(--theme-color-rgb), 0.3);
        position: relative;
        z-index: 2;
        border: 3px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(10px);
        overflow: hidden;
    }

    .project-avatar-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(0, 0, 0, 0.1) 100%);
        z-index: 1;
    }

    .project-avatar-modern i {
        color: white !important;
        font-size: 2.4rem !important;
        text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        transition: all 0.3s ease;
        position: relative;
        z-index: 2;
        font-weight: 600;
    }

    .project-avatar-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(var(--theme-color-rgb), 0.4);
    }

    .project-avatar-modern:hover i {
        transform: scale(1.1) rotate(5deg);
        text-shadow: 0 3px 12px rgba(0, 0, 0, 0.6);
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.4));
    }

    /* Subtle pulse animation for the project avatar */
    .project-avatar-modern {
        animation: subtlePulse 3s ease-in-out infinite;
    }

    @keyframes subtlePulse {
        0%, 100% {
            box-shadow: 0 8px 25px rgba(var(--theme-color-rgb), 0.3);
        }
        50% {
            box-shadow: 0 8px 25px rgba(var(--theme-color-rgb), 0.4);
        }
    }

    .project-title {
        font-size: 2.5rem;
        font-weight: 700;
        background: linear-gradient(135deg, var(--theme-color) 0%, rgba(var(--theme-color-rgb), 0.8) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
        position: relative;
        z-index: 2;
    }

    .project-description {
        font-size: 1.1rem;
        color: #64748b;
        line-height: 1.6;
        margin-bottom: 2rem;
        position: relative;
        z-index: 2;
    }

    /* Status Badge */
    .status-badge {
        padding: 0.5rem 1.5rem;
        border-radius: 50px;
        font-weight: 600;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 2;
    }

    /* Progress Section */
    .progress-modern {
        height: 12px;
        background: rgba(0, 0, 0, 0.05);
        border-radius: 10px;
        overflow: hidden;
        position: relative;
    }

    .progress-modern .progress-bar {
        background: linear-gradient(135deg, var(--theme-color) 0%, rgba(var(--theme-color-rgb), 0.7) 100%);
        border-radius: 10px;
        position: relative;
        overflow: hidden;
    }

    .progress-modern .progress-bar::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    /* Info Grid */
    .info-grid-item {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.4);
        border-radius: var(--border-radius-sm);
        padding: 1.8rem 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    }

    .info-grid-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .info-grid-item:hover::before {
        opacity: 1;
    }

    .info-grid-item:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .info-icon {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1.2rem;
        font-size: 1.5rem;
        transition: all 0.3s ease;
        position: relative;
        border: 2px solid rgba(var(--theme-color-rgb), 0.1);
        box-shadow: 0 2px 8px rgba(var(--theme-color-rgb), 0.1);
    }

    .info-icon::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(var(--theme-color-rgb), 0.2);
        border-radius: 50%;
        z-index: 1;
    }

    .info-icon i {
        position: relative;
        z-index: 2;
        color: var(--theme-color) !important;
        font-weight: 700;
        font-size: 1.7rem;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    }

    .info-icon:hover {
        transform: translateY(-2px);
    }

    .info-icon:hover::before {
        background: rgba(var(--theme-color-rgb), 0.35);
        transform: scale(1.05);
    }

    /* Large info icons for empty states */
    .info-icon-large {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1.2rem;
        font-size: 2rem;
        transition: all 0.3s ease;
        position: relative;
        border: none;
        box-shadow: none;
    }

    .info-icon-large::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(var(--theme-color-rgb), 0.15);
        border-radius: 50%;
        z-index: 1;
    }

    .info-icon-large i {
        position: relative;
        z-index: 2;
        color: var(--theme-color) !important;
        font-weight: 600;
    }

    /* Small activity icons */
    .info-icon-small {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        transition: all 0.3s ease;
        position: relative;
        border: none;
        box-shadow: none;
        flex-shrink: 0;
    }

    .info-icon-small::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(var(--theme-color-rgb), 0.15);
        border-radius: 50%;
        z-index: 1;
    }

    .info-icon-small i {
        position: relative;
        z-index: 2;
        color: var(--theme-color) !important;
        font-weight: 600;
    }

    /* Metric Cards */
    .metric-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--border-radius);
        padding: 2.5rem 2rem;
        text-align: center;
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    }

    .metric-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--success-gradient);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .metric-card:hover::before {
        transform: scaleX(1);
    }

    .metric-number {
        font-size: 3rem;
        font-weight: 800;
        background: linear-gradient(135deg, var(--theme-color) 0%, rgba(var(--theme-color-rgb), 0.7) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        line-height: 1;
        margin-bottom: 0.5rem;
    }

    .metric-label {
        color: #64748b;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        font-size: 0.875rem;
    }

    /* Action Buttons */
    .action-btn-modern {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.4);
        border-radius: var(--border-radius-sm);
        padding: 1.2rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    }

    .action-btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.5s ease;
    }

    .action-btn-modern:hover::before {
        left: 100%;
    }

    .action-btn-modern:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        border-color: rgba(var(--theme-color-rgb), 0.2);
    }

    .action-btn-primary {
        background: linear-gradient(135deg, var(--theme-color) 0%, rgba(var(--theme-color-rgb), 0.8) 100%);
        color: white;
        border: none;
    }

    /* Spacing utilities */
    .space-y-4 > * + * { margin-top: 1.5rem; }
    .space-y-3 > * + * { margin-top: 1rem; }
    .space-y-2 > * + * { margin-top: 0.5rem; }

    /* Custom scrollbar */
    .custom-scroll::-webkit-scrollbar { width: 6px; }
    .custom-scroll::-webkit-scrollbar-track { background: rgba(0, 0, 0, 0.05); border-radius: 10px; }
    .custom-scroll::-webkit-scrollbar-thumb { background: rgba(0, 0, 0, 0.2); border-radius: 10px; }
    .custom-scroll::-webkit-scrollbar-thumb:hover { background: rgba(0, 0, 0, 0.3); }

    /* Responsive */
    @media (max-width: 768px) {
        .project-hero {
            padding: 2rem 1.5rem;
            margin-bottom: 1.5rem;
        }
        .project-title {
            font-size: 1.8rem;
            margin-bottom: 1rem;
        }
        .metric-number {
            font-size: 2.2rem;
        }
        .project-avatar-modern {
            width: 60px;
            height: 60px;
        }
        .project-avatar-modern i {
            font-size: 1.8rem !important;
        }
        .info-grid-item {
            padding: 1.2rem 1rem;
        }
        .info-icon {
            width: 48px;
            height: 48px;
            font-size: 1.4rem;
        }
        .info-icon-large {
            width: 64px;
            height: 64px;
            font-size: 1.6rem;
        }
        .info-icon-small {
            width: 32px;
            height: 32px;
            font-size: 1rem;
        }
        .action-btn-modern {
            padding: 1rem;
        }
        .modern-card {
            margin-bottom: 1.5rem;
        }
    }

    @media (max-width: 576px) {
        .project-hero {
            padding: 1.5rem 1rem;
        }
        .d-flex.gap-4 {
            flex-direction: column;
            gap: 1.5rem !important;
        }
        .project-avatar-modern {
            align-self: center;
        }
    }

    /* Enhanced Analytics Styles */
    .analytics-card {
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(25px);
        border: 1px solid rgba(255, 255, 255, 0.4);
        border-radius: var(--border-radius);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    /* Project Tabs Styles - Minimalistic Design */
    .project-tabs {
        background: transparent;
        margin-top: 2rem;
        margin-bottom: 1rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .project-tabs .nav-tabs {
        border: none;
        background: transparent;
        padding: 0;
        margin: 0;
        gap: 0;
    }

    .project-tabs .nav-tabs .nav-item {
        margin: 0;
    }

    .project-tabs .nav-tabs .nav-link {
        border: none;
        background: transparent;
        color: #6c757d;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        border-radius: 0;
        transition: all 0.2s ease;
        position: relative;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        text-transform: none;
        letter-spacing: 0;
        border-bottom: 2px solid transparent;
    }

    .project-tabs .nav-tabs .nav-link:hover {
        color: var(--theme-color);
        background: rgba(var(--theme-color-rgb), 0.05);
        border-bottom-color: rgba(var(--theme-color-rgb), 0.3);
    }

    .project-tabs .nav-tabs .nav-link.active {
        color: var(--theme-color);
        background: rgba(var(--theme-color-rgb), 0.08);
        border-bottom-color: var(--theme-color);
        font-weight: 600;
    }

    .project-tabs .nav-tabs .nav-link i {
        font-size: 1rem;
        transition: all 0.2s ease;
    }

    .project-tabs .tab-content {
        padding: 0;
        background: transparent;
    }

    .project-tabs .tab-pane {
        padding: 1.5rem 0;
        min-height: 300px;
    }

    /* Responsive tabs */
    @media (max-width: 768px) {
        .project-tabs .nav-tabs {
            flex-wrap: nowrap;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .project-tabs .nav-tabs .nav-link {
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
            white-space: nowrap;
            flex-shrink: 0;
        }

        .project-tabs .nav-tabs .nav-link i {
            font-size: 0.9rem;
        }

        .kanban-column {
            margin-bottom: 1rem;
        }
    }

    /* Kanban Board Styles */
    .kanban-column {
        background: #f8fafc;
        border-radius: 16px;
        padding: 16px;
        border: 1px solid rgba(0, 0, 0, 0.04);
        height: fit-content;
        transition: all 0.2s ease;
    }

    .kanban-column:hover {
        background: #f1f5f9;
    }

    .kanban-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    }

    .kanban-header h6 {
        font-weight: 600;
        color: #1f2937;
        margin: 0;
        flex: 1;
        font-weight: 600;
        font-size: 14px;
    }

    .kanban-tasks {
        display: flex;
        flex-direction: column;
        gap: 12px;
        min-height: 200px;
        padding: 8px;
    }

    .kanban-task-card {
        background: white;
        border-radius: 12px;
        padding: 16px;
        border: 1px solid #f0f0f0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        position: relative;
        margin-bottom: 12px;
        overflow: hidden;
        animation: slideInUp 0.3s ease-out;
        will-change: transform;
    }

    /* Disable transitions during drag */
    .kanban-task-card.gu-transit {
        transition: none !important;
        animation: none !important;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .kanban-task-card:hover:not(.gu-transit):not(.updating-stage) {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        border-color: #e0e0e0;
    }

    .kanban-task-card:active {
        transform: translateY(0);
        transition: transform 0.1s ease;
    }

    .task-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;
    }

    .task-title {
        font-size: 14px;
        font-weight: 600;
        color: #1a1a1a;
        line-height: 1.4;
        margin: 0;
        flex: 1;
        margin-right: 8px;
    }

    .task-title a {
        color: inherit;
        text-decoration: none;
        display: block;
    }

    .task-title a:hover {
        color: var(--theme-color);
    }

    .task-priority-badge {
        font-size: 10px;
        font-weight: 600;
        padding: 4px 8px;
        border-radius: 6px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        white-space: nowrap;
    }

    .task-priority-badge.priority-low {
        background: #f0f9ff;
        color: #0369a1;
        border: 1px solid #e0f2fe;
    }

    .task-priority-badge.priority-medium {
        background: #fefce8;
        color: #a16207;
        border: 1px solid #fef3c7;
    }

    .task-priority-badge.priority-high {
        background: #fef2f2;
        color: #dc2626;
        border: 1px solid #fecaca;
    }

    .task-priority-badge.priority-critical {
        background: #fdf2f8;
        color: #be185d;
        border: 1px solid #fce7f3;
    }

    .task-description {
        font-size: 13px;
        color: #6b7280;
        line-height: 1.5;
        margin: 0 0 16px 0;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .task-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: auto;
    }

    .task-assignees {
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .task-avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: var(--theme-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        font-weight: 600;
        border: 2px solid white;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .task-avatar:not(:first-child) {
        margin-left: -8px;
    }

    .task-date {
        font-size: 11px;
        color: #9ca3af;
        font-weight: 500;
    }

    .task-stats {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-top: 12px;
        padding-top: 12px;
        border-top: 1px solid #f3f4f6;
    }

    .task-stat {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 11px;
        color: #6b7280;
        font-weight: 500;
    }

    .task-stat i {
        font-size: 12px;
        opacity: 0.7;
    }

    .kanban-task-card.gu-mirror {
        transform: rotate(3deg) scale(1.02);
        opacity: 0.9;
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.25);
        border: 1px solid rgba(var(--theme-color-rgb), 0.3);
        z-index: 9999;
        transition: none !important;
        animation: none !important;
        cursor: grabbing !important;
    }

    /* Hide original card during drag */
    .kanban-task-card.gu-hide {
        opacity: 0.3 !important;
        transform: scale(0.95) !important;
        transition: all 0.2s ease !important;
    }

    /* Smooth return animation after drag */
    .kanban-task-card:not(.gu-transit):not(.gu-mirror) {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .task-drag-handle {
        position: absolute;
        top: 12px;
        right: 12px;
        color: #d1d5db;
        font-size: 14px;
        opacity: 0;
        transition: all 0.2s ease;
        cursor: grab;
        padding: 4px;
        border-radius: 4px;
    }

    .task-drag-handle:hover {
        background: #f3f4f6;
        color: #6b7280;
    }

    .kanban-task-card:hover:not(.gu-transit) .task-drag-handle {
        opacity: 1;
    }

    .task-drag-handle:active {
        cursor: grabbing;
        background: #e5e7eb;
    }

    /* Always show drag handle on touch devices */
    @media (hover: none) {
        .task-drag-handle {
            opacity: 0.6;
        }
    }

    /* Empty Stage Styles */
    .empty-stage {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 32px 16px;
        border: 2px dashed #e5e7eb;
        border-radius: 12px;
        background: #fafafa;
        transition: all 0.2s ease;
        margin: 8px 0;
    }

    .empty-stage:hover {
        border-color: #d1d5db;
        background: #f3f4f6;
    }

    .empty-stage-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #f3f4f6;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        color: #9ca3af;
        font-size: 18px;
    }

    .empty-stage-text {
        color: #9ca3af;
        font-size: 13px;
        font-weight: 500;
        margin: 0;
    }

    .add-task-btn {
        width: 28px;
        height: 28px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        border: 1px solid rgba(var(--theme-color-rgb), 0.15);
        background: rgba(var(--theme-color-rgb), 0.08);
        color: var(--theme-color);
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        font-size: 14px;
    }

    .add-task-btn:hover {
        background: var(--theme-color) !important;
        color: white !important;
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(var(--theme-color-rgb), 0.3);
    }

    .add-task-btn:active {
        transform: scale(0.95);
    }

    /* Files Manager Styles */
    .files-manager {
        background: white;
        border-radius: 12px;
        overflow: hidden;
    }

    .files-toolbar {
        background: #f8fafc;
        padding: 16px 20px;
        border-bottom: 1px solid #e5e7eb;
    }

    .breadcrumb {
        background: none;
        padding: 0;
        margin: 0;
        font-size: 13px;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: #9ca3af;
        margin: 0 8px;
    }

    .breadcrumb-item a {
        color: #6b7280;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: var(--theme-color);
    }

    .breadcrumb-item.active {
        color: #1f2937;
        font-weight: 500;
    }

    .files-grid {
        padding: 20px;
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 12px;
        min-height: 400px;
        transition: all 0.3s ease;
    }

    .files-grid.list-view {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .file-item, .folder-item {
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        min-height: 120px;
        max-height: 140px;
    }

    .files-grid.list-view .file-item,
    .files-grid.list-view .folder-item {
        flex-direction: row;
        align-items: center;
        text-align: left;
        min-height: 60px;
        max-height: 60px;
        padding: 12px 16px;
    }

    .file-item:hover, .folder-item:hover {
        border-color: var(--theme-color);
        box-shadow: 0 4px 12px rgba(var(--theme-color-rgb), 0.15);
        transform: translateY(-2px);
    }

    .file-item:hover .file-icon i,
    .folder-item:hover .folder-icon i {
        transform: scale(1.1);
        color: var(--theme-color) !important;
    }

    .folder-item {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    }

    .file-icon, .folder-icon {
        font-size: 32px;
        margin-bottom: 8px;
        color: #6b7280;
        flex-shrink: 0;
    }

    .folder-icon {
        color: var(--folder-color, #3b82f6);
    }

    .files-grid.list-view .file-icon,
    .files-grid.list-view .folder-icon {
        font-size: 24px;
        margin-bottom: 0;
        margin-right: 12px;
    }

    .file-name, .folder-name {
        font-size: 12px;
        font-weight: 500;
        color: #1f2937;
        margin-bottom: 4px;
        word-break: break-word;
        line-height: 1.2;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .files-grid.list-view .file-name,
    .files-grid.list-view .folder-name {
        font-size: 14px;
        margin-bottom: 2px;
        -webkit-line-clamp: 1;
        flex: 1;
    }

    .file-meta {
        font-size: 10px;
        color: #9ca3af;
        margin-top: auto;
    }

    .folder-meta {
        font-size: 10px;
        color: #6b7280;
        margin-top: auto;
    }

    .files-grid.list-view .file-meta,
    .files-grid.list-view .folder-meta {
        font-size: 12px;
        margin-top: 0;
        margin-left: auto;
        white-space: nowrap;
    }

    .item-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        min-width: 0;
    }

    .files-grid.list-view .item-content {
        flex-direction: row;
        align-items: center;
        width: 100%;
    }

    .item-details {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        min-width: 0;
    }

    .files-grid.list-view .item-details {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    .empty-files {
        grid-column: 1 / -1;
        text-align: center;
        padding: 60px 20px;
        color: #9ca3af;
    }

    .empty-files i {
        font-size: 64px;
        margin-bottom: 16px;
        opacity: 0.5;
    }

    /* Context Menu */
    .context-menu {
        position: fixed;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        min-width: 160px;
        display: none;
    }

    .context-menu-item {
        padding: 10px 16px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 13px;
        color: #374151;
        transition: background 0.2s ease;
    }

    .context-menu-item:hover {
        background: #f3f4f6;
    }

    .context-menu-item.text-danger {
        color: #dc2626;
    }

    .context-menu-item.text-danger:hover {
        background: #fef2f2;
    }

    .context-menu-divider {
        height: 1px;
        background: #e5e7eb;
        margin: 4px 0;
    }

    /* Folder Icon Selection Grid */
    .folder-icon-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 8px;
        margin-bottom: 16px;
    }

    .folder-icon-option {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 12px 8px;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        background: white;
    }

    .folder-icon-option:hover {
        border-color: var(--theme-color);
        background: rgba(var(--theme-color-rgb), 0.05);
    }

    .folder-icon-option.active {
        border-color: var(--theme-color);
        background: rgba(var(--theme-color-rgb), 0.1);
    }

    .folder-icon-option i {
        font-size: 20px;
        margin-bottom: 4px;
        color: #6b7280;
        transition: color 0.2s ease;
    }

    .folder-icon-option:hover i,
    .folder-icon-option.active i {
        color: var(--theme-color);
    }

    .folder-icon-option span {
        font-size: 10px;
        color: #6b7280;
        text-align: center;
        font-weight: 500;
    }

    .folder-icon-option:hover span,
    .folder-icon-option.active span {
        color: var(--theme-color);
    }

    /* Folder Preview */
    .folder-preview {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 16px;
        background: #f8fafc;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        margin-bottom: 8px;
    }

    .folder-preview-icon {
        margin-bottom: 8px;
    }

    .folder-preview-icon i {
        font-size: 32px;
        transition: all 0.2s ease;
    }

    .folder-preview-name {
        font-size: 12px;
        color: #6b7280;
        font-weight: 500;
    }

    /* Color Presets */
    .color-presets {
        align-items: center;
    }

    .color-preset {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        border: 2px solid white;
        cursor: pointer;
        transition: transform 0.2s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .color-preset:hover {
        transform: scale(1.1);
    }

    .color-preset.active {
        border-color: #374151;
        transform: scale(1.1);
    }

    /* Upload Progress */
    .upload-progress {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        padding: 16px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        min-width: 300px;
        z-index: 1000;
    }

    .upload-item {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;
    }

    .upload-item:last-child {
        margin-bottom: 0;
    }

    .upload-progress-bar {
        flex: 1;
        height: 4px;
        background: #e5e7eb;
        border-radius: 2px;
        overflow: hidden;
    }

    .upload-progress-fill {
        height: 100%;
        background: var(--theme-color);
        transition: width 0.3s ease;
    }

    /* Drag and Drop Styles */
    .files-grid.drag-over {
        border: 2px dashed var(--theme-color);
        background: rgba(var(--theme-color-rgb), 0.05);
        border-radius: 12px;
    }

    .files-grid.drag-over::before {
        content: '{{ __("Drop files here to upload") }}';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 18px;
        font-weight: 600;
        color: var(--theme-color);
        z-index: 10;
        pointer-events: none;
    }

    .files-grid.drag-over .file-item,
    .files-grid.drag-over .folder-item {
        opacity: 0.3;
        pointer-events: none;
    }

    /* File Type Icons Colors - Consistent with tab icons */
    .file-item .ti-file-type-pdf { color: #dc2626; }
    .file-item .ti-file-type-doc { color: #2563eb; }
    .file-item .ti-file-type-xls { color: #16a34a; }
    .file-item .ti-file-type-ppt { color: #ea580c; }
    .file-item .ti-photo { color: #7c3aed; }
    .file-item .ti-file-zip { color: #f59e0b; }
    .file-item .ti-video { color: #dc2626; }
    .file-item .ti-music { color: #16a34a; }
    .file-item .ti-file-code { color: #374151; }
    .file-item .ti-file-text { color: #6b7280; }
    .file-item .ti-file { color: #9ca3af; }

    /* Folder Icons */
    .folder-item .ti-folder-filled {
        transition: all 0.2s ease;
    }

    .folder-item:hover .ti-folder-filled {
        transform: scale(1.05);
    }

    /* Icon consistency with tabs */
    .file-icon i, .folder-icon i {
        font-size: inherit;
        transition: all 0.2s ease;
        font-weight: 400;
        line-height: 1;
    }

    .files-grid.list-view .file-icon i,
    .files-grid.list-view .folder-icon i {
        font-size: inherit;
    }

    /* Toolbar icons consistency */
    .files-toolbar .btn i {
        font-size: 1rem;
        transition: all 0.2s ease;
        font-weight: 400;
    }

    .files-toolbar .btn:hover i {
        transform: scale(1.05);
    }

    /* Context menu icons */
    .context-menu-item i {
        font-size: 0.9rem;
        font-weight: 400;
        width: 16px;
        text-align: center;
    }

    /* Breadcrumb icons */
    .breadcrumb-item i {
        font-size: 0.9rem;
        font-weight: 400;
    }

    /* View Toggle Styles */
    .btn-group .btn {
        border-radius: 6px !important;
    }

    .btn-group .btn:first-child {
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
    }

    .btn-group .btn:last-child {
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
    }

    .vr {
        width: 1px;
        height: 24px;
        background-color: #e5e7eb;
        margin: 0 8px;
    }

    /* Empty State Improvements */
    .empty-files {
        grid-column: 1 / -1;
        text-align: center;
        padding: 60px 20px;
        color: #9ca3af;
        background: #fafafa;
        border: 2px dashed #e5e7eb;
        border-radius: 12px;
        margin: 20px;
    }

    .empty-files i {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.5;
    }

    .empty-files h6 {
        color: #6b7280;
        margin-bottom: 8px;
    }

    /* Hover Effects for List View */
    .files-grid.list-view .file-item:hover,
    .files-grid.list-view .folder-item:hover {
        background: #f8fafc;
        transform: none;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    /* File Type Specific Styling */
    .file-item[data-type="file"] .file-icon {
        background: #f8fafc;
        border-radius: 6px;
        padding: 6px;
        margin-bottom: 6px;
    }

    .files-grid.list-view .file-item[data-type="file"] .file-icon {
        margin-bottom: 0;
        margin-right: 12px;
        padding: 4px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .files-grid {
            grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
            gap: 10px;
            padding: 16px;
        }

        .files-grid.list-view {
            grid-template-columns: 1fr;
        }

        .files-toolbar {
            padding: 12px 16px;
        }

        .files-toolbar .d-flex {
            flex-direction: column;
            gap: 12px;
            align-items: stretch !important;
        }

        .breadcrumb {
            font-size: 12px;
        }

        .file-item, .folder-item {
            min-height: 100px;
            max-height: 120px;
        }

        .files-grid.list-view .file-item,
        .files-grid.list-view .folder-item {
            min-height: 50px;
            max-height: 50px;
            padding: 8px 12px;
        }

        .file-name, .folder-name {
            font-size: 11px;
        }

        .files-grid.list-view .file-name,
        .files-grid.list-view .folder-name {
            font-size: 13px;
        }
    }

    @media (max-width: 480px) {
        .files-grid {
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 8px;
            padding: 12px;
        }

        .files-toolbar .btn {
            font-size: 12px;
            padding: 6px 12px;
        }

        .files-toolbar .btn-group .btn {
            padding: 6px 8px;
        }
    }

    /* Dragula specific styles */
    .gu-mirror {
        position: fixed !important;
        margin: 0 !important;
        z-index: 9999 !important;
        opacity: 0.9;
        pointer-events: none;
        transform: rotate(3deg) scale(1.02);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.25);
    }

    .gu-hide {
        opacity: 0.3 !important;
        transform: scale(0.95) !important;
    }

    .gu-unselectable {
        -webkit-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
        user-select: none !important;
    }

    .gu-transit {
        opacity: 0.3 !important;
        transition: none !important;
        animation: none !important;
    }

    /* Body state during dragging */
    body.dragging {
        cursor: grabbing !important;
        user-select: none;
    }

    body.dragging * {
        cursor: grabbing !important;
    }

    /* Prevent hover effects during drag */
    body.dragging .kanban-task-card:hover {
        transform: none !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04) !important;
    }

    .kanban-tasks.gu-over {
        background: rgba(var(--theme-color-rgb), 0.08);
        border-radius: 12px;
        min-height: 120px;
        border: 2px dashed rgba(var(--theme-color-rgb), 0.3);
        transition: all 0.2s ease;
    }

    .kanban-task-card.updating-stage {
        position: relative;
        opacity: 0.7;
        transform: scale(0.98);
        transition: all 0.3s ease;
    }

    .kanban-task-card.updating-stage::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 12px;
        z-index: 1;
        backdrop-filter: blur(1px);
    }

    .kanban-task-card.updating-stage::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 24px;
        height: 24px;
        margin: -12px 0 0 -12px;
        border: 2px solid #e5e7eb;
        border-top: 2px solid var(--theme-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        z-index: 2;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .task-title {
        font-size: 0.9rem;
        font-weight: 600;
        color: #2d3748;
        line-height: 1.3;
    }

    .task-description {
        font-size: 0.8rem;
        color: #718096;
        margin: 0.5rem 0;
        line-height: 1.4;
    }

    .task-meta {
        margin-top: 0.75rem;
    }

    .task-assignees {
        display: flex;
        gap: 0.25rem;
    }

    .user-avatar-sm {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: var(--theme-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.7rem;
        font-weight: 600;
    }

    .priority-badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .priority-critical {
        background: #fed7d7;
        color: #c53030;
    }

    .priority-high {
        background: #feebc8;
        color: #dd6b20;
    }

    .priority-medium {
        background: #bee3f8;
        color: #3182ce;
    }

    .priority-low {
        background: #c6f6d5;
        color: #38a169;
    }

    /* Task List Styles */
    .task-checkbox input[type="checkbox"] {
        width: 16px;
        height: 16px;
        border-radius: 3px;
    }

    .progress-sm {
        width: 60px;
        height: 4px;
        background: #e2e8f0;
        border-radius: 2px;
        overflow: hidden;
    }

    .progress-sm .progress-bar {
        height: 100%;
        background: var(--theme-color);
        transition: width 0.3s ease;
    }

    /* File Card Styles */
    .file-card {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        border: 1px solid rgba(0, 0, 0, 0.06);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
        text-align: center;
    }

    .file-card:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .file-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        background: rgba(var(--theme-color-rgb), 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 0.75rem;
    }

    .file-icon i {
        font-size: 1.5rem;
        color: var(--theme-color);
    }

    .file-name {
        font-size: 0.9rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.25rem;
    }

    .analytics-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, var(--theme-color), rgba(var(--theme-color-rgb), 0.5), var(--theme-color));
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .analytics-card:hover::before {
        opacity: 1;
    }

    .analytics-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    }

    /* Chart Container Enhancements */
    .chart-container {
        position: relative;
        background: rgba(var(--theme-color-rgb), 0.02);
        border-radius: var(--border-radius-sm);
        padding: 1rem;
        margin: 1rem 0;
        border: 1px solid rgba(var(--theme-color-rgb), 0.1);
    }

    .chart-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
        border-radius: var(--border-radius-sm);
        pointer-events: none;
    }

    /* KPI Card Styles */
    .kpi-card {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--border-radius-sm);
        padding: 1.5rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .kpi-card::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(180deg, var(--theme-color), rgba(var(--theme-color-rgb), 0.3));
        transform: scaleY(0);
        transition: transform 0.3s ease;
    }

    .kpi-card:hover::after {
        transform: scaleY(1);
    }

    .kpi-card:hover {
        transform: translateX(4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    /* Statistics Grid */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
        margin: 1.5rem 0;
    }

    .stat-item {
        text-align: center;
        padding: 1.2rem 0.8rem;
        background: rgba(var(--theme-color-rgb), 0.05);
        border-radius: var(--border-radius-sm);
        border: 1px solid rgba(var(--theme-color-rgb), 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: var(--theme-color);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .stat-item:hover::before {
        transform: scaleX(1);
    }

    .stat-item:hover {
        transform: translateY(-2px);
        background: rgba(var(--theme-color-rgb), 0.1);
    }

    .stat-value {
        font-size: 1.8rem;
        font-weight: 800;
        color: var(--theme-color);
        line-height: 1;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        font-size: 0.875rem;
        color: #64748b;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Entrance Animations */
    .modern-card, .info-grid-item, .metric-card, .analytics-card {
        animation: fadeInUp 0.6s ease-out forwards;
        opacity: 0;
        transform: translateY(20px);
    }

    .modern-card:nth-child(1) { animation-delay: 0.1s; }
    .modern-card:nth-child(2) { animation-delay: 0.2s; }
    .modern-card:nth-child(3) { animation-delay: 0.3s; }
    .info-grid-item:nth-child(1) { animation-delay: 0.1s; }
    .info-grid-item:nth-child(2) { animation-delay: 0.2s; }
    .info-grid-item:nth-child(3) { animation-delay: 0.3s; }
    .info-grid-item:nth-child(4) { animation-delay: 0.4s; }

    @keyframes fadeInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Chart Loading Animation */
    @keyframes chartPulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.7;
        }
    }

    .chart-loading {
        animation: chartPulse 1.5s ease-in-out infinite;
    }

    /* Team Members Styles */
    .member-item {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .member-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .member-item:hover::before {
        opacity: 1;
    }

    .member-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        background: rgba(255, 255, 255, 0.9) !important;
    }

    .user-avatar-text {
        transition: all 0.3s ease;
    }

    .user-avatar-text:hover {
        transform: translateY(-1px) scale(1.05);
        box-shadow: 0 6px 20px rgba(var(--theme-color-rgb), 0.4) !important;
    }

    /* Loading placeholder improvements */
    #members-placeholder {
        transition: opacity 0.3s ease;
    }

    .members-container {
        position: relative;
    }

    /* Improved action buttons */
    .member-item .btn-outline-danger:hover {
        background: #dc3545 !important;
        color: white !important;
        border-color: #dc3545 !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
    }
</style>
@endpush

@push('script-page')
    <script>
        // Enhanced Analytics Charts
        document.addEventListener('DOMContentLoaded', function() {
            // Task Completion Donut Chart
            (function() {
                var completedTasks = {{ $project_data['task']['done'] }};
                var totalTasks = {{ $project_data['task']['total'] }};
                var remainingTasks = totalTasks - completedTasks;

                var options = {
                    chart: {
                        type: 'donut',
                        height: 200,
                        animations: {
                            enabled: true,
                            easing: 'easeinout',
                            speed: 800,
                        }
                    },
                    series: [completedTasks, remainingTasks],
                    labels: ['{{ __("Completed") }}', '{{ __("Remaining") }}'],
                    colors: ['var(--theme-color)', 'rgba(var(--theme-color-rgb), 0.2)'],
                    dataLabels: {
                        enabled: true,
                        formatter: function (val, opts) {
                            return opts.w.config.series[opts.seriesIndex]
                        },
                        style: {
                            fontSize: '14px',
                            fontWeight: 'bold'
                        }
                    },
                    plotOptions: {
                        pie: {
                            donut: {
                                size: '70%',
                                labels: {
                                    show: true,
                                    total: {
                                        show: true,
                                        label: '{{ __("Total Tasks") }}',
                                        fontSize: '12px',
                                        color: '#666',
                                        formatter: function () {
                                            return totalTasks
                                        }
                                    },
                                    value: {
                                        show: true,
                                        fontSize: '20px',
                                        fontWeight: 'bold',
                                        color: 'var(--theme-color)'
                                    }
                                }
                            }
                        }
                    },
                    legend: {
                        position: 'bottom',
                        fontSize: '12px'
                    },
                    tooltip: {
                        y: {
                            formatter: function(val) {
                                return val + ' {{ __("tasks") }}'
                            }
                        }
                    }
                };
                var taskCompletionChart = new ApexCharts(document.querySelector("#task_completion_chart"), options);
                taskCompletionChart.render();
            })();

            // Task Trend Chart (7-day sparkline)
            (function() {
                var options = {
                    chart: {
                        type: 'area',
                        height: 60,
                        sparkline: {
                            enabled: true,
                        },
                        animations: {
                            enabled: true,
                            easing: 'easeinout',
                            speed: 800,
                        }
                    },
                    colors: ["var(--theme-color)"],
                    dataLabels: {
                        enabled: false
                    },
                    stroke: {
                        curve: 'smooth',
                        width: 3,
                    },
                    fill: {
                        type: 'gradient',
                        gradient: {
                            shadeIntensity: 1,
                            opacityFrom: 0.7,
                            opacityTo: 0.1,
                            stops: [0, 90, 100]
                        }
                    },
                    series: [{
                        name: '{{ __("Tasks Completed") }}',
                        data: {{ json_encode($project_data['task_chart']['chart']) }}
                    }],
                    tooltip: {
                        followCursor: false,
                        fixed: {
                            enabled: false
                        },
                        x: {
                            show: false
                        },
                        y: {
                            title: {
                                formatter: function(seriesName) {
                                    return seriesName + ': '
                                }
                            }
                        },
                        marker: {
                            show: false
                        }
                    }
                };
                var taskTrendChart = new ApexCharts(document.querySelector("#task_trend_chart"), options);
                taskTrendChart.render();
            })();

            // Time Distribution Chart (Radial Bar)
            (function() {
                var timeSpentPercentage = {{ $project_data['time_spent']['percentage'] }};
                var allocatedPercentage = {{ $project_data['task_allocated_hrs']['percentage'] }};

                var options = {
                    chart: {
                        type: 'radialBar',
                        height: 200,
                        animations: {
                            enabled: true,
                            easing: 'easeinout',
                            speed: 800,
                        }
                    },
                    plotOptions: {
                        radialBar: {
                            offsetY: 0,
                            startAngle: 0,
                            endAngle: 270,
                            hollow: {
                                margin: 5,
                                size: '30%',
                                background: 'transparent',
                            },
                            dataLabels: {
                                name: {
                                    show: false,
                                },
                                value: {
                                    show: false,
                                }
                            }
                        }
                    },
                    colors: ['var(--theme-color)', 'rgba(var(--theme-color-rgb), 0.5)'],
                    series: [timeSpentPercentage, allocatedPercentage],
                    labels: ['{{ __("Time Spent") }}', '{{ __("Allocated") }}'],
                    legend: {
                        show: true,
                        floating: true,
                        fontSize: '12px',
                        position: 'left',
                        offsetX: 50,
                        offsetY: 10,
                        labels: {
                            useSeriesColors: true,
                        },
                        formatter: function(seriesName, opts) {
                            return seriesName + ": " + opts.w.globals.series[opts.seriesIndex] + '%'
                        },
                        itemMargin: {
                            horizontal: 3,
                        }
                    },
                    responsive: [{
                        breakpoint: 480,
                        options: {
                            legend: {
                                show: false
                            }
                        }
                    }]
                };
                var timeDistributionChart = new ApexCharts(document.querySelector("#time_distribution_chart"), options);
                timeDistributionChart.render();
            })();

            // Time Trend Chart (7-day sparkline)
            (function() {
                var options = {
                    chart: {
                        type: 'line',
                        height: 60,
                        sparkline: {
                            enabled: true,
                        },
                        animations: {
                            enabled: true,
                            easing: 'easeinout',
                            speed: 800,
                        }
                    },
                    colors: ["var(--theme-color)"],
                    dataLabels: {
                        enabled: false
                    },
                    stroke: {
                        curve: 'smooth',
                        width: 3,
                    },
                    series: [{
                        name: '{{ __("Hours") }}',
                        data: {{ json_encode(array_map('floatval', $project_data['timesheet_chart']['chart'])) }}
                    }],
                    tooltip: {
                        followCursor: false,
                        fixed: {
                            enabled: false
                        },
                        x: {
                            show: false
                        },
                        y: {
                            title: {
                                formatter: function(seriesName) {
                                    return seriesName + ': '
                                }
                            },
                            formatter: function(val) {
                                return val + 'h'
                            }
                        },
                        marker: {
                            show: false
                        }
                    }
                };
                var timeTrendChart = new ApexCharts(document.querySelector("#time_trend_chart"), options);
                timeTrendChart.render();
            })();

        });

        $(document).ready(function() {
            loadProjectUser();
            initializeKanbanDrag();

            // Tab switching is now handled by Bootstrap without AJAX

            $(document).on('click', '.invite_usr', function() {
                var project_id = $('#project_id').val();
                var user_id = $(this).attr('data-id');

                $.ajax({
                    url: '{{ route('invite.project.user.member') }}',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        'project_id': project_id,
                        'user_id': user_id,
                        "_token": "{{ csrf_token() }}"
                    },
                    success: function(data) {
                        if (data.code == '200') {
                            show_toastr(data.status, data.success, 'success')
                            setInterval('location.reload()', 5000);
                            loadProjectUser();
                        } else if (data.code == '404') {
                            show_toastr(data.status, data.errors, 'error')
                        }
                    }
                });
            });
        });

        function loadProjectUser() {
            var mainEle = $('#project_users');
            var placeholderEle = $('#members-placeholder');
            var project_id = '{{ $project->id }}';

            $.ajax({
                url: '{{ route('project.user') }}',
                data: {
                    project_id: project_id
                },
                beforeSend: function() {
                    placeholderEle.show();
                    mainEle.html('');
                },
                success: function(data) {
                    placeholderEle.hide();
                    mainEle.html(data.html);
                    $('[id^=fire-modal]').remove();

                    // Initialize tooltips for new content
                    $('[data-bs-toggle="tooltip"]').tooltip();
                },
                error: function() {
                    placeholderEle.hide();
                    mainEle.html('<div class="text-center py-4"><div class="text-danger"><i class="ti ti-alert-circle"></i></div><h6 class="text-muted mt-2">{{ __('Failed to load team members') }}</h6></div>');
                }
            });
        }

        // Initialize Kanban Drag and Drop
        var dragulaInstance = null;

        function initializeKanbanDrag() {
            if (typeof dragula !== 'undefined' && $('.kanban-wrapper').length > 0) {
                var containers = [];
                $('.kanban-tasks').each(function() {
                    containers.push(this);
                });

                dragulaInstance = dragula(containers, {
                    moves: function (el, container, handle) {
                        return $(el).hasClass('task-card-draggable') && !$(el).hasClass('updating-stage');
                    },
                    accepts: function (el, target, source, sibling) {
                        return $(target).hasClass('kanban-tasks');
                    },
                    removeOnSpill: false,
                    revertOnSpill: true,
                    direction: 'vertical'
                });

                // Add drag event handlers for better UX
                dragulaInstance.on('drag', function(el, source) {
                    $(el).addClass('gu-transit');
                    $('body').addClass('dragging');
                });

                dragulaInstance.on('dragend', function(el) {
                    $(el).removeClass('gu-transit');
                    $('body').removeClass('dragging');
                });

                dragulaInstance.on('cancel', function(el, container, source) {
                    $(el).removeClass('gu-transit');
                    $('body').removeClass('dragging');
                });

                dragulaInstance.on('drop', function(el, target, source, sibling) {
                    // Remove drag classes
                    $(el).removeClass('gu-transit');
                    $('body').removeClass('dragging');

                    var taskId = $(el).data('task-id');
                    var newStageId = $(target).data('stage-id');
                    var oldStageId = $(source).data('stage-id');

                    if (newStageId !== oldStageId) {
                        // Prevent further dragging of this element during update
                        $(el).addClass('updating-stage');

                        // Store the current position for potential revert
                        var currentPosition = {
                            element: el,
                            target: target,
                            source: source,
                            sibling: sibling
                        };

                        // Update the task's data attribute to reflect new stage
                        $(el).data('stage-id', newStageId);

                        // Update stage counters immediately
                        updateStageCounters();

                        // Send AJAX request to update database
                        updateTaskStage(taskId, newStageId, oldStageId, currentPosition);
                    }
                });
            }
        }

        // Update task stage via AJAX
        function updateTaskStage(taskId, newStageId, oldStageId, position) {
            var taskElement = position.element;
            var targetContainer = position.target;
            var sourceContainer = position.source;

            // Add loading state to task card
            $(taskElement).addClass('updating-stage').css('opacity', '0.7');

            $.ajax({
                url: '{{ route("projects.tasks.update.stage") }}',
                method: 'POST',
                data: {
                    task_id: taskId,
                    stage_id: newStageId,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        show_toastr('Success', response.message, 'success');

                        // Refresh the kanban board content
                        refreshKanbanBoard();

                        // Also refresh task list if it's active
                        if ($('#task-list-tab').hasClass('active')) {
                            setTimeout(function() {
                                refreshTaskList();
                            }, 500);
                        }
                    } else {
                        // Remove loading state
                        $(taskElement).removeClass('updating-stage').css('opacity', '1');
                        show_toastr('Error', response.message, 'error');
                        // Revert the task to original position
                        revertTaskPosition(taskElement, sourceContainer, oldStageId);
                    }
                },
                error: function(xhr, status, error) {
                    // Remove loading state
                    $(taskElement).removeClass('updating-stage').css('opacity', '1');

                    show_toastr('Error', '{{ __("Failed to update task stage") }}', 'error');
                    // Revert the task to original position
                    revertTaskPosition(taskElement, sourceContainer, oldStageId);
                }
            });
        }

        // Revert task position if update fails
        function revertTaskPosition(taskElement, sourceContainer, oldStageId) {
            // Move the task back to its original container
            $(sourceContainer).append(taskElement);
            // Update the task's data attribute back to old stage
            $(taskElement).data('stage-id', oldStageId);
            // Update counters
            updateStageCounters();
        }

        // Refresh kanban board content
        function refreshKanbanBoard() {
            var kanbanContainer = $('#task-kanban');

            $.ajax({
                url: '{{ route("projects.show", $project->id) }}',
                method: 'GET',
                data: {
                    'refresh_kanban': true
                },
                beforeSend: function() {
                    kanbanContainer.html('<div class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">{{ __("Refreshing kanban board...") }}</span></div><p class="mt-2 text-muted">{{ __("Updating task positions...") }}</p></div>');
                },
                success: function(data) {
                    // Since we're now returning just the kanban content, use it directly
                    if (data && data.trim().length > 0) {
                        kanbanContainer.html(data);

                        // Reinitialize drag and drop after content is loaded
                        setTimeout(function() {
                            if (dragulaInstance) {
                                dragulaInstance.destroy();
                            }
                            initializeKanbanDrag();

                            // Initialize tooltips for new elements
                            $('[data-bs-toggle="tooltip"]').tooltip();
                        }, 200);
                    } else {
                        kanbanContainer.html('<div class="text-center py-5"><div class="text-danger"><i class="ti ti-alert-circle"></i></div><h6 class="text-muted mt-2">{{ __("Failed to refresh kanban board") }}</h6></div>');
                    }
                },
                error: function() {
                    kanbanContainer.html('<div class="text-center py-5"><div class="text-danger"><i class="ti ti-alert-circle"></i></div><h6 class="text-muted mt-2">{{ __("Failed to refresh kanban board") }}</h6></div>');
                }
            });
        }

        // Update stage counters
        function updateStageCounters() {
            $('.kanban-tasks').each(function() {
                var stageId = $(this).data('stage-id');
                var taskCount = $(this).find('.kanban-task-card').length;
                $(this).closest('.kanban-column').find('.badge').text(taskCount);
            });
        }

        // Refresh task list content
        function refreshTaskList() {
            var taskListContainer = $('#task-list-content');

            $.ajax({
                url: '{{ route("projects.show", $project->id) }}',
                method: 'GET',
                data: {
                    'refresh_task_list': true
                },
                beforeSend: function() {
                    taskListContainer.html('<div class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">{{ __("Loading tasks...") }}</span></div><p class="mt-2 text-muted">{{ __("Fetching latest task data...") }}</p></div>');
                },
                success: function(data) {
                    if (data && data.trim().length > 0) {
                        taskListContainer.html(data);

                        // Initialize tooltips for new elements
                        $('[data-bs-toggle="tooltip"]').tooltip();
                    } else {
                        taskListContainer.html('<div class="text-center py-5"><div class="text-danger"><i class="ti ti-alert-circle"></i></div><h6 class="text-muted mt-2">{{ __("Failed to load task list") }}</h6></div>');
                    }
                },
                error: function() {
                    taskListContainer.html('<div class="text-center py-5"><div class="text-danger"><i class="ti ti-alert-circle"></i></div><h6 class="text-muted mt-2">{{ __("Failed to load task list") }}</h6></div>');
                }
            });
        }



        // Variable to track if task was saved
        var taskWasSaved = false;

        // Refresh kanban after task operations only if task was saved
        $(document).on('hidden.bs.modal', '#commonModal', function() {
            // Only refresh if task was actually saved
            if (taskWasSaved) {
                if ($('#task-kanban-tab').hasClass('active')) {
                    setTimeout(function() {
                        refreshKanbanBoard();
                    }, 300);
                }
                // Also refresh task list if it's active
                if ($('#task-list-tab').hasClass('active')) {
                    setTimeout(function() {
                        refreshTaskList();
                    }, 300);
                }
            }
            // Reset the flag
            taskWasSaved = false;
        });

        // Handle successful task creation/update via AJAX
        $(document).on('submit', '#commonModal form', function(e) {
            var form = $(this);
            var actionUrl = form.attr('action');

            // Check if this is a task-related form
            if (actionUrl && (actionUrl.includes('tasks') || actionUrl.includes('task'))) {
                e.preventDefault();

                $.ajax({
                    url: actionUrl,
                    method: form.attr('method') || 'POST',
                    data: form.serialize(),
                    success: function(response) {
                        // Set flag to indicate task was saved successfully
                        taskWasSaved = true;

                        $('#commonModal').modal('hide');
                        if (response.success !== false) {
                            show_toastr('Success', response.message || '{{ __("Task updated successfully") }}', 'success');
                        }
                    },
                    error: function(xhr) {
                        var response = xhr.responseJSON;
                        if (response && response.message) {
                            show_toastr('Error', response.message, 'error');
                        } else {
                            show_toastr('Error', '{{ __("An error occurred") }}', 'error');
                        }
                    }
                });
            }
        });

        // Handle successful task operations from standard ajax-popup system
        $(document).on('click', '#commonModal .btn[type="submit"]', function() {
            var form = $(this).closest('form');
            var actionUrl = form.attr('action');

            // Check if this is a task-related form
            if (actionUrl && (actionUrl.includes('tasks') || actionUrl.includes('task'))) {
                // Set a timeout to check if the modal closes successfully (indicating success)
                setTimeout(function() {
                    if (!$('#commonModal').hasClass('show')) {
                        taskWasSaved = true;
                    }
                }, 1000);
            }
        });

        // Also handle when forms are submitted successfully via standard ajax-popup
        $(document).ajaxSuccess(function(event, xhr, settings) {
            // Check if this was a task-related request
            if (settings.url && (settings.url.includes('tasks') || settings.url.includes('task'))) {
                // Check if it was a POST/PUT request (create/update)
                if (settings.type === 'POST' || settings.type === 'PUT') {
                    taskWasSaved = true;
                }
            }
        });

        // Handle task list tab click to refresh data
        $(document).on('click', '#task-list-tab', function() {
            // Small delay to ensure tab is active before refreshing
            setTimeout(function() {
                if ($('#task-list-tab').hasClass('active')) {
                    refreshTaskList();
                }
            }, 100);
        });

        // Also refresh task list when tasks are saved (similar to kanban)
        var taskListNeedsRefresh = false;

        // Set flag when task operations complete successfully
        $(document).ajaxSuccess(function(event, xhr, settings) {
            if (settings.url && (settings.url.includes('tasks') || settings.url.includes('task'))) {
                if (settings.type === 'POST' || settings.type === 'PUT' || settings.type === 'DELETE') {
                    taskListNeedsRefresh = true;
                }
            }
        });

        // Refresh task list when modal closes if needed
        $(document).on('hidden.bs.modal', '#commonModal', function() {
            if (taskListNeedsRefresh && $('#task-list-tab').hasClass('active')) {
                setTimeout(function() {
                    refreshTaskList();
                }, 300);
            }
            taskListNeedsRefresh = false;
        });

        // File Management Variables
        let currentFolderId = null;
        let selectedItem = null;
        let selectedItemType = null;

        // Load files when Files tab is clicked
        $(document).on('click', '#files-tab', function() {
            setTimeout(function() {
                if ($('#files-tab').hasClass('active')) {
                    loadFiles(currentFolderId);
                }
            }, 100);
        });

        // File Management Functions
        function loadFiles(folderId = null) {
            currentFolderId = folderId;

            $.ajax({
                url: '{{ route("projects.files.index", $project->id) }}',
                method: 'GET',
                data: { folder_id: folderId },
                beforeSend: function() {
                    $('#files-grid').html(`
                        <div class="text-center py-5" style="grid-column: 1 / -1;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">{{ __("Loading...") }}</span>
                            </div>
                            <p class="mt-2 text-muted">{{ __("Loading files...") }}</p>
                        </div>
                    `);
                },
                success: function(response) {
                    updateBreadcrumb(response.breadcrumb);
                    renderFilesGrid(response.folders, response.files);
                },
                error: function() {
                    $('#files-grid').html(`
                        <div class="text-center py-5 text-danger" style="grid-column: 1 / -1;">
                            <i class="ti ti-alert-circle" style="font-size: 3rem;"></i>
                            <h6 class="mt-2">{{ __("Failed to load files") }}</h6>
                        </div>
                    `);
                }
            });
        }

        function updateBreadcrumb(breadcrumb) {
            let breadcrumbHtml = `
                <li class="breadcrumb-item">
                    <a href="#" onclick="navigateToFolder(null)" class="text-decoration-none">
                        <i class="ti ti-home"></i> {{ __('Root') }}
                    </a>
                </li>
            `;

            if (breadcrumb && breadcrumb.length > 0) {
                breadcrumb.forEach(function(folder, index) {
                    if (index === breadcrumb.length - 1) {
                        breadcrumbHtml += `<li class="breadcrumb-item active">${folder.name}</li>`;
                    } else {
                        breadcrumbHtml += `
                            <li class="breadcrumb-item">
                                <a href="#" onclick="navigateToFolder(${folder.id})" class="text-decoration-none">
                                    ${folder.name}
                                </a>
                            </li>
                        `;
                    }
                });
            }

            $('#files-breadcrumb').html(breadcrumbHtml);
        }

        function renderFilesGrid(folders, files) {
            let html = '';

            // Render folders
            folders.forEach(function(folder) {
                const folderIcon = folder.icon || 'ti-folder-filled';
                html += `
                    <div class="folder-item" data-id="${folder.id}" data-type="folder"
                         ondblclick="navigateToFolder(${folder.id})"
                         oncontextmenu="showContextMenu(event, ${folder.id}, 'folder')"
                         style="--folder-color: ${folder.color}">
                        <div class="item-content">
                            <div class="folder-icon">
                                <i class="ti ${folderIcon}" style="color: ${folder.color}"></i>
                            </div>
                            <div class="item-details">
                                <div class="folder-name">${folder.name}</div>
                                <div class="folder-meta">${folder.total_files_count || 0} items</div>
                            </div>
                        </div>
                    </div>
                `;
            });

            // Render files
            files.forEach(function(file) {
                html += `
                    <div class="file-item" data-id="${file.id}" data-type="file"
                         ondblclick="downloadFile(${file.id})"
                         oncontextmenu="showContextMenu(event, ${file.id}, 'file')">
                        <div class="item-content">
                            <div class="file-icon">
                                <i class="${getFileIcon(file.file_type)}"></i>
                            </div>
                            <div class="item-details">
                                <div class="file-name">${file.name}</div>
                                <div class="file-meta">${file.formatted_size}</div>
                            </div>
                        </div>
                    </div>
                `;
            });

            if (html === '') {
                html = `
                    <div class="empty-files">
                        <i class="ti ti-folder-open"></i>
                        <h6>{{ __('No files or folders') }}</h6>
                        <p class="text-muted">{{ __('This folder is empty. Upload files or create folders to get started.') }}</p>
                    </div>
                `;
            }

            $('#files-grid').html(html);
        }

        function getFileIcon(fileType) {
            const icons = {
                // Documents
                'pdf': 'ti ti-file-type-pdf',
                'doc': 'ti ti-file-type-doc',
                'docx': 'ti ti-file-type-doc',
                'xls': 'ti ti-file-type-xls',
                'xlsx': 'ti ti-file-type-xls',
                'ppt': 'ti ti-file-type-ppt',
                'pptx': 'ti ti-file-type-ppt',
                'txt': 'ti ti-file-text',
                'rtf': 'ti ti-file-text',

                // Images
                'jpg': 'ti ti-photo',
                'jpeg': 'ti ti-photo',
                'png': 'ti ti-photo',
                'gif': 'ti ti-photo',
                'svg': 'ti ti-photo',
                'webp': 'ti ti-photo',
                'bmp': 'ti ti-photo',
                'ico': 'ti ti-photo',

                // Archives
                'zip': 'ti ti-file-zip',
                'rar': 'ti ti-file-zip',
                '7z': 'ti ti-file-zip',
                'tar': 'ti ti-file-zip',
                'gz': 'ti ti-file-zip',

                // Media
                'mp4': 'ti ti-video',
                'avi': 'ti ti-video',
                'mov': 'ti ti-video',
                'wmv': 'ti ti-video',
                'flv': 'ti ti-video',
                'mp3': 'ti ti-music',
                'wav': 'ti ti-music',
                'flac': 'ti ti-music',
                'aac': 'ti ti-music',

                // Code
                'html': 'ti ti-file-code',
                'css': 'ti ti-file-code',
                'js': 'ti ti-file-code',
                'php': 'ti ti-file-code',
                'py': 'ti ti-file-code',
                'java': 'ti ti-file-code',
                'cpp': 'ti ti-file-code',
                'c': 'ti ti-file-code',
                'json': 'ti ti-file-code',
                'xml': 'ti ti-file-code'
            };
            return icons[fileType.toLowerCase()] || 'ti ti-file';
        }

        // Get folder icon names for display
        function getFolderIconName(icon) {
            const names = {
                'ti-folder-filled': '{{ __("Default") }}',
                'ti-folder-code': '{{ __("Code") }}',
                'ti-photo': '{{ __("Images") }}',
                'ti-video': '{{ __("Videos") }}',
                'ti-music': '{{ __("Audio") }}',
                'ti-file-text': '{{ __("Documents") }}',
                'ti-archive': '{{ __("Archive") }}',
                'ti-settings': '{{ __("Settings") }}',
                'ti-users': '{{ __("Team") }}',
                'ti-star': '{{ __("Important") }}',
                'ti-lock': '{{ __("Private") }}',
                'ti-share': '{{ __("Shared") }}',
                'ti-calendar': '{{ __("Events") }}',
                'ti-briefcase': '{{ __("Work") }}',
                'ti-heart': '{{ __("Favorites") }}',
                'ti-download': '{{ __("Downloads") }}'
            };
            return names[icon] || '{{ __("Custom") }}';
        }

        function navigateToFolder(folderId) {
            loadFiles(folderId);
        }

        function refreshFiles() {
            loadFiles(currentFolderId);
        }

        // View Toggle Functions
        let currentView = 'grid'; // Default view

        function toggleView(view) {
            currentView = view;
            const filesGrid = $('#files-grid');
            const gridBtn = $('#grid-view-btn');
            const listBtn = $('#list-view-btn');

            // Add fade effect during transition
            filesGrid.css('opacity', '0.7');

            setTimeout(function() {
                if (view === 'list') {
                    filesGrid.addClass('list-view');
                    listBtn.addClass('active');
                    gridBtn.removeClass('active');
                    localStorage.setItem('files-view-preference', 'list');
                } else {
                    filesGrid.removeClass('list-view');
                    gridBtn.addClass('active');
                    listBtn.removeClass('active');
                    localStorage.setItem('files-view-preference', 'grid');
                }

                // Restore opacity
                filesGrid.css('opacity', '1');
            }, 150);
        }

        // Load saved view preference
        function loadViewPreference() {
            const savedView = localStorage.getItem('files-view-preference') || 'grid';
            toggleView(savedView);
        }

        // Initialize view preference when files tab is first loaded
        $(document).ready(function() {
            loadViewPreference();
        });

        // Create Folder
        function createFolder() {
            // Reset form
            $('#create-folder-form')[0].reset();
            $('#folder-icon-input').val('ti-folder-filled');
            $('.folder-icon-option').removeClass('active');
            $('.folder-icon-option[data-icon="ti-folder-filled"]').addClass('active');
            $('#folder-color').val('#3b82f6');
            $('.color-preset').removeClass('active');
            $('.color-preset[data-color="#3b82f6"]').addClass('active');

            $('#createFolderModal').modal('show');
            $('#folder-name').focus();
        }

        $('#create-folder-form').on('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            // Only append parent_folder_id if it's not null
            if (currentFolderId !== null) {
                formData.append('parent_folder_id', currentFolderId);
            }

            $.ajax({
                url: '{{ route("projects.folders.create", $project->id) }}',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    $('#createFolderModal').modal('hide');
                    $('#create-folder-form')[0].reset();
                    show_toastr('Success', response.message, 'success');
                    loadFiles(currentFolderId);
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    console.error('Folder creation error:', response);

                    let errorMessage = '{{ __("Failed to create folder") }}';
                    if (response && response.message) {
                        errorMessage = response.message;
                    } else if (response && response.errors) {
                        // Handle validation errors
                        const errors = Object.values(response.errors).flat();
                        errorMessage = errors.join(', ');
                    }

                    show_toastr('Error', errorMessage, 'error');
                }
            });
        });

        // Upload Files
        function uploadFiles() {
            $('#file-upload-input').click();
        }

        $('#file-upload-input').on('change', function(e) {
            const files = e.target.files;
            if (files.length === 0) return;

            const formData = new FormData();
            for (let i = 0; i < files.length; i++) {
                formData.append('files[]', files[i]);
            }

            // Only append folder_id if it's not null
            if (currentFolderId !== null) {
                formData.append('folder_id', currentFolderId);
            }

            $.ajax({
                url: '{{ route("projects.files.upload", $project->id) }}',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    show_toastr('Success', response.message, 'success');
                    loadFiles(currentFolderId);
                    $('#file-upload-input').val('');
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    show_toastr('Error', response.message || '{{ __("Failed to upload files") }}', 'error');
                    $('#file-upload-input').val('');
                }
            });
        });

        // Context Menu
        function showContextMenu(event, itemId, itemType) {
            event.preventDefault();
            selectedItem = itemId;
            selectedItemType = itemType;

            const contextMenu = $('#context-menu');
            contextMenu.css({
                display: 'block',
                left: event.pageX + 'px',
                top: event.pageY + 'px'
            });

            // Hide download option for folders
            if (itemType === 'folder') {
                contextMenu.find('.context-menu-item').first().hide();
            } else {
                contextMenu.find('.context-menu-item').first().show();
            }
        }

        // Hide context menu when clicking elsewhere
        $(document).on('click', function() {
            $('#context-menu').hide();
        });

        // Download File
        function downloadFile(fileId) {
            window.open('{{ route("projects.files.download", [$project->id, ":fileId"]) }}'.replace(':fileId', fileId));
        }

        function downloadItem() {
            if (selectedItemType === 'file') {
                downloadFile(selectedItem);
            }
            $('#context-menu').hide();
        }

        // Rename Item
        function renameItem() {
            const itemName = $(`.${selectedItemType}-item[data-id="${selectedItem}"] .${selectedItemType}-name`).text();
            $('#rename-input').val(itemName);
            $('#rename-modal-title').text(selectedItemType === 'folder' ? '{{ __("Rename Folder") }}' : '{{ __("Rename File") }}');
            $('#renameModal').modal('show');
            $('#context-menu').hide();
        }

        $('#rename-form').on('submit', function(e) {
            e.preventDefault();

            const newName = $('#rename-input').val();
            const url = selectedItemType === 'folder'
                ? '{{ route("projects.folders.rename", [$project->id, ":itemId"]) }}'.replace(':itemId', selectedItem)
                : '{{ route("projects.files.rename", [$project->id, ":itemId"]) }}'.replace(':itemId', selectedItem);

            $.ajax({
                url: url,
                method: 'PATCH',
                data: { name: newName },
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    $('#renameModal').modal('hide');
                    show_toastr('Success', response.message, 'success');
                    loadFiles(currentFolderId);
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    show_toastr('Error', response.message || '{{ __("Failed to rename") }}', 'error');
                }
            });
        });

        // Delete Item
        function deleteItem() {
            const itemName = $(`.${selectedItemType}-item[data-id="${selectedItem}"] .${selectedItemType}-name`).text();
            const confirmMessage = selectedItemType === 'folder'
                ? '{{ __("Are you sure you want to delete this folder and all its contents?") }}'
                : '{{ __("Are you sure you want to delete this file?") }}';

            if (confirm(confirmMessage)) {
                const url = selectedItemType === 'folder'
                    ? '{{ route("projects.folders.delete", [$project->id, ":itemId"]) }}'.replace(':itemId', selectedItem)
                    : '{{ route("projects.files.delete", [$project->id, ":itemId"]) }}'.replace(':itemId', selectedItem);

                $.ajax({
                    url: url,
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        show_toastr('Success', response.message, 'success');
                        loadFiles(currentFolderId);
                    },
                    error: function(xhr) {
                        const response = xhr.responseJSON;
                        show_toastr('Error', response.message || '{{ __("Failed to delete") }}', 'error');
                    }
                });
            }
            $('#context-menu').hide();
        }

        // Folder Icon Selection
        $(document).on('click', '.folder-icon-option', function() {
            const icon = $(this).data('icon');
            $('#folder-icon-input').val(icon);
            $('.folder-icon-option').removeClass('active');
            $(this).addClass('active');
            updateFolderPreview();
        });

        // Color Preset Selection
        $(document).on('click', '.color-preset', function() {
            const color = $(this).data('color');
            $('#folder-color').val(color);
            $('.color-preset').removeClass('active');
            $(this).addClass('active');
            updateFolderPreview();
        });

        // Color picker change
        $(document).on('change', '#folder-color', function() {
            updateFolderPreview();
        });

        // Update folder preview
        function updateFolderPreview() {
            const icon = $('#folder-icon-input').val() || 'ti-folder-filled';
            const color = $('#folder-color').val() || '#3b82f6';

            $('#folder-preview-icon')
                .removeClass()
                .addClass(`ti ${icon}`)
                .css('color', color);
        }

        // Drag and Drop File Upload
        let dragCounter = 0;

        $('#files-grid').on('dragenter', function(e) {
            e.preventDefault();
            dragCounter++;
            $(this).addClass('drag-over');
        });

        $('#files-grid').on('dragleave', function(e) {
            e.preventDefault();
            dragCounter--;
            if (dragCounter === 0) {
                $(this).removeClass('drag-over');
            }
        });

        $('#files-grid').on('dragover', function(e) {
            e.preventDefault();
        });

        $('#files-grid').on('drop', function(e) {
            e.preventDefault();
            dragCounter = 0;
            $(this).removeClass('drag-over');

            const files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                uploadDroppedFiles(files);
            }
        });

        function uploadDroppedFiles(files) {
            const formData = new FormData();
            for (let i = 0; i < files.length; i++) {
                formData.append('files[]', files[i]);
            }

            // Only append folder_id if it's not null
            if (currentFolderId !== null) {
                formData.append('folder_id', currentFolderId);
            }

            $.ajax({
                url: '{{ route("projects.files.upload", $project->id) }}',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    show_toastr('Success', response.message, 'success');
                    loadFiles(currentFolderId);
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    show_toastr('Error', response.message || '{{ __("Failed to upload files") }}', 'error');
                }
            });
        }

        // Keyboard Shortcuts
        $(document).on('keydown', function(e) {
            if ($('#files-tab').hasClass('active')) {
                // Ctrl+N or Cmd+N for new folder
                if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                    e.preventDefault();
                    createFolder();
                }
                // Ctrl+U or Cmd+U for upload
                if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
                    e.preventDefault();
                    uploadFiles();
                }
                // F5 for refresh
                if (e.key === 'F5') {
                    e.preventDefault();
                    refreshFiles();
                }
            }
        });
    </script>

    {{-- Dragula Library for Drag and Drop --}}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dragula/3.7.3/dragula.min.js"></script>

    {{-- share project copy link --}}
    <script>
        function copyToClipboard(element) {

            var copyText = element.id;
            navigator.clipboard.writeText(copyText);
            // document.addEventListener('copy', function (e) {
            //     e.clipboardData.setData('text/plain', copyText);
            //     e.preventDefault();
            // }, true);
            //
            // document.execCommand('copy');
            show_toastr('success', 'Url copied to clipboard', 'success');
        }
    </script>
@endpush
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ route('projects.index') }}">{{ __('Project') }}</a></li>
    <li class="breadcrumb-item">{{ ucwords($project->project_name) }}</li>
@endsection
@section('action-btn')
    <div class="float-end">

        {{-- Gantt Chart hidden as per user preference --}}
        {{-- @can('view grant chart')
            <a href="{{ route('projects.gantt', $project->id) }}" class="btn btn-sm bg-warning-subtle text-white me-1">
                {{ __('Gantt Chart') }}
            </a>
        @endcan --}}

        {{-- Expense hidden as per user preference --}}
        {{-- @can('view expense')
            <a href="{{ route('projects.expenses.index', $project->id) }}"
                class="btn btn-sm bg-light-blue-subtitle text-white me-1">
                {{ __('Expense') }}
            </a>
        @endcan --}}
        {{-- Timesheet hidden as per user preference --}}
        {{-- @if (\Auth::user()->type != 'client')
            @can('view timesheet')
                <a href="{{ route('timesheet.index', $project->id) }}" class="btn btn-sm bg-blue-subtitle text-white me-1">
                    {{ __('Timesheet') }}
                </a>
            @endcan
        @endif --}}
        {{-- Bug Report hidden as per user preference --}}
        {{-- @can('manage bug report')
            <a href="{{ route('task.bug', $project->id) }}" class="btn btn-sm bg-light-green-subtitle text-white me-1">
                {{ __('Bug Report') }}
            </a>
        @endcan --}}

        @can('edit project')
            <a href="#" data-size="lg" data-url="{{ route('projects.edit', $project->id) }}" data-ajax-popup="true"
                data-bs-toggle="tooltip" title="{{ __('Edit Project') }}" class="btn btn-sm btn-info me-1">
                <i class="ti ti-pencil"></i>
            </a>
        @endcan
        @can('delete project')
            {!! Form::open(['method' => 'DELETE', 'route' => ['projects.destroy', $project->id], 'class' => 'd-inline']) !!}
            <button type="submit" class="btn btn-sm btn-danger bs-pass-para" data-bs-toggle="tooltip"
                    title="{{ __('Delete Project') }}" data-confirm="{{ __('Are You Sure?') }}"
                    data-text="{{ __('This action can not be undone. Do you want to continue?') }}">
                <i class="ti ti-trash"></i>
            </button>
            {!! Form::close() !!}
        @endcan


    </div>
@endsection
@section('content')
<div class="project-container">
    <!-- Project Tabs Section -->
    <div class="project-tabs">
        <ul class="nav nav-tabs" id="projectTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview"
                        type="button" role="tab" aria-controls="overview" aria-selected="true">
                    <i class="ti ti-dashboard"></i>
                    {{ __('Overview') }}
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="task-kanban-tab" data-bs-toggle="tab" data-bs-target="#task-kanban"
                        type="button" role="tab" aria-controls="task-kanban" aria-selected="false">
                    <i class="ti ti-layout-kanban"></i>
                    {{ __('Task Kanban') }}
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="task-list-tab" data-bs-toggle="tab" data-bs-target="#task-list"
                        type="button" role="tab" aria-controls="task-list" aria-selected="false">
                    <i class="ti ti-list"></i>
                    {{ __('Task List') }}
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="files-tab" data-bs-toggle="tab" data-bs-target="#files"
                        type="button" role="tab" aria-controls="files" aria-selected="false">
                    <i class="ti ti-files"></i>
                    {{ __('Files') }}
                </button>
            </li>
        </ul>

        @php
            // Get all tasks for this project - needed for Task List and Files tabs
            $allTasks = \App\Models\Project::projectTask($project->id);
        @endphp

        <div class="tab-content" id="projectTabsContent">
            <!-- Overview Tab -->
            <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                <!-- Hero Section -->
                <div class="project-hero mb-4">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <div class="d-flex align-items-start gap-4">
                                <div class="project-avatar-modern">
                                    <i class="ti ti-briefcase"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center justify-content-between mb-3">
                                        <h1 class="project-title">{{ $project->project_name }}</h1>
                                        <span class="status-badge bg-{{ \App\Models\Project::$status_color[$project->status] }}">
                                            {{ __(\App\Models\Project::$project_status[$project->status]) }}
                                        </span>
                                    </div>
                                    <p class="project-description">{{ $project->description ?: 'No description available for this project.' }}</p>

                                    <!-- Progress Section -->
                                    @php
                                        $lastTaskId = $last_task ? $last_task->id : null;
                                        $projectProgress = $lastTaskId ? $project->project_progress($project, $lastTaskId)['percentage'] : '0%';
                                    @endphp
                                    <div class="mb-4">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <span class="fw-semibold text-dark">{{ __('Project Progress') }}</span>
                                            <span class="fw-bold" style="font-size: 1.1rem; color: var(--theme-color);">{{ $projectProgress }}</span>
                                        </div>
                                        <div class="progress-modern">
                                            <div class="progress-bar" style="width: {{ $projectProgress }}; height: 100%; background: linear-gradient(135deg, var(--theme-color) 0%, rgba(var(--theme-color-rgb), 0.7) 100%);"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="metric-card">
                                <div class="metric-number">{{ $project_data['task']['total'] }}</div>
                                <div class="metric-label">{{ __('Total Tasks') }}</div>
                                <div class="d-flex justify-content-center gap-4 mt-4">
                                    <div class="text-center">
                                        <div class="fw-bold" style="color: var(--theme-color); font-size: 1.25rem;">{{ $project_data['task']['done'] }}</div>
                                        <small class="text-muted fw-medium">{{ __('Done') }}</small>
                                    </div>
                                    <div class="text-center">
                                        <div class="fw-bold" style="color: rgba(var(--theme-color-rgb), 0.6); font-size: 1.25rem;">{{ $project_data['task']['total'] - $project_data['task']['done'] }}</div>
                                        <small class="text-muted fw-medium">{{ __('Pending') }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Project Info Grid -->
                    <div class="row g-4 mt-4">
                        <div class="col-md-3">
                            <div class="info-grid-item">
                                <div class="info-icon">
                                    <i class="ti ti-calendar-plus"></i>
                                </div>
                                <div class="small text-muted text-uppercase fw-semibold mb-1">{{ __('Start Date') }}</div>
                                <div class="fw-bold text-dark">{{ Utility::getDateFormated($project->start_date) }}</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-grid-item">
                                <div class="info-icon">
                                  <i class="ti ti-calendar-plus"></i>
                                </div>
                                <div class="small text-muted text-uppercase fw-semibold mb-1">{{ __('End Date') }}</div>
                                <div class="fw-bold text-dark">{{ Utility::getDateFormated($project->end_date) }}</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-grid-item">
                                <div class="info-icon">
                                    <i class="ti ti-user"></i>
                                </div>
                                <div class="small text-muted text-uppercase fw-semibold mb-1">{{ __('Client') }}</div>
                                <div class="fw-bold text-dark">{{ !empty($project->client) ? $project->client->name : '-' }}</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-grid-item">
                                <div class="info-icon">
                                    <i class="ti ti-wallet"></i>
                                </div>
                                <div class="small text-muted text-uppercase fw-semibold mb-1">{{ __('Budget') }}</div>
                                <div class="fw-bold text-dark">{{ \Auth::user()->priceFormat($project->budget) }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Team and Milestones Section -->
                <div class="row g-4 mb-4">
        <!-- Team Members -->
        <div class="col-lg-6">
            <div class="modern-card">
                <div class="p-4">
                    <div class="d-flex align-items-center justify-content-between mb-4">
                        <div>
                            <h5 class="mb-1 fw-bold text-dark">{{ __('Team Members') }}</h5>
                            <small class="text-muted">{{ __('Project collaborators') }}</small>
                        </div>
                        @can('edit project')
                            <a href="#" data-size="lg" data-url="{{ route('invite.project.member.view', $project->id) }}"
                               data-ajax-popup="true" class="action-btn-modern action-btn-primary" style="padding: 0.75rem;">
                                <i class="ti ti-plus text-white"></i>
                            </a>
                        @endcan
                    </div>
                    <div class="members-container custom-scroll" style="max-height: 350px; overflow-y: auto;">
                        <div id="project_users">
                            <!-- Members will be loaded via AJAX -->
                        </div>
                        <div class="text-center py-4" id="members-placeholder">
                            <div class="info-icon d-inline-flex mb-3">
                                <i class="ti ti-users"></i>
                            </div>
                            <h6 class="text-muted">{{ __('Loading team members...') }}</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Milestones -->
        <div class="col-lg-6">
            <div class="modern-card">
                <div class="p-4">
                    <div class="d-flex align-items-center justify-content-between mb-4">
                        <div>
                            <h5 class="mb-1 fw-bold text-dark">{{ __('Milestones') }}</h5>
                            <small class="text-muted">{{ count($project->milestones) }} {{ __('milestones created') }}</small>
                        </div>
                        @can('create milestone')
                            <a href="#" data-size="md" data-url="{{ route('project.milestone', $project->id) }}"
                               data-ajax-popup="true" class="action-btn-modern action-btn-primary" style="padding: 0.75rem;">
                                <i class="ti ti-plus text-white"></i>
                            </a>
                        @endcan
                    </div>
                    <div class="milestones-container custom-scroll" style="max-height: 350px; overflow-y: auto;">
                        @if ($project->milestones->count() > 0)
                            <div class="space-y-3">
                                @foreach ($project->milestones as $milestone)
                                    <div class="milestone-item p-3 rounded-3" style="background: rgba(255, 255, 255, 0.7); border: 1px solid rgba(255, 255, 255, 0.3); transition: all 0.3s ease;">
                                        <div class="d-flex align-items-start justify-content-between">
                                            <div class="flex-grow-1">
                                                <div class="d-flex align-items-center gap-3 mb-2">
                                                    <div class="info-icon bg-{{ \App\Models\Project::$status_color[$milestone->status] }} bg-opacity-10" style="width: 32px; height: 32px;">
                                                        <i class="ti ti-flag text-{{ \App\Models\Project::$status_color[$milestone->status] }}"></i>
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <h6 class="mb-1 fw-semibold text-dark">{{ $milestone->title }}</h6>
                                                        <div class="d-flex align-items-center gap-2">
                                                            <span class="status-badge bg-{{ \App\Models\Project::$status_color[$milestone->status] }}" style="padding: 0.25rem 0.75rem; font-size: 0.75rem;">
                                                                {{ __(\App\Models\Project::$project_status[$milestone->status]) }}
                                                            </span>
                                                            <small class="text-muted">{{ $milestone->tasks->count() }} {{ __('tasks') }}</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="d-flex gap-1">
                                                @can('view milestone')
                                                    <a href="#" data-size="lg" data-url="{{ route('project.milestone.show', $milestone->id) }}"
                                                       data-ajax-popup="true" class="btn btn-sm btn-outline-primary" style="border-radius: 8px;">
                                                        <i class="ti ti-eye"></i>
                                                    </a>
                                                @endcan
                                                @can('edit milestone')
                                                    <a href="#" data-size="md" data-url="{{ route('project.milestone.edit', $milestone->id) }}"
                                                       data-ajax-popup="true" data-title="{{ __('Edit Milestone') }}"
                                                       class="btn btn-sm btn-outline-warning" style="border-radius: 8px;">
                                                        <i class="ti ti-pencil"></i>
                                                    </a>
                                                @endcan
                                                @can('delete milestone')
                                                    {!! Form::open(['method' => 'DELETE', 'route' => ['project.milestone.destroy', $milestone->id], 'class' => 'd-inline']) !!}
                                                    <button type="submit" class="btn btn-sm btn-outline-danger bs-pass-para" style="border-radius: 8px;">
                                                        <i class="ti ti-trash"></i>
                                                    </button>
                                                    {!! Form::close() !!}
                                                @endcan
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-5">
                                <div class="info-icon-large d-inline-flex mb-3">
                                    <i class="ti ti-flag"></i>
                                </div>
                                <h6 class="text-muted mb-2">{{ __('No Milestones Yet') }}</h6>
                                <p class="text-muted small mb-0">{{ __('Create milestones to track important project goals') }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Activity and Attachments Section -->
    <div class="row g-4">
        @can('view activity')
            <!-- Activity Log -->
            <div class="col-lg-12">
                <div class="modern-card">
                    <div class="p-4">
                        <div class="d-flex align-items-center justify-content-between mb-4">
                            <div>
                                <h5 class="mb-1 fw-bold text-dark">{{ __('Activity Log') }}</h5>
                                <small class="text-muted">{{ __('Recent project activities') }}</small>
                            </div>
                            <div class="info-icon">
                                <i class="ti ti-activity"></i>
                            </div>
                        </div>
                        <div class="activity-container custom-scroll" style="max-height: 400px; overflow-y: auto;">
                            @if($project->activities->count() > 0)
                                <div class="space-y-3">
                                    @foreach ($project->activities as $activity)
                                        <div class="activity-item d-flex gap-3 p-3 rounded-3" style="background: rgba(255, 255, 255, 0.7); border: 1px solid rgba(255, 255, 255, 0.3);">
                                            <div class="info-icon-small">
                                                <i class="ti {{ $activity->logIcon($activity->log_type) }}"></i>
                                            </div>
                                            <div class="flex-grow-1">
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <h6 class="mb-0 fw-semibold text-dark">{{ __($activity->log_type) }}</h6>
                                                    <small class="text-muted">{{ $activity->created_at->diffForHumans() }}</small>
                                                </div>
                                                <p class="text-muted small mb-0 lh-base">{!! $activity->getRemark() !!}</p>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-5">
                                    <div class="info-icon-large d-inline-flex mb-3">
                                        <i class="ti ti-activity"></i>
                                    </div>
                                    <h6 class="text-muted mb-2">{{ __('No Activities Yet') }}</h6>
                                    <p class="text-muted small mb-0">{{ __('Project activities will appear here as they happen') }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        @endcan

        {{-- <!-- Attachments -->
        <div class="col-lg-6">
            <div class="modern-card">
                <div class="p-4">
                    <div class="d-flex align-items-center justify-content-between mb-4">
                        <div>
                            <h5 class="mb-1 fw-bold text-dark">{{ __('Attachments') }}</h5>
                            <small class="text-muted">{{ __('Project files and documents') }}</small>
                        </div>
                        <div class="info-icon">
                            <i class="ti ti-paperclip"></i>
                        </div>
                    </div>
                    <div class="attachments-container custom-scroll" style="max-height: 400px; overflow-y: auto;">
                        @if ($project->projectAttachments()->count() > 0)
                            <div class="space-y-3">
                                @foreach ($project->projectAttachments() as $attachment)
                                    @php
                                        $file = \App\Models\Utility::get_file('uploads/tasks/');
                                        $extension = pathinfo($attachment->name, PATHINFO_EXTENSION);
                                        $iconClass = match(strtolower($extension)) {
                                            'pdf' => 'ti-file-type-pdf text-danger',
                                            'doc', 'docx' => 'ti-file-type-doc text-primary',
                                            'xls', 'xlsx' => 'ti-file-type-xls text-success',
                                            'jpg', 'jpeg', 'png', 'gif' => 'ti-photo text-warning',
                                            default => 'ti-file text-muted'
                                        };
                                    @endphp
                                    <div class="attachment-item d-flex align-items-center justify-content-between p-3 rounded-3" style="background: rgba(255, 255, 255, 0.7); border: 1px solid rgba(255, 255, 255, 0.3);">
                                        <div class="d-flex align-items-center gap-3">
                                            <div class="info-icon bg-white" style="width: 40px; height: 40px;">
                                                <i class="ti {{ $iconClass }}"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1 fw-semibold text-dark">{{ $attachment->name }}</h6>
                                                <small class="text-muted">{{ $attachment->file_size }}</small>
                                            </div>
                                        </div>
                                        <a href="{{ $file . $attachment->file }}"
                                           class="action-btn-modern" style="padding: 0.5rem 1rem;" download>
                                            <i class="ti ti-download"></i>
                                        </a>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-5">
                                <div class="info-icon-large d-inline-flex mb-3">
                                    <i class="ti ti-paperclip"></i>
                                </div>
                                <h6 class="text-muted mb-2">{{ __('No Attachments Found') }}</h6>
                                <p class="text-muted small mb-0">{{ __('Project files and documents will appear here') }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div> --}}
    </div>
            </div>

            <!-- Task Kanban Tab -->
            <div class="tab-pane fade" id="task-kanban" role="tabpanel" aria-labelledby="task-kanban-tab">
                @include('projects.kanban_content', ['project' => $project])
            </div>

            <!-- Task List Tab -->
            <div class="tab-pane fade" id="task-list" role="tabpanel" aria-labelledby="task-list-tab">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">{{ __('Task List') }}</h6>
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshTaskList()" title="{{ __('Refresh Task List') }}">
                        <i class="ti ti-refresh"></i> {{ __('Refresh') }}
                    </button>
                </div>
                <div id="task-list-content">
                    @include('projects.task_list_content', ['project' => $project])
                </div>
            </div>

            <!-- Files Tab -->
            <div class="tab-pane fade" id="files" role="tabpanel" aria-labelledby="files-tab">
                @include('projects.files_content')
            </div>
        </div>
    </div>
</div>
@endsection
