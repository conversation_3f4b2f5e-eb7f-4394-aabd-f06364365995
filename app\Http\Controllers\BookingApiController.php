<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\AppointmentBooking;
use App\Models\CalendarEvent;
use App\Models\User;
use App\Services\CrmWebhookDispatcher;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

class BookingApiController extends Controller
{
    /**
     * Validate SSO Token middleware
     */
    private function validateSsoToken(Request $request)
    {
        $token = $request->bearerToken() ?? $request->get('token');
        
        if (!$token) {
            return response()->json(['error' => 'SSO token required'], 401);
        }

        try {
            $decoded = JWT::decode($token, new Key(env('SSO_SECRET', 'default-secret'), 'HS256'));
            
            $user = User::find($decoded->user_id);
            if (!$user) {
                return response()->json(['error' => 'Invalid user'], 401);
            }

            // Set the authenticated user for the request
            Auth::setUser($user);
            
            return true;
        } catch (\Exception $e) {
            return response()->json(['error' => 'Invalid or expired SSO token'], 401);
        }
    }

    /**
     * Check if token validation failed
     */
    private function checkAuth(Request $request)
    {
        $validation = $this->validateSsoToken($request);
        if ($validation !== true) {
            return $validation;
        }
        return null;
    }

    // ==================== BOOKINGS API ====================

    /**
     * Get all bookings
     */
    public function getBookings(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $bookings = Booking::with('event')
            ->whereHas('event', function($query) {
                $query->where('created_by', Auth::id());
            })
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $bookings,
            'message' => 'Bookings retrieved successfully'
        ]);
    }

    /**
     * Get single booking
     */
    public function getBooking(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $booking = Booking::with('event')
            ->whereHas('event', function($query) {
                $query->where('created_by', Auth::id());
            })
            ->where('id', $id)
            ->first();

        if (!$booking) {
            return response()->json(['error' => 'Booking not found'], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $booking,
            'message' => 'Booking retrieved successfully'
        ]);
    }

    /**
     * Create new booking
     */
    public function createBooking(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'event_id' => 'required|exists:calendar_events,id',
            'date' => 'required|date',
            'time' => 'required|string',
            'custom_fields' => 'nullable|array',
            'custom_fields_value' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        // Verify the event exists and belongs to the user
        $event = CalendarEvent::where('id', $request->event_id)
            ->where('created_by', Auth::id())
            ->first();

        if (!$event) {
            return response()->json(['error' => 'Event not found or access denied'], 404);
        }

        // Create the booking
        $booking = Booking::create([
            'event_id' => $request->event_id,
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'date' => $request->date,
            'time' => $request->time,
            'custom_fields' => $request->custom_fields,
            'custom_fields_value' => $request->custom_fields_value,
        ]);

        // Send webhook
        try {
            $webhookDispatcher = new CrmWebhookDispatcher();
            $webhookDispatcher->dispatchBookingCreated($booking);
        } catch (\Exception $e) {
            Log::error('Webhook dispatch failed for booking creation', ['error' => $e->getMessage()]);
        }

        return response()->json([
            'success' => true,
            'data' => $booking->load('event'),
            'message' => 'Booking created successfully'
        ], 201);
    }

    /**
     * Update booking
     */
    public function updateBooking(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $booking = Booking::with('event')
            ->whereHas('event', function($query) {
                $query->where('created_by', Auth::id());
            })
            ->where('id', $id)
            ->first();

        if (!$booking) {
            return response()->json(['error' => 'Booking not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'date' => 'required|date',
            'time' => 'required|string',
            'custom_fields' => 'nullable|array',
            'custom_fields_value' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $booking->update([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'date' => $request->date,
            'time' => $request->time,
            'custom_fields' => $request->custom_fields,
            'custom_fields_value' => $request->custom_fields_value,
        ]);

        return response()->json([
            'success' => true,
            'data' => $booking->load('event'),
            'message' => 'Booking updated successfully'
        ]);
    }

    /**
     * Delete booking
     */
    public function deleteBooking(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $booking = Booking::with('event')
            ->whereHas('event', function($query) {
                $query->where('created_by', Auth::id());
            })
            ->where('id', $id)
            ->first();

        if (!$booking) {
            return response()->json(['error' => 'Booking not found'], 404);
        }

        $booking->delete();

        return response()->json([
            'success' => true,
            'message' => 'Booking deleted successfully'
        ]);
    }

    // ==================== APPOINTMENT BOOKINGS API ====================

    /**
     * Get all appointment bookings
     */
    public function getAppointmentBookings(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $query = AppointmentBooking::with('event')
            ->whereHas('event', function($q) {
                $q->where('created_by', Auth::id());
            });

        // Filter by specific event if provided
        if ($request->has('event_id')) {
            $query->where('event_id', $request->event_id);
        }

        // Filter by date range if provided
        if ($request->has('start_date') && $request->has('end_date')) {
            $query->whereBetween('event_date', [$request->start_date, $request->end_date]);
        }

        $appointmentBookings = $query->orderBy('event_date', 'desc')
                                    ->orderBy('time_slots', 'desc')
                                    ->get();

        return response()->json([
            'success' => true,
            'data' => $appointmentBookings,
            'message' => 'Appointment bookings retrieved successfully'
        ]);
    }

    /**
     * Get single appointment booking
     */
    public function getAppointmentBooking(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $appointmentBooking = AppointmentBooking::with('event')
            ->whereHas('event', function($query) {
                $query->where('created_by', Auth::id());
            })
            ->where('id', $id)
            ->first();

        if (!$appointmentBooking) {
            return response()->json(['error' => 'Appointment booking not found'], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $appointmentBooking,
            'message' => 'Appointment booking retrieved successfully'
        ]);
    }

    /**
     * Create new appointment booking
     */
    public function createAppointmentBooking(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'event_id' => 'required|exists:calendar_events,id',
            'event_location' => 'required|string|max:255',
            'event_location_value' => 'nullable|string',
            'event_date' => 'required|date',
            'time_zone' => 'required|string|max:100',
            'time_slots' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        // Get the calendar event and verify ownership
        $calendarEvent = CalendarEvent::where('id', $request->event_id)
            ->where('created_by', Auth::id())
            ->first();

        if (!$calendarEvent) {
            return response()->json(['error' => 'Event not found or access denied'], 404);
        }

        // Create the appointment booking
        $appointmentBooking = AppointmentBooking::create([
            'event_id' => $request->event_id,
            'event_location' => $request->event_location,
            'event_location_value' => $request->event_location_value,
            'event_date' => $request->event_date,
            'time_zone' => $request->time_zone,
            'time_slots' => $request->time_slots,
        ]);

        // Send webhook
        try {
            $webhookDispatcher = new CrmWebhookDispatcher();
            $webhookDispatcher->dispatchAppointmentBookingCreated($appointmentBooking);
        } catch (\Exception $e) {
            Log::error('Webhook dispatch failed for appointment booking creation', ['error' => $e->getMessage()]);
        }

        return response()->json([
            'success' => true,
            'data' => $appointmentBooking->load('event'),
            'message' => 'Appointment booking created successfully'
        ], 201);
    }

    /**
     * Update appointment booking
     */
    public function updateAppointmentBooking(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $appointmentBooking = AppointmentBooking::with('event')
            ->whereHas('event', function($query) {
                $query->where('created_by', Auth::id());
            })
            ->where('id', $id)
            ->first();

        if (!$appointmentBooking) {
            return response()->json(['error' => 'Appointment booking not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'event_location' => 'required|string|max:255',
            'event_location_value' => 'nullable|string',
            'event_date' => 'required|date',
            'time_zone' => 'required|string|max:100',
            'time_slots' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $appointmentBooking->update([
            'event_location' => $request->event_location,
            'event_location_value' => $request->event_location_value,
            'event_date' => $request->event_date,
            'time_zone' => $request->time_zone,
            'time_slots' => $request->time_slots,
        ]);

        return response()->json([
            'success' => true,
            'data' => $appointmentBooking->load('event'),
            'message' => 'Appointment booking updated successfully'
        ]);
    }

    /**
     * Delete appointment booking
     */
    public function deleteAppointmentBooking(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $appointmentBooking = AppointmentBooking::with('event')
            ->whereHas('event', function($query) {
                $query->where('created_by', Auth::id());
            })
            ->where('id', $id)
            ->first();

        if (!$appointmentBooking) {
            return response()->json(['error' => 'Appointment booking not found'], 404);
        }

        $appointmentBooking->delete();

        return response()->json([
            'success' => true,
            'message' => 'Appointment booking deleted successfully'
        ]);
    }

    // ==================== CALENDAR EVENTS API ====================

    /**
     * Get all calendar events
     */
    public function getCalendarEvents(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $events = CalendarEvent::where('created_by', Auth::id())
            ->with(['bookings', 'appointmentBookings'])
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $events,
            'message' => 'Calendar events retrieved successfully'
        ]);
    }

    /**
     * Get single calendar event
     */
    public function getCalendarEvent(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $event = CalendarEvent::where('created_by', Auth::id())
            ->where('id', $id)
            ->with(['bookings', 'appointmentBookings', 'weeklyAvailability'])
            ->first();

        if (!$event) {
            return response()->json(['error' => 'Calendar event not found'], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $event,
            'message' => 'Calendar event retrieved successfully'
        ]);
    }

    /**
     * Create new calendar event
     */
    public function createCalendarEvent(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'duration' => 'required|integer|min:1',
            'booking_per_slot' => 'nullable|integer|min:1',
            'minimum_notice' => 'nullable|integer|min:0',
            'description' => 'nullable|string',
            'location' => 'nullable|string|max:255',
            'meet_link' => 'nullable|url',
            'physical_address' => 'nullable|string',
            'require_name' => 'boolean',
            'require_email' => 'boolean',
            'require_phone' => 'boolean',
            'weekly_availability' => 'nullable|array',
            'custom_fields' => 'nullable|array',
            'date_override' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $event = CalendarEvent::create([
            'title' => $request->title,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'duration' => $request->duration,
            'booking_per_slot' => $request->booking_per_slot ?? 1,
            'minimum_notice' => $request->minimum_notice ?? 0,
            'description' => $request->description,
            'location' => $request->location,
            'meet_link' => $request->meet_link,
            'physical_address' => $request->physical_address,
            'require_name' => $request->require_name ?? true,
            'require_email' => $request->require_email ?? true,
            'require_phone' => $request->require_phone ?? false,
            'weekly_availability' => $request->weekly_availability,
            'custom_fields' => $request->custom_fields,
            'date_override' => $request->date_override,
            'created_by' => Auth::id(),
        ]);

        return response()->json([
            'success' => true,
            'data' => $event,
            'message' => 'Calendar event created successfully'
        ], 201);
    }

    // ==================== NEW APPOINTMENT & EVENT ACTIONS (API) ====================

    /**
     * Update a calendar event (API)
     */
    public function updateCalendarEvent(Request $request, $id)
    {
        return response()->json(['error' => 'Not implemented'], 501);
    }

    /**
     * Delete a calendar event (API)
     */
    public function deleteCalendarEvent(Request $request, $id)
    {
        return response()->json(['error' => 'Not implemented'], 501);
    }

    /**
     * Set appointment status (confirm, cancel, complete, reschedule)
     */
    public function setAppointmentStatus(Request $request, $id)
    {
        return response()->json(['error' => 'Not implemented'], 501);
    }

    /**
     * Generate appointment link
     */
    public function generateAppointmentLink(Request $request, $id)
    {
        return response()->json(['error' => 'Not implemented'], 501);
    }

    /**
     * Send calendar invite for appointment
     */
    public function sendCalendarInvite(Request $request, $id)
    {
        return response()->json(['error' => 'Not implemented'], 501);
    }

    /**
     * Update appointment location
     */
    public function updateAppointmentLocation(Request $request, $id)
    {
        return response()->json(['error' => 'Not implemented'], 501);
    }

    /**
     * Reschedule appointment
     */
    public function rescheduleAppointment(Request $request, $id)
    {
        return response()->json(['error' => 'Not implemented'], 501);
    }

    /**
     * Explicitly cancel appointment (optional)
     */
    public function cancelAppointment(Request $request, $id)
    {
        return response()->json(['error' => 'Not implemented'], 501);
    }
}
