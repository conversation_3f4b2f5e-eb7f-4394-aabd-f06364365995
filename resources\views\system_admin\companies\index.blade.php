@extends('layouts.admin')
@php
    $profile = \App\Models\Utility::get_file('uploads/avatar');
@endphp
@section('page-title')
    {{ __('Sub-Accounts') }}
    <span class="badge bg-primary align-middle ms-2" style="font-size:1rem;">{{ $totalCompaniesCount }}</span>
@endsection
@section('breadcrumb')
    <li class="breadcrumb-item">
        <a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a>
    </li>
    <li class="breadcrumb-item">{{ __('Sub-Accounts (Companies)') }}</li>
@endsection
@section('action-btn')
    <div class="float-end">
        @if (\Auth::user()->type == 'system admin' || (\Auth::user()->type == 'staff' && \Auth::user()->can('create sub accounts')))
            <a href="{{ route('system-admin.companies.create') }}" class="btn btn-sm btn-primary me-1"
                data-bs-toggle="tooltip" data-title="{{ __('Create Sub-Account') }}" data-bs-original-title="{{ __('Create Sub-Account') }}">
                <i class="ti ti-plus"></i>
            </a>
        @endif
    </div>
@endsection
@section('content')
{{-- Loading Overlay --}}
<div id="loading-overlay" class="d-none">
    <div class="loading-content">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">{{ __('Loading...') }}</span>
        </div>
        <div class="mt-2 text-muted">{{ __('Loading companies...') }}</div>
    </div>
</div>

{{-- Per Page Selector --}}
<div class="d-flex justify-content-between align-items-center mb-3">
    <div class="per-page-selector">
        {{-- Fallback form for non-JS users --}}
        <form method="GET" action="{{ route('system-admin.companies') }}" class="d-none" id="per-page-fallback-form">
            <div class="d-flex align-items-center gap-2">
                <label for="per_page_fallback" class="form-label mb-0 text-muted small">{{ __('Show') }}:</label>
                <select name="per_page" id="per_page_fallback" class="form-select form-select-sm" style="width: auto;" onchange="this.form.submit()">
                    <option value="10" {{ request('per_page', 10) == 10 ? 'selected' : '' }}>10</option>
                    <option value="25" {{ request('per_page', 10) == 25 ? 'selected' : '' }}>25</option>
                    <option value="50" {{ request('per_page', 10) == 50 ? 'selected' : '' }}>50</option>
                    <option value="100" {{ request('per_page', 10) == 100 ? 'selected' : '' }}>100</option>
                </select>
                <span class="text-muted small">{{ __('entries') }}</span>
            </div>
        </form>

        {{-- AJAX form for JS users --}}
        <div class="d-flex align-items-center gap-2" id="per-page-ajax-form">
            <label for="per_page" class="form-label mb-0 text-muted small">{{ __('Show') }}:</label>
            <select name="per_page" id="per_page" class="form-select form-select-sm" style="width: auto;">
                <option value="10" {{ request('per_page', 10) == 10 ? 'selected' : '' }}>10</option>
                <option value="25" {{ request('per_page', 10) == 25 ? 'selected' : '' }}>25</option>
                <option value="50" {{ request('per_page', 10) == 50 ? 'selected' : '' }}>50</option>
                <option value="100" {{ request('per_page', 10) == 100 ? 'selected' : '' }}>100</option>
            </select>
            <span class="text-muted small">{{ __('entries') }}</span>
            <div id="per-page-spinner" class="spinner-border spinner-border-sm text-primary d-none ms-2" role="status">
                <span class="visually-hidden">{{ __('Loading...') }}</span>
            </div>
        </div>
    </div>
    <div class="total-count">
        <span class="text-muted small">
            {{ __('Total') }}: <strong>{{ $totalCompaniesCount }}</strong> {{ __('companies') }}
        </span>
    </div>
</div>

{{-- Table Container --}}
<div id="companies-table-container" class="table-responsive">
    @include('system_admin.companies.partials.table', ['companies' => $companies])
</div>

{{-- Pagination Container --}}
<div id="companies-pagination-container">
    @include('system_admin.companies.partials.pagination', ['companies' => $companies])
</div>

<!-- Force Delete Confirmation Modal -->
<div class="modal fade" id="forceDeleteModal" tabindex="-1" aria-labelledby="forceDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="forceDeleteModalLabel">
                    <i class="ti ti-alert-triangle text-danger me-2"></i>
                    {{ __('Force Delete Confirmation') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger d-flex align-items-center mb-3">
                    <i class="ti ti-alert-circle me-2"></i>
                    <div>
                        <strong>{{ __('Warning!') }}</strong> {{ __('This action cannot be undone.') }}
                    </div>
                </div>
                
                <p id="forceDeleteConfirmText" class="mb-3">
                    {{ __('This will force delete the company even if modules have dependency errors. Are you sure?') }}
                </p>
                
                <div class="mb-3">
                    <label for="forceDeleteInput" class="form-label">
                        {{ __('Type') }} <strong class="text-danger">DELETE</strong> {{ __('to confirm:') }}
                    </label>
                    <input type="text" 
                           class="form-control" 
                           id="forceDeleteInput" 
                           placeholder="{{ __('Type DELETE to confirm') }}"
                           autocomplete="off">
                    <div id="forceDeleteError" class="invalid-feedback" style="display: none;">
                        {{ __('Please type DELETE exactly as shown above.') }}
                    </div>
                </div>
                
                <div class="text-muted small">
                    <i class="ti ti-info-circle me-1"></i>
                    {{ __('This confirmation is required for security purposes.') }}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ __('Cancel') }}
                </button>
                <button type="button" class="btn btn-danger" id="forceDeleteConfirmBtn" disabled>
                    <i class="ti ti-trash me-1"></i>
                    {{ __('Force Delete') }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Standard Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="ti ti-alert-triangle text-danger me-2"></i>
                    {{ __('Delete Confirmation') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger d-flex align-items-center mb-3">
                    <i class="ti ti-alert-circle me-2"></i>
                    <div>
                        <strong>{{ __('Warning!') }}</strong> {{ __('This action cannot be undone.') }}
                    </div>
                </div>
                <p id="deleteConfirmText" class="mb-3">
                    {{ __('Are you sure you want to delete this company?') }}
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ __('Cancel') }}
                </button>
                <button type="button" class="btn btn-danger" id="deleteConfirmBtn">
                    <i class="ti ti-trash me-1"></i>
                    {{ __('Delete') }}
                </button>
            </div>
        </div>
    </div>
</div>

@endsection

{{-- Fallback styles for users with JavaScript disabled --}}
<noscript>
    <style>
        #per-page-ajax-form { display: none !important; }
        #per-page-fallback-form { display: block !important; }
        .pagination-link {
            pointer-events: none;
            opacity: 0.6;
        }
        .pagination-link:after {
            content: " (JavaScript required)";
            font-size: 0.7em;
            color: #dc3545;
        }
    </style>
</noscript>

@push('style-page')
<style>
    /* Loading Overlay Styles */
    #loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.9);
        z-index: 9999;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .loading-content {
        text-align: center;
        padding: 2rem;
        background: white;
        border-radius: 0.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    /* Table Loading State */
    .table-loading {
        opacity: 0.6;
        pointer-events: none;
        transition: opacity 0.3s ease;
    }

    /* Custom Pagination Styles */
    .pagination-wrapper .pagination {
        margin-bottom: 0;
    }

    .pagination-wrapper .page-link {
        color: #6c757d;
        background-color: #fff;
        border: 1px solid #dee2e6;
        padding: 0.5rem 0.75rem;
        margin: 0 2px;
        border-radius: 0.375rem;
        transition: all 0.15s ease-in-out;
        font-size: 0.875rem;
        line-height: 1.25;
        min-width: 40px;
        text-align: center;
    }

    .pagination-wrapper .page-link:hover {
        color: #0056b3;
        background-color: #e9ecef;
        border-color: #adb5bd;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .pagination-wrapper .page-item.active .page-link {
        color: #fff;
        background-color: #0d6efd;
        border-color: #0d6efd;
        box-shadow: 0 2px 4px rgba(13,110,253,0.25);
    }

    .pagination-wrapper .page-item.disabled .page-link {
        color: #6c757d;
        background-color: #fff;
        border-color: #dee2e6;
        opacity: 0.65;
    }

    .pagination-info {
        font-size: 0.875rem;
        color: #6c757d;
    }

    /* Per-page selector styles */
    .per-page-selector .form-select {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 0.25rem 1.5rem 0.25rem 0.5rem;
        font-size: 0.875rem;
        min-width: 70px;
    }

    .per-page-selector .form-select:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .pagination-wrapper .pagination {
            justify-content: center;
        }

        .pagination-info {
            text-align: center;
            margin-bottom: 1rem;
        }

        .d-flex.justify-content-between {
            flex-direction: column;
            align-items: center !important;
        }

        .pagination-wrapper .page-link {
            padding: 0.375rem 0.5rem;
            font-size: 0.8rem;
            min-width: 35px;
        }

        .d-flex.justify-content-between.align-items-center.mb-3 {
            flex-direction: column;
            gap: 1rem;
        }

        .per-page-selector,
        .total-count {
            text-align: center;
        }
    }

    @media (max-width: 576px) {
        .pagination-wrapper .page-link {
            padding: 0.25rem 0.4rem;
            font-size: 0.75rem;
            min-width: 30px;
            margin: 0 1px;
        }

        .pagination-info {
            font-size: 0.8rem;
        }
    }
</style>
@endpush

@push('script-page')
    <script>
        // Global variables for pagination state
        let currentPage = 1;
        let currentPerPage = parseInt('{{ request('per_page', 10) }}');

        // AJAX function to load companies
        function loadCompanies(page = 1, perPage = null, showLoading = true) {
            if (perPage === null) {
                perPage = currentPerPage;
            }

            if (showLoading) {
                showLoadingState();
            }

            $.ajax({
                url: '{{ route("system-admin.companies") }}',
                type: 'GET',
                data: {
                    page: page,
                    per_page: perPage
                },
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    if (response.success) {
                        // Update table content
                        $('#companies-table-container').html(response.html);

                        // Update pagination
                        $('#companies-pagination-container').html(response.pagination);

                        // Update current state
                        currentPage = response.info.current_page;
                        currentPerPage = response.info.per_page;

                        // Update per-page selector
                        $('#per_page').val(currentPerPage);

                        // Re-initialize tooltips for new content
                        $('[data-bs-toggle="tooltip"]').tooltip();

                        // Scroll to top of table smoothly
                        $('html, body').animate({
                            scrollTop: $('#companies-table-container').offset().top - 100
                        }, 300);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading companies:', error);
                    showToast('error', '{{ __("Error loading companies. Please try again.") }}');
                },
                complete: function() {
                    hideLoadingState();
                }
            });
        }

        // Show loading state
        function showLoadingState() {
            $('#companies-table-container').addClass('table-loading');
            $('#companies-pagination-container').addClass('table-loading');

            // Disable pagination links during loading
            $('.pagination-link').prop('disabled', true).addClass('disabled');
        }

        // Hide loading state
        function hideLoadingState() {
            $('#companies-table-container').removeClass('table-loading');
            $('#companies-pagination-container').removeClass('table-loading');
            $('#per-page-spinner').addClass('d-none');
            $('#per_page').prop('disabled', false);

            // Re-enable pagination links
            $('.pagination-link').prop('disabled', false).removeClass('disabled');
        }

        // Show toast notification
        function showToast(type, message) {
            // You can implement your toast notification here
            // For now, using alert as fallback
            if (type === 'error') {
                alert(message);
            }
        }

        // Handle per-page change
        $(document).on('change', '#per_page', function() {
            var newPerPage = $(this).val();

            // Show spinner
            $('#per-page-spinner').removeClass('d-none');
            $(this).prop('disabled', true);

            // Load companies with new per-page value
            loadCompanies(1, newPerPage);
        });

        // Handle pagination clicks
        $(document).on('click', '.pagination-link', function(e) {
            e.preventDefault();
            var page = $(this).data('page');
            if (page) {
                loadCompanies(page);
            }
        });

        // Password switch functionality
        $(document).on('change', '#password_switch', function() {
            if ($(this).is(':checked')) {
                $('.ps_div').removeClass('d-none');
                $('#password').attr("required", true);
            } else {
                $('.ps_div').addClass('d-none');
                $('#password').val(null);
                $('#password').removeAttr("required");
            }
        });

        // Login enable functionality
        $(document).on('click', '.login_enable', function() {
            setTimeout(function() {
                $('.modal-body').append($('<input>', {
                    type: 'hidden',
                    val: 'true',
                    name: 'login_enable'
                }));
            }, 2000);
        });

        // Force delete modal functionality
        $(document).on('click', '.bs-pass-para-force', function(e) {
            e.preventDefault();
            e.stopPropagation();

            var el = $(this);
            var form = el.closest('form');
            var confirmText = el.data('confirm-text') || 'This will force delete the company even if modules have dependency errors. Are you sure?';

            $('#forceDeleteConfirmText').text(confirmText);

            var input = $('#forceDeleteInput');
            var errorMsg = $('#forceDeleteError');
            var confirmBtn = $('#forceDeleteConfirmBtn');

            input.val('');
            input.removeClass('is-invalid is-valid');
            errorMsg.hide();
            confirmBtn.prop('disabled', true);

            $('#forceDeleteModal').attr('data-form-id', form.attr('id'));
            $('#forceDeleteModal').modal('show');

            $('#forceDeleteModal').on('shown.bs.modal', function() {
                input.focus();
            });
        });

        // Handle force delete input validation
        $(document).on('input', '#forceDeleteInput', function() {
            var input = $(this);
            var confirmBtn = $('#forceDeleteConfirmBtn');
            var errorMsg = $('#forceDeleteError');

            if (input.val() === 'DELETE') {
                input.removeClass('is-invalid').addClass('is-valid');
                errorMsg.hide();
                confirmBtn.prop('disabled', false);
            } else {
                input.removeClass('is-valid');
                if (input.val().length > 0) {
                    input.addClass('is-invalid');
                    errorMsg.show();
                } else {
                    input.removeClass('is-invalid');
                    errorMsg.hide();
                }
                confirmBtn.prop('disabled', true);
            }
        });

        // Handle force delete confirmation
        $(document).on('click', '#forceDeleteConfirmBtn', function() {
            var input = $('#forceDeleteInput');
            if (input.val() === 'DELETE') {
                var modal = $('#forceDeleteModal');
                var formId = modal.attr('data-form-id');
                var form = $('#' + formId);

                modal.modal('hide');
                if (form.length) {
                    form.submit();
                }
            }
        });

        // Standard delete modal functionality
        $(document).on('click', '.bs-pass-para-delete', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var el = $(this);
            var form = el.closest('form');
            var confirmText = el.data('confirm-text') || 'Are you sure you want to delete this company?';

            $('#deleteConfirmText').text(confirmText);
            $('#deleteModal').attr('data-form-id', form.attr('id'));
            $('#deleteModal').modal('show');
        });

        // Handle standard delete confirmation
        $(document).on('click', '#deleteConfirmBtn', function() {
            var modal = $('#deleteModal');
            var formId = modal.attr('data-form-id');
            var form = $('#' + formId);
            modal.modal('hide');
            if (form.length) {
                form.submit();
            }
        });

        // Initialize tooltips on page load
        $(document).ready(function() {
            $('[data-bs-toggle="tooltip"]').tooltip();

            // Show AJAX form and hide fallback form when JS is enabled
            $('#per-page-fallback-form').addClass('d-none');
            $('#per-page-ajax-form').removeClass('d-none');
        });

        // Fallback for non-JS users
        $(document).ready(function() {
            // If JavaScript is disabled, the fallback form will be shown
            $('noscript').append('<style>#per-page-ajax-form { display: none !important; } #per-page-fallback-form { display: block !important; }</style>');
        });
    </script>
@endpush
