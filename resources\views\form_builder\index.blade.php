@extends('layouts.admin')
@section('page-title')
    {{__('Manage Form Builder')}}
@endsection
@push('script-page')
    <script>
        $(document).ready(function () {
            $('.cp_link').on('click', function () {
                var value = $(this).attr('data-link');
                var $temp = $("<input>");
                $("body").append($temp);
                $temp.val(value).select();
                document.execCommand("copy");
                $temp.remove();
                show_toastr('success', '{{__('Link Copy on Clipboard')}}')
            });
        });

        $(document).ready(function () {
            $('.iframe_link').on('click', function () {
                var value = $(this).attr('data-link');
                var $temp = $("<input>");
                $("body").append($temp);
                $temp.val(value).select();
                document.execCommand("copy");
                $temp.remove();
                show_toastr('success', '{{__('Link Copy on Clipboard')}}')
            });
        });
    </script>
@endpush
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item">{{__('Form Builder')}}</li>
@endsection
@section('action-btn')
    <div class="float-end">
        <a href="#" data-size="md" data-url="{{ route('form_builder.create') }}" data-ajax-popup="true" data-bs-toggle="tooltip" title="{{__('Create New Form')}}" class="btn btn-sm btn-primary">
            <i class="ti ti-plus"></i>
        </a>
    </div>
    {{-- Style customization button before create new form button commented out --}}
    {{-- <div class="float-end me-2">
        <a href="{{ route('form_builder.custom_design') }}" data-bs-toggle="tooltip" title="{{__('Customize Form Styles')}}" class="btn btn-sm btn-info">
            <i class="ti ti-palette"></i> {{__('Style Customization')}}
        </a>
    </div> --}}
@endsection

@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table datatable">
                            <thead>
                            <tr>
                                <th>{{__('Name')}}</th>
                                <th>{{__('Response')}}</th>
                                @if (\Auth::user()->type == 'company' || (\Auth::user()->can('manage form field') || \Auth::user()->can('view form response') || \Auth::user()->can('edit form builder') || \Auth::user()->can('delete form builder')))
                                    <th class="text-end" width="200px">{{__('Action')}}</th>
                                @endif
                            </tr>
                            </thead>
                            <tbody>
                            @foreach ($forms as $form)
                                <tr>
                                    <td>{{ $form->name }}</td>
                                    <td>
                                        {{ $form->response->count() }}
                                    </td>
                                        <td class="text-end">

                                            <div class="action-btn me-2">
                                                <a href="#" class="mx-3 btn btn-sm align-items-center cp_link bg-warning-subtle" data-link="<iframe width=&quot;100%&quot; height=&quot;100%&quot; src='{{url('/form/'.$form->code)}}' title='{{ $form->name }}'></iframe>" data-bs-toggle="tooltip" title="{{__('Click to copy iframe link')}}"><i class="ti ti-frame text-white"></i></a>
                                            </div>

                                            @if(\Auth::user()->type=='company' || \Auth::user()->type=='accountant')
                                            <div class="action-btn me-2">
                                                <a href="#" class="mx-3 btn btn-sm align-items-center bg-light-blue-subtitle" data-url="{{ route('form.field.bind',$form->id) }}" data-ajax-popup="true" data-size="md" data-bs-toggle="tooltip" title="{{__('Convert into Lead Setting')}}" data-title="{{__('Convert into Lead Setting')}}">
                                                    <i class="ti ti-exchange text-white"></i>
                                                </a>
                                            </div>
                                            @endif

                                            <div class="action-btn me-2">
                                                <a href="#" class="mx-3 btn btn-sm align-items-center cp_link bg-primary" data-link="{{url('/form/'.$form->code)}}" data-bs-toggle="tooltip" title="{{__('Click to copy link')}}"><i class="ti ti-copy text-white"></i></a>
                                            </div>

                                            {{-- Form field button commented out --}}
                                            {{-- @can('manage form field')
                                                <div class="action-btn me-2">
                                                    <a href="{{route('form_builder.show',$form->id)}}" class="mx-3 btn btn-sm align-items-center bg-secondary" data-bs-toggle="tooltip" title="{{__('Form field')}}"><i class="ti ti-table text-white"></i></a>
                                                </div>
                                            @endcan --}}

                                            @can('manage form field')
                                                <div class="action-btn me-2">
                                                    <a href="{{route('form_builder.customize',$form->id)}}" class="mx-3 btn btn-sm align-items-center bg-danger" data-bs-toggle="tooltip" title="{{__('Form Customization')}}"><i class="ti ti-palette text-white"></i></a>
                                                </div>
                                            @endcan

                                            <!-- Three-dot context menu for View Response, Edit, Delete -->
                                            <div class="action-btn me-2">
                                                <div class="dropdown">
                                                    <a href="#" class="mx-3 btn btn-sm align-items-center bg-info" data-bs-toggle="dropdown" aria-expanded="false" data-bs-toggle="tooltip" title="{{__('More Actions')}}">
                                                        <i class="ti ti-dots-vertical text-white"></i>
                                                    </a>
                                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton-{{ $form->id }}">
                                                    @can('view form response')
                                                        <li>
                                                            <a class="dropdown-item" href="{{route('form.response',$form->id)}}">
                                                                <i class="ti ti-eye me-2"></i>{{__('View Response')}}
                                                            </a>
                                                        </li>
                                                    @endcan

                                                    @can('edit form builder')
                                                        <li>
                                                            <a class="dropdown-item" href="#" data-url="{{ route('form_builder.edit',$form->id) }}" data-ajax-popup="true" data-size="md" data-title="{{__('Form Builder Edit')}}">
                                                                <i class="ti ti-pencil me-2"></i>{{__('Edit')}}
                                                            </a>
                                                        </li>
                                                    @endcan

                                                    @can('delete form builder')
                                                        <li>
                                                            {!! Form::open(['method' => 'DELETE', 'route' => ['form_builder.destroy', $form->id],'id'=>'delete-form-'.$form->id, 'style' => 'display: inline;']) !!}
                                                            <a class="dropdown-item text-danger bs-pass-para" href="#">
                                                                <i class="ti ti-trash me-2"></i>{{__('Delete')}}
                                                            </a>
                                                            {!! Form::close() !!}
                                                        </li>
                                                    @endcan
                                                </ul>
                                                </div>
                                            </div>
                                        </td>
                                </tr>
                            @endforeach

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
