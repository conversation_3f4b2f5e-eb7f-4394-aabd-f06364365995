<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('deal_files', function (Blueprint $table) {
            $table->string('original_name')->nullable()->after('file_name');
            $table->string('file_type')->nullable()->after('file_path');
            $table->string('mime_type')->nullable()->after('file_type');
            $table->bigInteger('file_size')->nullable()->after('mime_type');
            $table->unsignedBigInteger('uploaded_by')->nullable()->after('file_size');

            // Add foreign key constraint
            $table->foreign('uploaded_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('deal_files', function (Blueprint $table) {
            $table->dropForeign(['uploaded_by']);
            $table->dropColumn(['original_name', 'file_type', 'mime_type', 'file_size', 'uploaded_by']);
        });
    }
};
