<?php

/**
 * CRM API Test Script
 * 
 * This script tests the CRM API endpoints with SSO token authentication.
 * Run this script from the command line or browser to test the API.
 */

// Configuration
$appUrl = $_ENV['APP_URL'] ?? 'http://localhost:8000';
$baseUrl = $appUrl . '/api'; // Uses APP_URL environment variable
$testEmail = '<EMAIL>'; // Adjust this to a valid user email
$testPassword = '<EMAIL>'; // Adjust this to the user's password

echo "=== CRM API Test Script ===\n\n";

// Step 1: Login to get authentication token
echo "Step 1: Logging in to get authentication token...\n";
$loginData = [
    'email' => $testEmail,
    'password' => $testPassword
];

$loginResponse = makeRequest('POST', $baseUrl . '/login', $loginData);
if (!$loginResponse || !isset($loginResponse['data']['token'])) {
    echo "❌ Login failed. Please check your credentials.\n";
    if ($loginResponse) {
        echo "Response: " . json_encode($loginResponse, JSON_PRETTY_PRINT) . "\n";
    }
    exit(1);
}

$authToken = $loginResponse['data']['token'];
echo "✅ Login successful. Auth token obtained.\n\n";

// Step 2: Generate SSO token
echo "Step 2: Generating SSO token...\n";
$ssoResponse = makeRequest('POST', $baseUrl . '/generate-sso-token', [], [
    'Authorization: Bearer ' . $authToken,
    'Content-Type: application/json'
]);

if (!$ssoResponse || !isset($ssoResponse['token'])) {
    echo "❌ SSO token generation failed.\n";
    if ($ssoResponse) {
        echo "Response: " . json_encode($ssoResponse, JSON_PRETTY_PRINT) . "\n";
    }
    exit(1);
}

$ssoToken = $ssoResponse['token'];
echo "✅ SSO token generated successfully.\n\n";

// Step 3: Test CRM API endpoints
echo "Step 3: Testing CRM API endpoints...\n\n";

$headers = [
    'Authorization: Bearer ' . $ssoToken,
    'Content-Type: application/json'
];

// Test endpoints
$testEndpoints = [
    ['GET', '/crm/leads', null, 'Get all leads'],
    ['GET', '/crm/deals', null, 'Get all deals'],
    ['GET', '/crm/customers', null, 'Get all customers'],
    ['GET', '/crm/pipelines', null, 'Get all pipelines'],
    ['GET', '/crm/stages', null, 'Get all stages'],
    ['GET', '/crm/sources', null, 'Get all sources'],
    ['GET', '/crm/labels', null, 'Get all labels'],
];

foreach ($testEndpoints as $test) {
    [$method, $endpoint, $data, $description] = $test;
    
    echo "Testing: {$description}\n";
    echo "Endpoint: {$method} {$endpoint}\n";
    
    $response = makeRequest($method, $baseUrl . $endpoint, $data, $headers);
    
    if ($response && isset($response['success']) && $response['success']) {
        echo "✅ Success: " . ($response['message'] ?? 'Request completed') . "\n";
        if (isset($response['data'])) {
            echo "   Data count: " . (is_array($response['data']) ? count($response['data']) : 'N/A') . "\n";
        }
    } else {
        echo "❌ Failed: " . ($response['error'] ?? $response['message'] ?? 'Unknown error') . "\n";
    }
    echo "\n";
}

// Step 4: Test creating a new lead (if pipelines and stages exist)
echo "Step 4: Testing lead creation...\n";

// First get pipelines and stages
$pipelinesResponse = makeRequest('GET', $baseUrl . '/crm/pipelines', null, $headers);
$stagesResponse = makeRequest('GET', $baseUrl . '/crm/stages', null, $headers);

if ($pipelinesResponse && isset($pipelinesResponse['data']) && count($pipelinesResponse['data']) > 0 &&
    $stagesResponse && isset($stagesResponse['data']) && count($stagesResponse['data']) > 0) {
    
    $pipeline = $pipelinesResponse['data'][0];
    $stage = null;
    
    // Find a stage for this pipeline
    foreach ($stagesResponse['data'] as $stageItem) {
        if ($stageItem['pipeline_id'] == $pipeline['id']) {
            $stage = $stageItem;
            break;
        }
    }
    
    if ($stage) {
        $leadData = [
            'subject' => 'Test Lead from API',
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'pipeline_id' => $pipeline['id'],
            'stage_id' => $stage['id'],
            'sources' => 'Website',
            'products' => 'Test Product',
            'notes' => 'This is a test lead created via API'
        ];
        
        $createResponse = makeRequest('POST', $baseUrl . '/crm/leads', $leadData, $headers);
        
        if ($createResponse && isset($createResponse['success']) && $createResponse['success']) {
            echo "✅ Lead created successfully!\n";
            echo "   Lead ID: " . $createResponse['data']['id'] . "\n";
            echo "   Lead Name: " . $createResponse['data']['name'] . "\n";
        } else {
            echo "❌ Lead creation failed: " . ($createResponse['error'] ?? 'Unknown error') . "\n";
        }
    } else {
        echo "⚠️  No suitable stage found for pipeline. Skipping lead creation.\n";
    }
} else {
    echo "⚠️  No pipelines or stages found. Skipping lead creation.\n";
}

// Step 6: Test Move Lead API (if we have leads and stages)
echo "\nStep 6: Testing Move Lead API...\n";
$leadsResponse = makeRequest('GET', $baseUrl . '/crm/leads', null, $headers);

if ($leadsResponse && isset($leadsResponse['data']) && count($leadsResponse['data']) > 0 &&
    $stagesResponse && isset($stagesResponse['data']) && count($stagesResponse['data']) > 1) {

    $lead = $leadsResponse['data'][0];
    $currentStageId = $lead['stage_id'];

    // Find a different stage to move to
    $targetStage = null;
    foreach ($stagesResponse['data'] as $stage) {
        if ($stage['id'] != $currentStageId && $stage['pipeline_id'] == $lead['pipeline_id']) {
            $targetStage = $stage;
            break;
        }
    }

    if ($targetStage) {
        $moveData = ['stage_id' => $targetStage['id']];
        $moveResponse = makeRequest('POST', $baseUrl . '/crm/leads/' . $lead['id'] . '/move-stage', $moveData, $headers);

        if ($moveResponse && isset($moveResponse['status']) && $moveResponse['status'] === 'success') {
            echo "✅ Lead moved successfully!\n";
            echo "   Lead ID: " . $moveResponse['lead_id'] . "\n";
            echo "   From Stage: " . $moveResponse['old_stage_id'] . " to " . $moveResponse['new_stage_id'] . "\n";
        } else {
            echo "❌ Lead move failed: " . ($moveResponse['message'] ?? 'Unknown error') . "\n";
        }
    } else {
        echo "⚠️  No suitable target stage found for moving lead.\n";
    }
} else {
    echo "⚠️  No leads or insufficient stages found. Skipping move test.\n";
}

// Step 7: Test Move Deal API (if we have deals and stages)
echo "\nStep 7: Testing Move Deal API...\n";
$dealsResponse = makeRequest('GET', $baseUrl . '/crm/deals', null, $headers);

if ($dealsResponse && isset($dealsResponse['data']) && count($dealsResponse['data']) > 0 &&
    $stagesResponse && isset($stagesResponse['data']) && count($stagesResponse['data']) > 1) {

    $deal = $dealsResponse['data'][0];
    $currentStageId = $deal['stage_id'];

    // Find a different stage to move to
    $targetStage = null;
    foreach ($stagesResponse['data'] as $stage) {
        if ($stage['id'] != $currentStageId && $stage['pipeline_id'] == $deal['pipeline_id']) {
            $targetStage = $stage;
            break;
        }
    }

    if ($targetStage) {
        $moveData = ['stage_id' => $targetStage['id']];
        $moveResponse = makeRequest('POST', $baseUrl . '/crm/deals/' . $deal['id'] . '/move-stage', $moveData, $headers);

        if ($moveResponse && isset($moveResponse['status']) && $moveResponse['status'] === 'success') {
            echo "✅ Deal moved successfully!\n";
            echo "   Deal ID: " . $moveResponse['deal_id'] . "\n";
            echo "   From Stage: " . $moveResponse['old_stage_id'] . " to " . $moveResponse['new_stage_id'] . "\n";
        } else {
            echo "❌ Deal move failed: " . ($moveResponse['message'] ?? 'Unknown error') . "\n";
        }
    } else {
        echo "⚠️  No suitable target stage found for moving deal.\n";
    }
} else {
    echo "⚠️  No deals or insufficient stages found. Skipping move test.\n";
}

echo "\n=== Test completed ===\n";

/**
 * Make HTTP request
 */
function makeRequest($method, $url, $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    if (!empty($headers)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    }
    
    switch (strtoupper($method)) {
        case 'POST':
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
            break;
        case 'PUT':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
            break;
        case 'DELETE':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
            break;
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_error($ch)) {
        echo "cURL Error: " . curl_error($ch) . "\n";
        curl_close($ch);
        return null;
    }
    
    curl_close($ch);
    
    $decodedResponse = json_decode($response, true);
    
    if ($httpCode >= 400) {
        echo "HTTP Error {$httpCode}: " . ($decodedResponse['message'] ?? $response) . "\n";
        if ($decodedResponse) {
            echo "Full response: " . json_encode($decodedResponse, JSON_PRETTY_PRINT) . "\n";
        }
    }

    return $decodedResponse;
}
