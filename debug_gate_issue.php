<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use Illuminate\Support\Facades\Gate;

// Initialize Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Debug Gate Issue ===\n\n";

try {
    $user = User::where('email', '<EMAIL>')->first();
    
    if ($user) {
        echo "User Details:\n";
        echo "   - Name: {$user->name}\n";
        echo "   - Email: {$user->email}\n";
        echo "   - Type: {$user->type}\n";
        echo "   - Plan ID: {$user->plan}\n\n";
        
        // Debug the Gate logic step by step
        echo "Gate Logic Debug:\n";
        
        // Check if system admin
        $isSystemAdmin = $user->type === 'system admin';
        echo "   - Is system admin: " . ($isSystemAdmin ? 'YES' : 'NO') . "\n";
        
        if ($isSystemAdmin) {
            echo "   - Gate should return TRUE (system admin always has access)\n";
        } else {
            echo "   - Checking hasModulePermission...\n";
            $hasModulePermission = $user->hasModulePermission('omx_flow', 'access omx flow');
            echo "   - hasModulePermission result: " . ($hasModulePermission ? 'YES' : 'NO') . "\n";
            echo "   - Gate should return: " . ($hasModulePermission ? 'TRUE' : 'FALSE') . "\n";
        }
        
        // Actual Gate result
        $actualGateResult = Gate::forUser($user)->check('access omx flow');
        echo "   - Actual Gate result: " . ($actualGateResult ? 'TRUE' : 'FALSE') . "\n\n";
        
        // Check if there's a mismatch
        $expectedResult = $isSystemAdmin || $user->hasModulePermission('omx_flow', 'access omx flow');
        if ($actualGateResult !== $expectedResult) {
            echo "❌ MISMATCH DETECTED!\n";
            echo "   - Expected: " . ($expectedResult ? 'TRUE' : 'FALSE') . "\n";
            echo "   - Actual: " . ($actualGateResult ? 'TRUE' : 'FALSE') . "\n";
        } else {
            echo "✅ Gate logic is working correctly\n";
        }
        
        // Check if user has traditional Spatie permissions
        echo "\nTraditional Permission Check:\n";
        $hasTraditionalPermission = $user->can('access omx flow');
        echo "   - user->can('access omx flow'): " . ($hasTraditionalPermission ? 'YES' : 'NO') . "\n";
        
        // Check user's direct permissions
        echo "   - User's direct permissions: " . $user->permissions->pluck('name')->implode(', ') . "\n";
        
        // Check user's roles and role permissions
        $roles = $user->roles;
        echo "   - User's roles: " . $roles->pluck('name')->implode(', ') . "\n";
        foreach ($roles as $role) {
            echo "   - Role '{$role->name}' permissions: " . $role->permissions->pluck('name')->implode(', ') . "\n";
        }
        
    } else {
        echo "❌ User not found\n";
    }

} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Debug Complete ===\n";
