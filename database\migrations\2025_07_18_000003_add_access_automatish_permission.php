<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create the 'access automatish' permission if it doesn't exist
        $permission = Permission::where('name', 'access automatish')->first();
        if (!$permission) {
            Permission::create(['name' => 'access automatish']);
        }

        // Clear permission cache
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Assign the permission to admin roles
        $adminRoles = ['super admin', 'system admin', 'company'];
        
        foreach ($adminRoles as $roleName) {
            $role = Role::where('name', $roleName)->first();
            if ($role) {
                $permission = Permission::where('name', 'access automatish')->first();
                if ($permission && !$role->hasPermissionTo($permission)) {
                    $role->givePermissionTo($permission);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the permission from all roles
        $permission = Permission::where('name', 'access automatish')->first();
        if ($permission) {
            // Remove from all roles
            $roles = Role::all();
            foreach ($roles as $role) {
                if ($role->hasPermissionTo($permission)) {
                    $role->revokePermissionTo($permission);
                }
            }
            
            // Delete the permission
            $permission->delete();
        }

        // Clear permission cache
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
    }
};
