@extends('layouts.admin')

@section('page-title')
    {{ __('Create Staff') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('system-admin.dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ route('system-admin.staff.index') }}">{{ __('Staff Management') }}</a></li>
    <li class="breadcrumb-item">{{ __('Create Staff') }}</li>
@endsection

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('Create New Staff Member') }}</h5>
                    <small class="text-muted">{{ __('Company: ') . $company->name }}</small>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('system-admin.staff.store') }}">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name" class="form-label">{{ __('Name') }} <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email" class="form-label">{{ __('Email') }} <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                           id="email" name="email" value="{{ old('email') }}" required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="password" class="form-label">{{ __('Password') }} <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                           id="password" name="password" required>
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Permissions Section -->
                        <div class="row">
                            <div class="col-12">
                                <h6 class="mb-3">{{ __('Staff Permissions') }}</h6>
                                <div class="alert alert-info">
                                    <i class="ti ti-info-circle"></i>
                                    {{ __('Select the permissions you want to grant to this staff member. These permissions will determine what sections of the system admin panel they can access.') }}
                                </div>
                            </div>
                        </div>

                        @foreach($allPermissions as $module => $permissions)
                            <div class="row mb-4">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">{{ $module }}</h6>
                                            <div class="float-end">
                                                <button type="button" class="btn btn-sm btn-outline-primary select-all-btn">
                                                    {{ __('Select All') }}
                                                </button>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                @foreach($permissions as $permission)
                                                    <div class="col-md-6 mb-2">
                                                        <div class="form-check">
                                                            <input class="form-check-input"
                                                                   type="checkbox"
                                                                   name="permissions[]"
                                                                   value="{{ $permission }}"
                                                                   id="permission_{{ str_replace(' ', '_', $permission) }}"
                                                                   {{ in_array($permission, old('permissions', [])) ? 'checked' : '' }}>
                                                            <label class="form-check-label" for="permission_{{ str_replace(' ', '_', $permission) }}">
                                                                {{ ucfirst($permission) }}
                                                            </label>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach

                        <div class="row">
                            <div class="col-12">
                                <div class="text-end">
                                    <a href="{{ route('system-admin.staff.index') }}" class="btn btn-secondary">
                                        {{ __('Cancel') }}
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        {{ __('Create Staff') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<script>
    $(document).ready(function() {
        // Handle select all button clicks
        $(document).on('click', '.select-all-btn', function() {
            const button = $(this);
            const cardBody = button.closest('.card').find('.card-body');
            const checkboxes = cardBody.find('input[type="checkbox"]');
            const allChecked = checkboxes.filter(':checked').length === checkboxes.length;

            checkboxes.prop('checked', !allChecked);
            button.text(allChecked ? '{{ __("Select All") }}' : '{{ __("Deselect All") }}');
        });

        // Update select all button text when individual checkboxes change
        $(document).on('change', 'input[type="checkbox"][name="permissions[]"]', function() {
            const checkbox = $(this);
            const cardBody = checkbox.closest('.card-body');
            const card = cardBody.closest('.card');
            const selectAllBtn = card.find('.select-all-btn');
            const checkboxes = cardBody.find('input[type="checkbox"]');
            const allChecked = checkboxes.filter(':checked').length === checkboxes.length;

            selectAllBtn.text(allChecked ? '{{ __("Deselect All") }}' : '{{ __("Select All") }}');
        });
    });
</script>
@endpush


