<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * Webhook Logger Service
 * 
 * This service handles logging of webhook calls to a dedicated log file.
 * It provides structured logging for webhook events with different log levels.
 */
class WebhookLogger
{
    /**
     * The log channel name for webhooks
     */
    const LOG_CHANNEL = 'webhook';
    
    /**
     * Log a successful webhook call
     * 
     * @param string $action
     * @param string $moduleName
     * @param string $webhookUrl
     * @param array $requestPayload
     * @param array $response
     * @param int $responseTimeMs
     * @param int|null $userId
     * @param int|null $entityId
     * @param string|null $entityType
     */
    public function logSuccess($action, $moduleName, $webhookUrl, $requestPayload, $response, $responseTimeMs = null, $userId = null, $entityId = null, $entityType = null)
    {
        $context = $this->buildLogContext([
            'action' => $action,
            'module_name' => $moduleName,
            'webhook_url' => $webhookUrl,
            'status' => 'success',
            'status_code' => $response['status_code'] ?? null,
            'response_time_ms' => $responseTimeMs,
            'user_id' => $userId,
            'entity_id' => $entityId,
            'entity_type' => $entityType,
            'request_payload' => $requestPayload,
            'response' => $response['response'] ?? null,
        ]);
        
        Log::channel(self::LOG_CHANNEL)->info("Webhook sent successfully to {$moduleName}", $context);
    }
    
    /**
     * Log a failed webhook call
     * 
     * @param string $action
     * @param string $moduleName
     * @param string $webhookUrl
     * @param array $requestPayload
     * @param string $errorMessage
     * @param int|null $statusCode
     * @param string|null $responseBody
     * @param int|null $responseTimeMs
     * @param int|null $userId
     * @param int|null $entityId
     * @param string|null $entityType
     */
    public function logFailure($action, $moduleName, $webhookUrl, $requestPayload, $errorMessage, $statusCode = null, $responseBody = null, $responseTimeMs = null, $userId = null, $entityId = null, $entityType = null)
    {
        $context = $this->buildLogContext([
            'action' => $action,
            'module_name' => $moduleName,
            'webhook_url' => $webhookUrl,
            'status' => 'failed',
            'status_code' => $statusCode,
            'response_time_ms' => $responseTimeMs,
            'user_id' => $userId,
            'entity_id' => $entityId,
            'entity_type' => $entityType,
            'request_payload' => $requestPayload,
            'error_message' => $errorMessage,
            'response_body' => $responseBody,
        ]);
        
        Log::channel(self::LOG_CHANNEL)->error("Webhook failed for {$moduleName}: {$errorMessage}", $context);
    }
    
    /**
     * Log a webhook timeout
     * 
     * @param string $action
     * @param string $moduleName
     * @param string $webhookUrl
     * @param array $requestPayload
     * @param int|null $userId
     * @param int|null $entityId
     * @param string|null $entityType
     */
    public function logTimeout($action, $moduleName, $webhookUrl, $requestPayload, $userId = null, $entityId = null, $entityType = null)
    {
        $context = $this->buildLogContext([
            'action' => $action,
            'module_name' => $moduleName,
            'webhook_url' => $webhookUrl,
            'status' => 'timeout',
            'user_id' => $userId,
            'entity_id' => $entityId,
            'entity_type' => $entityType,
            'request_payload' => $requestPayload,
            'error_message' => 'Request timed out',
        ]);
        
        Log::channel(self::LOG_CHANNEL)->warning("Webhook timed out for {$moduleName}", $context);
    }
    
    /**
     * Log webhook dispatch start
     * 
     * @param string $action
     * @param int|null $userId
     * @param string|null $entityType
     * @param int|null $entityId
     */
    public function logDispatchStart($action, $userId = null, $entityType = null, $entityId = null)
    {
        $context = $this->buildLogContext([
            'action' => $action,
            'user_id' => $userId,
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'status' => 'dispatching',
        ]);
        
        Log::channel(self::LOG_CHANNEL)->info("Starting webhook dispatch for action: {$action}", $context);
    }
    
    /**
     * Log webhook dispatch completion
     * 
     * @param string $action
     * @param array $results
     * @param int|null $userId
     */
    public function logDispatchComplete($action, $results, $userId = null)
    {
        $successCount = 0;
        $failureCount = 0;
        $modules = [];
        
        foreach ($results as $moduleName => $result) {
            $modules[] = $moduleName;
            if (isset($result['success']) && $result['success']) {
                $successCount++;
            } else {
                $failureCount++;
            }
        }
        
        $context = $this->buildLogContext([
            'action' => $action,
            'user_id' => $userId,
            'status' => 'completed',
            'total_modules' => count($modules),
            'successful_modules' => $successCount,
            'failed_modules' => $failureCount,
            'modules' => $modules,
            'results' => $results,
        ]);
        
        $level = $failureCount > 0 ? 'warning' : 'info';
        $message = "Webhook dispatch completed for action: {$action}. Success: {$successCount}, Failed: {$failureCount}";
        
        Log::channel(self::LOG_CHANNEL)->$level($message, $context);
    }
    
    /**
     * Log webhook test
     * 
     * @param string $moduleName
     * @param array $result
     */
    public function logTest($moduleName, $result)
    {
        $context = $this->buildLogContext([
            'action' => 'test_webhook',
            'module_name' => $moduleName,
            'status' => $result['success'] ? 'success' : 'failed',
            'result' => $result,
        ]);
        
        if ($result['success']) {
            Log::channel(self::LOG_CHANNEL)->info("Webhook test successful for {$moduleName}", $context);
        } else {
            Log::channel(self::LOG_CHANNEL)->error("Webhook test failed for {$moduleName}", $context);
        }
    }
    
    /**
     * Log general webhook information
     * 
     * @param string $message
     * @param array $context
     */
    public function logInfo($message, $context = [])
    {
        Log::channel(self::LOG_CHANNEL)->info($message, $this->buildLogContext($context));
    }
    
    /**
     * Log webhook warnings
     * 
     * @param string $message
     * @param array $context
     */
    public function logWarning($message, $context = [])
    {
        Log::channel(self::LOG_CHANNEL)->warning($message, $this->buildLogContext($context));
    }
    
    /**
     * Log webhook errors
     * 
     * @param string $message
     * @param array $context
     */
    public function logError($message, $context = [])
    {
        Log::channel(self::LOG_CHANNEL)->error($message, $this->buildLogContext($context));
    }
    
    /**
     * Build standardized log context
     * 
     * @param array $data
     * @return array
     */
    private function buildLogContext($data)
    {
        return array_merge([
            'timestamp' => Carbon::now()->toISOString(),
            'source' => 'crm_webhook_system',
        ], $data);
    }
    
    /**
     * Get webhook log file path
     * 
     * @return string
     */
    public static function getLogFilePath()
    {
        return storage_path('logs/webhook.log');
    }
    
    /**
     * Clear webhook logs (truncate the log file)
     * 
     * @return bool
     */
    public static function clearLogs()
    {
        $logFile = self::getLogFilePath();
        
        if (file_exists($logFile)) {
            return file_put_contents($logFile, '') !== false;
        }
        
        return true;
    }
    
    /**
     * Get recent webhook logs
     * 
     * @param int $lines Number of lines to read from the end
     * @return array
     */
    public static function getRecentLogs($lines = 100)
    {
        $logFile = self::getLogFilePath();
        
        if (!file_exists($logFile)) {
            return [];
        }
        
        $command = "tail -n {$lines} " . escapeshellarg($logFile);
        $output = shell_exec($command);
        
        if ($output === null) {
            return [];
        }
        
        return array_filter(explode("\n", $output));
    }
}
