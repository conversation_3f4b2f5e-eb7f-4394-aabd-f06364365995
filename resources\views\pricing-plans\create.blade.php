 @extends('layouts.admin')

@section('page-title')
    {{ __('Create Pricing Plan') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('system-admin.dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ route('pricing-plans.index') }}">{{ __('Pricing Plans') }}</a></li>
    <li class="breadcrumb-item">{{ __('Create') }}</li>
@endsection

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('Create New Pricing Plan') }}</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('pricing-plans.store') }}">
                        @csrf
                        
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name" class="form-label">{{ __('Plan Name') }} <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="plan_type" class="form-label">{{ __('Plan Type') }} <span class="text-danger">*</span></label>
                                    <select class="form-control @error('plan_type') is-invalid @enderror" 
                                            id="plan_type" name="plan_type" required>
                                        <option value="">{{ __('Select Plan Type') }}</option>
                                        @foreach($planTypeOptions as $key => $value)
                                            <option value="{{ $key }}" {{ old('plan_type') == $key ? 'selected' : '' }}>
                                                {{ $value }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('plan_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="price" class="form-label">{{ __('Price') }} <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" step="0.01" min="0" 
                                               class="form-control @error('price') is-invalid @enderror" 
                                               id="price" name="price" value="{{ old('price') }}" required>
                                    </div>
                                    @error('price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="duration" class="form-label">{{ __('Duration') }} <span class="text-danger">*</span></label>
                                    <select class="form-control @error('duration') is-invalid @enderror" 
                                            id="duration" name="duration" required>
                                        <option value="">{{ __('Select Duration') }}</option>
                                        @foreach($durationOptions as $key => $value)
                                            <option value="{{ $key }}" {{ old('duration') == $key ? 'selected' : '' }}>
                                                {{ $value }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('duration')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status" class="form-label">{{ __('Status') }} <span class="text-danger">*</span></label>
                                    <select class="form-control @error('status') is-invalid @enderror" 
                                            id="status" name="status" required>
                                        @foreach($statusOptions as $key => $value)
                                            <option value="{{ $key }}" {{ old('status', 'active') == $key ? 'selected' : '' }}>
                                                {{ $value }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="form-group">
                                    <label for="description" class="form-label">{{ __('Description') }}</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" 
                                              id="description" name="description" rows="3">{{ old('description') }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Limits -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="max_users" class="form-label">{{ __('Max Users') }} <span class="text-danger">*</span></label>
                                    <input type="number" min="0" 
                                           class="form-control @error('max_users') is-invalid @enderror" 
                                           id="max_users" name="max_users" value="{{ old('max_users', 0) }}" required>
                                    <small class="text-muted">{{ __('Use -1 for unlimited') }}</small>
                                    @error('max_users')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="max_customers" class="form-label">{{ __('Max Customers') }} <span class="text-danger">*</span></label>
                                    <input type="number" min="0" 
                                           class="form-control @error('max_customers') is-invalid @enderror" 
                                           id="max_customers" name="max_customers" value="{{ old('max_customers', 0) }}" required>
                                    <small class="text-muted">{{ __('Use -1 for unlimited') }}</small>
                                    @error('max_customers')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="max_vendors" class="form-label">{{ __('Max Vendors') }} <span class="text-danger">*</span></label>
                                    <input type="number" min="0" 
                                           class="form-control @error('max_vendors') is-invalid @enderror" 
                                           id="max_vendors" name="max_vendors" value="{{ old('max_vendors', 0) }}" required>
                                    <small class="text-muted">{{ __('Use -1 for unlimited') }}</small>
                                    @error('max_vendors')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="max_clients" class="form-label">{{ __('Max Clients') }} <span class="text-danger">*</span></label>
                                    <input type="number" min="0" 
                                           class="form-control @error('max_clients') is-invalid @enderror" 
                                           id="max_clients" name="max_clients" value="{{ old('max_clients', 0) }}" required>
                                    <small class="text-muted">{{ __('Use -1 for unlimited') }}</small>
                                    @error('max_clients')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="storage_limit" class="form-label">{{ __('Storage Limit (GB)') }} <span class="text-danger">*</span></label>
                                    <input type="number" step="0.1" min="0" 
                                           class="form-control @error('storage_limit') is-invalid @enderror" 
                                           id="storage_limit" name="storage_limit" value="{{ old('storage_limit', 0) }}" required>
                                    <small class="text-muted">{{ __('Use -1 for unlimited') }}</small>
                                    @error('storage_limit')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="sort_order" class="form-label">{{ __('Sort Order') }}</label>
                                    <input type="number" min="0" 
                                           class="form-control @error('sort_order') is-invalid @enderror" 
                                           id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}">
                                    @error('sort_order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>


                            <!-- Module Permissions -->
                            <div class="col-12 mt-4">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5>{{ __('Module Permissions') }}</h5>
                                    <div>
                                        <button type="button" class="btn btn-sm btn-outline-primary me-2" id="selectAllModules">
                                            {{ __('Select All Modules') }}
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" id="deselectAllModules">
                                            {{ __('Deselect All') }}
                                        </button>
                                    </div>
                                </div>
                                <hr>
                                
                                @foreach($availableModules as $moduleKey => $moduleData)
                                    @php
                                        // Categorize permissions
                                        $createPermissions = [];
                                        $readPermissions = [];
                                        $deletePermissions = [];
                                        $otherPermissions = [];
                                        
                                        foreach($moduleData['permissions'] as $permission) {
                                            if (str_contains($permission, 'create')) {
                                                $createPermissions[] = $permission;
                                            } elseif (str_contains($permission, 'view') || str_contains($permission, 'show') || str_contains($permission, 'dashboard')) {
                                                $readPermissions[] = $permission;
                                            } elseif (str_contains($permission, 'delete')) {
                                                $deletePermissions[] = $permission;
                                            } else {
                                                $otherPermissions[] = $permission;
                                            }
                                        }
                                    @endphp
                                    
                                    <div class="card mb-3">
                                        <div class="card-header">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="form-check">
                                                    <input class="form-check-input module-checkbox" type="checkbox" 
                                                           id="module_{{ $moduleKey }}" name="modules[{{ $moduleKey }}]" 
                                                           data-module="{{ $moduleKey }}">
                                                    <label class="form-check-label fw-bold" for="module_{{ $moduleKey }}">
                                                        {{ $moduleData['name'] }}
                                                    </label>
                                                </div>
                                                <div class="btn-group" role="group" style="display: none;" id="selectButtons_{{ $moduleKey }}">
                                                    <button type="button" class="btn btn-sm btn-outline-success select-all-module" 
                                                            data-module="{{ $moduleKey }}">
                                                        {{ __('Select All') }}
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-warning select-create" 
                                                            data-module="{{ $moduleKey }}">
                                                        {{ __('Create') }}
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-info select-read" 
                                                            data-module="{{ $moduleKey }}">
                                                        {{ __('Read') }}
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger select-delete" 
                                                            data-module="{{ $moduleKey }}">
                                                        {{ __('Delete') }}
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body permissions-section" id="permissions_{{ $moduleKey }}" style="display: none;">
                                            
                                            @if(count($createPermissions) > 0)
                                                <div class="permission-group mb-4">
                                                    <h6 class="text-success mb-2">
                                                        <i class="fas fa-plus-circle"></i> {{ __('Create Permissions') }}
                                                    </h6>
                                                    <div class="row">
                                                        @foreach($createPermissions as $permission)
                                                            <div class="col-md-4 mb-2">
                                                                <div class="form-check">
                                                                    <input class="form-check-input permission-checkbox create-permission" type="checkbox" 
                                                                           id="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}" 
                                                                           name="permissions[{{ $moduleKey }}][]" 
                                                                           value="{{ $permission }}"
                                                                           data-module="{{ $moduleKey }}"
                                                                           data-type="create">
                                                                    <label class="form-check-label" 
                                                                           for="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}">
                                                                        {{ ucwords(str_replace('_', ' ', $permission)) }}
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            @endif

                                            @if(count($readPermissions) > 0)
                                                <div class="permission-group mb-4">
                                                    <h6 class="text-info mb-2">
                                                        <i class="fas fa-eye"></i> {{ __('Read/View Permissions') }}
                                                    </h6>
                                                    <div class="row">
                                                        @foreach($readPermissions as $permission)
                                                            <div class="col-md-4 mb-2">
                                                                <div class="form-check">
                                                                    <input class="form-check-input permission-checkbox read-permission" type="checkbox" 
                                                                           id="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}" 
                                                                           name="permissions[{{ $moduleKey }}][]" 
                                                                           value="{{ $permission }}"
                                                                           data-module="{{ $moduleKey }}"
                                                                           data-type="read">
                                                                    <label class="form-check-label" 
                                                                           for="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}">
                                                                        {{ ucwords(str_replace('_', ' ', $permission)) }}
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            @endif

                                            @if(count($deletePermissions) > 0)
                                                <div class="permission-group mb-4">
                                                    <h6 class="text-danger mb-2">
                                                        <i class="fas fa-trash"></i> {{ __('Delete Permissions') }}
                                                    </h6>
                                                    <div class="row">
                                                        @foreach($deletePermissions as $permission)
                                                            <div class="col-md-4 mb-2">
                                                                <div class="form-check">
                                                                    <input class="form-check-input permission-checkbox delete-permission" type="checkbox" 
                                                                           id="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}" 
                                                                           name="permissions[{{ $moduleKey }}][]" 
                                                                           value="{{ $permission }}"
                                                                           data-module="{{ $moduleKey }}"
                                                                           data-type="delete">
                                                                    <label class="form-check-label" 
                                                                           for="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}">
                                                                        {{ ucwords(str_replace('_', ' ', $permission)) }}
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            @endif

                                            @if(count($otherPermissions) > 0)
                                                <div class="permission-group mb-4">
                                                    <h6 class="text-secondary mb-2">
                                                        <i class="fas fa-cog"></i> {{ __('Other Permissions') }}
                                                    </h6>
                                                    <div class="row">
                                                        @foreach($otherPermissions as $permission)
                                                            <div class="col-md-4 mb-2">
                                                                <div class="form-check">
                                                                    <input class="form-check-input permission-checkbox other-permission" type="checkbox" 
                                                                           id="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}" 
                                                                           name="permissions[{{ $moduleKey }}][]" 
                                                                           value="{{ $permission }}"
                                                                           data-module="{{ $moduleKey }}"
                                                                           data-type="other">
                                                                    <label class="form-check-label" 
                                                                           for="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}">
                                                                        {{ ucwords(str_replace('_', ' ', $permission)) }}
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">{{ __('Create Plan') }}</button>
                                <a href="{{ route('pricing-plans.index') }}" class="btn btn-secondary">{{ __('Cancel') }}</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
    <script>
        $(document).ready(function() {
            // Module checkbox toggle
            $('.module-checkbox').on('change', function() {
                var module = $(this).data('module');
                var permissionsSection = $('#permissions_' + module);
                var permissionCheckboxes = permissionsSection.find('.permission-checkbox');
                var selectButtons = $('#selectButtons_' + module);
                
                if ($(this).is(':checked')) {
                    permissionsSection.show();
                    selectButtons.show();
                } else {
                    permissionsSection.hide();
                    selectButtons.hide();
                    permissionCheckboxes.prop('checked', false);
                }
            });

            // If any permission is checked, ensure module is checked
            $('.permission-checkbox').on('change', function() {
                var module = $(this).data('module');
                var moduleCheckbox = $('#module_' + module);
                var permissionCheckboxes = $('input[name="permissions[' + module + '][]"]');
                var checkedPermissions = permissionCheckboxes.filter(':checked');
                var selectButtons = $('#selectButtons_' + module);
                
                if (checkedPermissions.length > 0) {
                    moduleCheckbox.prop('checked', true);
                    $('#permissions_' + module).show();
                    selectButtons.show();
                } else {
                    moduleCheckbox.prop('checked', false);
                    $('#permissions_' + module).hide();
                    selectButtons.hide();
                }
            });

            // Select All Modules
            $('#selectAllModules').on('click', function() {
                $('.module-checkbox').prop('checked', true).trigger('change');
            });

            // Deselect All Modules
            $('#deselectAllModules').on('click', function() {
                $('.module-checkbox').prop('checked', false).trigger('change');
            });

            // Select All permissions for a specific module
            $('.select-all-module').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .permission-checkbox').prop('checked', true);
            });

            // Select Create permissions for a specific module
            $('.select-create').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .create-permission').prop('checked', true);
            });

            // Select Read permissions for a specific module
            $('.select-read').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .read-permission').prop('checked', true);
            });

            // Select Delete permissions for a specific module
            $('.select-delete').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .delete-permission').prop('checked', true);
            });
        });
    </script>
@endpush
