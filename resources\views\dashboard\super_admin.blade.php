@extends('layouts.admin')
@section('page-title')
    {{ __('Dashboard') }}
@endsection

@push('css-page')
    <style>
        .apexcharts-yaxis {
            transform: translate(20px, 0px) !important;
        }
        .color-palette .color-swatch {
            border: 2px solid #fff;
            box-shadow: 0 0 0 1px #ccc;
            transition: box-shadow 0.2s, border 0.2s;
            cursor: pointer;
        }
        .color-palette .color-swatch.selected {
            border: 2px solid #1976d2;
            box-shadow: 0 0 0 2px #1976d2;
        }
        @media (max-width: 767px) {
            .color-palette .color-swatch {
                width: 28px !important;
                height: 28px !important;
            }
        }
    </style>
@endpush

@push('theme-script')
    <script src="{{ asset('assets/libs/apexcharts/dist/apexcharts.min.js') }}"></script>
@endpush
@push('script-page')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Chart type options for each chart
const chartTypeOptions = ['pie', 'bar', 'line', 'doughnut', 'horizontalBar'];
const chartTypeLabels = {
    pie: 'Pie',
    bar: 'Bar',
    line: 'Line',
    doughnut: 'Donut',
    horizontalBar: 'Horizontal Bar'
};
// Default chart data for each chart (should match your real data)
const chartDataSets = {
    usersPieChart: {
        labels: ['Paid Users', 'Free Users'],
        data: [{{ $user['total_paid_user'] }}, {{ $user->total_user - $user['total_paid_user'] }}],
        backgroundColor: ['#0CAF60', '#6fd944']
    },
    ordersPieChart: {
        labels: ['Completed', 'Pending', 'Cancelled'],
        data: [70, 20, 10], // Example data - replace with actual data
        backgroundColor: ['#2e7d32', '#ffc107', '#f44336']
    },
    plansPieChart: {
        labels: ['Basic', 'Standard', 'Premium', 'Enterprise'],
        data: [40, 30, 20, 10], // Example data - replace with actual data
        backgroundColor: ['#2e7d32', '#4caf50', '#81c784', '#c8e6c9']
    }
};

let currentChartKey = null;
let previewChart = null;
let mainCharts = {};

function getStoredChartType(chartKey) {
    return localStorage.getItem('chartType_' + chartKey) || 'doughnut';
}
function setStoredChartType(chartKey, type) {
    localStorage.setItem('chartType_' + chartKey, type);
}

function getChartJsType(type) {
    if(type === 'horizontalBar') return 'bar'; // Chart.js v3+ uses 'bar' with indexAxis: 'y'
    return type;
}
function getChartJsOptions(type) {
    if(type === 'horizontalBar') {
        return {
            indexAxis: 'y',
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { position: 'right' } }
        };
    }
    return {
        responsive: true,
        maintainAspectRatio: false,
        plugins: { legend: { position: 'right' } }
    };
}

function renderChartJs(ctx, chartKey, type, colorOverride = null) {
    const dataSet = chartDataSets[chartKey];
    let backgroundColor = dataSet.backgroundColor;
    if (colorOverride) {
        backgroundColor = dataSet.backgroundColor.map(() => colorOverride);
    } else {
        backgroundColor = getChartColors(chartKey, dataSet.backgroundColor);
    }
    return new Chart(ctx, {
        type: getChartJsType(type),
        data: {
            labels: dataSet.labels,
            datasets: [{
                data: dataSet.data,
                backgroundColor: backgroundColor
            }]
        },
        options: getChartJsOptions(type)
    });
}

// Add color palette logic
const chartColorOptions = [
    '#0CAF60', '#2e7d32', '#ffc107', '#f44336', '#4caf50',
    '#81c784', '#c8e6c9', '#6fd944', '#1976d2', '#ff9800'
];
let selectedColor = null;

function getStoredChartColor(chartKey) {
    return localStorage.getItem('chartColor_' + chartKey) || null;
}
function setStoredChartColor(chartKey, color) {
    localStorage.setItem('chartColor_' + chartKey, color);
}

function getChartColors(chartKey, baseColors) {
    const stored = getStoredChartColor(chartKey);
    if (stored) {
        // For single-color charts, use selected color; for multi, apply to all
        return baseColors.map(() => stored);
    }
    return baseColors;
}

// Open modal with correct chart type and color
$(document).on('click', '.chart-settings-btn', function() {
    currentChartKey = $(this).data('chart');
    const savedType = getStoredChartType(currentChartKey);
    const savedColor = getStoredChartColor(currentChartKey);
    $('.chart-type-option').removeClass('active');
    $(`.chart-type-option[data-type="${savedType}"]`).addClass('active');
    // Color palette
    $('.color-swatch').removeClass('selected');
    if(savedColor) {
        $(`.color-swatch[data-color="${savedColor}"]`).addClass('selected');
        selectedColor = savedColor;
    } else {
        selectedColor = null;
    }
    // Render preview
    setTimeout(() => {
        if(previewChart) previewChart.destroy();
        const ctx = document.getElementById('chartPreviewCanvas').getContext('2d');
        previewChart = renderChartJs(ctx, currentChartKey, savedType, selectedColor);
    }, 200);
});
// Change preview on chart type select
$(document).on('click', '.chart-type-option', function() {
    $('.chart-type-option').removeClass('active');
    $(this).addClass('active');
    const type = $(this).data('type');
    if(previewChart) previewChart.destroy();
    const ctx = document.getElementById('chartPreviewCanvas').getContext('2d');
    previewChart = renderChartJs(ctx, currentChartKey, type, selectedColor);
});
// Color palette click
$(document).on('click', '.color-swatch', function() {
    $('.color-swatch').removeClass('selected');
    $(this).addClass('selected');
    selectedColor = $(this).data('color');
    // Update preview
    const type = $('.chart-type-option.active').data('type');
    if(previewChart) previewChart.destroy();
    const ctx = document.getElementById('chartPreviewCanvas').getContext('2d');
    previewChart = renderChartJs(ctx, currentChartKey, type, selectedColor);
});
// Reset color button
$(document).on('click', '.reset-color-btn', function() {
    $('.color-swatch').removeClass('selected');
    selectedColor = null;
    // Update preview to default color
    const type = $('.chart-type-option.active').data('type');
    if(previewChart) previewChart.destroy();
    const ctx = document.getElementById('chartPreviewCanvas').getContext('2d');
    previewChart = renderChartJs(ctx, currentChartKey, type, null);
});
// Save and update main chart
$('#saveChartTypeBtn').on('click', function() {
    const selectedType = $('.chart-type-option.active').data('type');
    setStoredChartType(currentChartKey, selectedType);
    if(selectedColor) setStoredChartColor(currentChartKey, selectedColor);
    else localStorage.removeItem('chartColor_' + currentChartKey);
    // Update main chart
    if(mainCharts[currentChartKey]) mainCharts[currentChartKey].destroy();
    const ctx = document.getElementById(currentChartKey).getContext('2d');
    mainCharts[currentChartKey] = renderChartJs(ctx, currentChartKey, selectedType, selectedColor);
    $('#chartSettingsModal').modal('hide');
});
// Initialize all main charts on page load
function initAllMainCharts() {
    Object.keys(chartDataSets).forEach(chartKey => {
        const type = getStoredChartType(chartKey);
        const color = getStoredChartColor(chartKey);
        const ctx = document.getElementById(chartKey);
        if(ctx) {
            mainCharts[chartKey] = renderChartJs(ctx.getContext('2d'), chartKey, type, color);
        }
    });
}
// Load Chart.js if not loaded, then init
if (typeof Chart === 'undefined') {
    var script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
    script.onload = initAllMainCharts;
    document.head.appendChild(script);
} else {
    initAllMainCharts();
}
</script>
@endpush
@php
    $admin_payment_setting = Utility::getAdminPaymentSetting();
@endphp

@section('content')
<div class="row mb-4 gy-4">
    <div class="col-xxl-4 col-md-6 col-12 project-dash-card">
        <div class="project-card-inner card mb-0 p-3 d-flex flex-column justify-content-between" style="height: 100%; box-shadow: 0 4px 20px rgba(46, 125, 50, 0.2); border-left: 4px solid #2e7d32;">
            <!-- Top Content -->
            <div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <i class="fas fa-building fa-2x" style="color: #0caf60;"></i>
                    <div class="text-end d-flex align-items-center justify-content-end gap-2">
                        <span>{{ $user['total_paid_user'] }}</span>
                        <p class="mb-0">{{ __('Paid Users') }}</p>
                    </div>
                </div>
            </div>
            <!-- Bottom Content -->
            <div class="mt-4 pt-2">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="h5 text-black m-0">
                        <a href="{{ route('users.index') }}" class="text-black">
                            {{ __('Companies ') }}<i class="fas fa-external-link-alt" style="font-size: 0.8rem;"></i>
                        </a>
                    </h2>
                    <h3 class="h5 mb-0" style="margin-right: 10px;">{{ __('Total: ') }}{{ $user->total_user }}</h3>
                </div>
            </div>
        </div>
    </div>  
    <div class="col-xxl-4 col-md-6 col-12 project-dash-card">
        <div class="project-card-inner card mb-0 p-3 d-flex flex-column justify-content-between" style="height: 100%; box-shadow: 0 4px 20px rgba(46, 125, 50, 0.2); border-left: 4px solid #2e7d32;">
            <!-- Top Content -->
            <div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <i class="fas fa-shopping-cart fa-2x" style="color: #0caf60;"></i>
                    <div class="text-end d-flex align-items-center justify-content-end gap-2">
                        <span>{{ env('CURRENCY_SYMBOL') }}{{ $user['total_orders_price'] }}</span>
                        <p class="mb-0">{{ __('Total Amount') }}</p>
                    </div>
                </div>
            </div>
            <!-- Bottom Content -->
            <div class="mt-4 pt-2">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="h5 text-black m-0">
                        <a href="{{ route('order.index') }}" class="text-black">
                            {{ __('Orders ') }}<i class="fas fa-external-link-alt" style="font-size: 0.8rem;"></i>
                        </a>
                    </h2>
                    <h3 class="h5 mb-0" style="margin-right: 10px;">{{ __('Total: ') }}{{ $user->total_orders }}</h3>
                </div>  
            </div>
        </div>
    </div>
    <div class="col-xxl-4 col-md-6 col-12 project-dash-card">
        <div class="project-card-inner card mb-0 p-3 d-flex flex-column justify-content-between" style="height: 100%; box-shadow: 0 4px 20px rgba(46, 125, 50, 0.2); border-left: 4px solid #2e7d32;">
            <!-- Top Content -->
            <div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <i class="fas fa-crown fa-2x" style="color: #0caf60;"></i>
                    <div class="text-end d-flex align-items-center justify-content-end gap-2">
                        <span>{{ $user['most_purchese_plan'] }}</span>
                        <p class="mb-0">{{ __('Most Popular') }}</p>
                    </div>
                </div>
            </div>
            <!-- Bottom Content -->
            <div class="mt-4 pt-2">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="h5 text-black m-0">
                        <a href="{{ route('pricing-plans.index') }}" class="text-black">
                            {{ __('Plans ') }}<i class="fas fa-external-link-alt" style="font-size: 0.8rem;"></i>
                        </a>
                    </h2>
                    <h3 class="h5 mb-0">{{ __('Total: ') }}{{ $user->total_plan }}</h3>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pie Charts Section -->
<div class="row mt-4">
    <!-- Users Distribution Chart -->
    <div class="col-xxl-4 col-md-4 mb-4">
        <div class="card h-100" style="border-left: 4px solid #2e7d32; box-shadow: 0 4px 20px rgba(46, 125, 50, 0.1);">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold"><i class="fas fa-users me-2"></i>{{ __('Users Distribution') }}</h6>
                <div class="d-flex align-items-center gap-2">
                    <span class="badge rounded-pill" style="background-color: rgba(46, 125, 50, 0.1); color: #2e7d32; font-weight: 600;">
                        {{ $user->total_user }} {{ __('Total') }}
                    </span>
                    <button class="btn btn-link p-0 chart-settings-btn" data-bs-toggle="modal" data-bs-target="#chartSettingsModal" data-chart="usersPieChart" title="{{ __('Chart Settings') }}">
                        <i class="fas fa-cog fa-lg" style="color: #2e7d32;"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container" style="min-height: 300px;">
                    <canvas id="usersPieChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <!-- Orders Status Chart -->
    <div class="col-xxl-4 col-md-4 mb-4">
        <div class="card h-100" style="border-left: 4px solid #2e7d32; box-shadow: 0 4px 20px rgba(46, 125, 50, 0.1);">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold"><i class="fas fa-shopping-cart me-2"></i>{{ __('Orders Status') }}</h6>
                <div class="d-flex align-items-center gap-2">
                    <span class="badge rounded-pill" style="background-color: rgba(46, 125, 50, 0.1); color: #2e7d32; font-weight: 600;">
                        {{ $user->total_orders }} {{ __('Total') }}
                    </span>
                    <button class="btn btn-link p-0 chart-settings-btn" data-bs-toggle="modal" data-bs-target="#chartSettingsModal" data-chart="ordersPieChart" title="{{ __('Chart Settings') }}">
                        <i class="fas fa-cog fa-lg" style="color: #2e7d32;"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container" style="min-height: 300px;">
                    <canvas id="ordersPieChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <!-- Plans Distribution Chart -->
    <div class="col-xxl-4 col-md-4 mb-4">
        <div class="card h-100" style="border-left: 4px solid #2e7d32; box-shadow: 0 4px 20px rgba(46, 125, 50, 0.1);">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold"><i class="fas fa-crown me-2"></i>{{ __('Plans Distribution') }}</h6>
                <div class="d-flex align-items-center gap-2">
                    <span class="badge rounded-pill" style="background-color: rgba(46, 125, 50, 0.1); color: #2e7d32; font-weight: 600;">
                        {{ $user->total_plan }} {{ __('Plans') }}
                    </span>
                    <button class="btn btn-link p-0 chart-settings-btn" data-bs-toggle="modal" data-bs-target="#chartSettingsModal" data-chart="plansPieChart" title="{{ __('Chart Settings') }}">
                        <i class="fas fa-cog fa-lg" style="color: #2e7d32;"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container" style="min-height: 300px;">
                    <canvas id="plansPieChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Chart Settings Modal (Generic, place after all chart cards) -->
<div class="modal fade" id="chartSettingsModal" tabindex="-1" aria-labelledby="chartSettingsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="chartSettingsModalLabel">{{ __('Chart Settings') }}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-md-4 mb-3">
            <label class="form-label">{{ __('Chart Type') }}</label>
            <div class="d-flex flex-column gap-2">
              <button type="button" class="btn btn-outline-primary chart-type-option" data-type="pie">Pie</button>
              <button type="button" class="btn btn-outline-primary chart-type-option" data-type="bar">Bar</button>
              <button type="button" class="btn btn-outline-primary chart-type-option" data-type="line">Line</button>
              <button type="button" class="btn btn-outline-primary chart-type-option" data-type="doughnut">Donut</button>
              <button type="button" class="btn btn-outline-primary chart-type-option" data-type="horizontalBar">Horizontal Bar</button>
            </div>
            <!-- Color Palette Section -->
            <div class="mt-4">
              <label class="form-label">{{ __('Chart Color') }}</label>
              <div class="d-flex flex-wrap gap-2 color-palette mb-2">
                <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#0CAF60" style="width:32px;height:32px;background:#0CAF60;"></button>
                <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#2e7d32" style="width:32px;height:32px;background:#2e7d32;"></button>
                <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#ffc107" style="width:32px;height:32px;background:#ffc107;"></button>
                <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#f44336" style="width:32px;height:32px;background:#f44336;"></button>
                <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#584ED2" style="width:32px;height:32px;background:#584ED2;"></button>
                <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#0C7785" style="width:32px;height:32px;background:#0C7785;"></button>
                <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#48494B" style="width:32px;height:32px;background:#48494B;"></button>
                <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#6fd944" style="width:32px;height:32px;background:#6fd944;"></button>
                <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#1976d2" style="width:32px;height:32px;background:#1976d2;"></button>
                <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#ff9800" style="width:32px;height:32px;background:#ff9800;"></button>
                <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#B9406B" style="width:32px;height:32px;background:#B9406B;"></button>
                <button type="button" class="color-swatch border-0 p-0 rounded-circle" data-color="#922C88" style="width:32px;height:32px;background:#922C88;"></button>
              </div>
              <button type="button" class="btn btn-outline-secondary btn-sm w-100 reset-color-btn mt-1">{{ __('Reset Color') }}</button>
            </div>
          </div>
          <div class="col-md-8">
            <label class="form-label">{{ __('Live Preview') }}</label>
            <div class="border rounded p-2 bg-light" style="min-height: 320px;">
              <canvas id="chartPreviewCanvas" style="width:100%;height:300px;"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
        <button type="button" class="btn btn-success" id="saveChartTypeBtn">{{ __('Save Changes') }}</button>
      </div>
    </div>
  </div>
</div>

    <div class="row">
        <div class="col-xxl-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-box" style="color: #0caf60;"></i>{{ __(' Recent Order') }}</h5>
                </div>
                <div class="card-body">
                    <div class="chart">
                        <div id="chart-sales" data-color="primary" data-height="280"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
