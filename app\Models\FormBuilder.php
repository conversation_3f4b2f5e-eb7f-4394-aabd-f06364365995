<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class FormBuilder extends Model
{
    protected $fillable = [
        'form_id',
        'name',
        'type',
        'form_styles',
        'hide_title',
        'created_by',
    ];

    public static $fieldTypes = [
        'text' => 'Text',
        'email' => 'Email',
        'number' => 'Number',
        'date' => 'Date',
        'textarea' => 'Textarea',
        'select' => 'Dropdown menu',
        'radio' => 'Radio button group',
        'checkbox' => 'Single or multiple checkbox options',
        'multiselect' => 'Dropdown with multiple selection',
        'file' => 'Single file upload',
        'file_multiple' => 'Multiple file upload',
    ];

    public function form_field()
    {
        return $this->hasMany('App\Models\FormField', 'form_id', 'id')->ordered();
    }

    public function fieldResponse()
    {
        return $this->hasOne('App\Models\FormFieldResponse', 'form_id', 'id');
    }

    public function response()
    {
        return $this->hasMany('App\Models\FormResponse', 'form_id', 'id');
    }
}
