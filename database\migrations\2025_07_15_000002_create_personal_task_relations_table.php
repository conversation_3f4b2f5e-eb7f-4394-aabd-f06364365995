<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePersonalTaskRelationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Create personal task comments table
        Schema::create('personal_task_comments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('task_id');
            $table->unsignedBigInteger('user_id');
            $table->text('comment');
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->json('comment_reaction')->nullable();
            $table->timestamps();
            
            $table->foreign('task_id')->references('id')->on('personal_tasks')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('parent_id')->references('id')->on('personal_task_comments')->onDelete('cascade');
        });

        // Create personal task files table
        Schema::create('personal_task_files', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('task_id');
            $table->string('file');
            $table->string('name');
            $table->string('extension');
            $table->string('file_size');
            $table->integer('created_by');
            $table->timestamps();
            
            $table->foreign('task_id')->references('id')->on('personal_tasks')->onDelete('cascade');
        });

        // Create personal task checklists table
        Schema::create('personal_task_checklists', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->unsignedBigInteger('task_id');
            $table->string('user_type');
            $table->integer('created_by');
            $table->integer('status')->default(0);
            $table->timestamps();
            
            $table->foreign('task_id')->references('id')->on('personal_tasks')->onDelete('cascade');
        });

        // Create personal task time tracking table
        Schema::create('personal_task_time_tracking', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('task_id');
            $table->unsignedBigInteger('user_id');
            $table->dateTime('start_time');
            $table->dateTime('end_time')->nullable();
            $table->boolean('is_running')->default(false);
            $table->integer('total_time')->default(0); // Total time in seconds
            $table->text('notes')->nullable();
            $table->timestamps();
            
            $table->foreign('task_id')->references('id')->on('personal_tasks')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            
            // Index for finding running timers
            $table->index(['user_id', 'is_running']);
            $table->index(['task_id', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('personal_task_time_tracking');
        Schema::dropIfExists('personal_task_checklists');
        Schema::dropIfExists('personal_task_files');
        Schema::dropIfExists('personal_task_comments');
    }
}
