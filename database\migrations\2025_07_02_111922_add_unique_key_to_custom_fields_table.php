<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;
use App\Models\CustomField;

return new class extends Migration
{
    public function up(): void
    {
        // Add the new nullable column
        Schema::table('custom_fields', function (Blueprint $table) {
            $table->string('unique_key')->nullable()->after('name');
        });

        // Fill with unique values for existing rows
        CustomField::whereNull('unique_key')->orWhere('unique_key', '')->get()->each(function ($field) {
            $field->unique_key = Str::slug($field->name . '-' . $field->id);
            $field->save();
        });

        // Add unique index
        Schema::table('custom_fields', function (Blueprint $table) {
            $table->unique('unique_key', 'custom_fields_unique_key_unique');
        });
    }

    public function down(): void
    {
        Schema::table('custom_fields', function (Blueprint $table) {
            $table->dropUnique('custom_fields_unique_key_unique');
            $table->dropColumn('unique_key');
        });
    }
};
