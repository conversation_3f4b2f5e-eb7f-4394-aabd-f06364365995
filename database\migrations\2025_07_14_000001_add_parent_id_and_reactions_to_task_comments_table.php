<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddParentIdAndReactionsToTaskCommentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('task_comments', function (Blueprint $table) {
            if (!Schema::hasColumn('task_comments', 'parent_id')) {
                $table->unsignedBigInteger('parent_id')->nullable()->after('task_id');
                $table->foreign('parent_id')->references('id')->on('task_comments')->onDelete('cascade');
            }

            if (!Schema::hasColumn('task_comments', 'comment_reaction')) {
                $table->json('comment_reaction')->nullable()->after('comment');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('task_comments', function (Blueprint $table) {
            $table->dropForeign(['parent_id']);
            $table->dropColumn(['parent_id', 'comment_reaction']);
        });
    }
}
