<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class TaskTimeTracking extends Model
{
    protected $table = 'task_time_tracking';
    
    protected $fillable = [
        'task_id',
        'project_id', 
        'user_id',
        'start_time',
        'end_time',
        'elapsed_seconds',
        'is_running',
        'description'
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'is_running' => 'boolean'
    ];

    // Relationships
    public function task()
    {
        return $this->belongsTo(ProjectTask::class, 'task_id');
    }

    public function project()
    {
        return $this->belongsTo(Project::class, 'project_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    // Helper methods
    public function getCurrentElapsedSeconds()
    {
        // Ensure elapsed_seconds is never negative
        $baseElapsed = max(0, (int)$this->elapsed_seconds);

        if (!$this->is_running || !$this->start_time) {
            return $baseElapsed;
        }

        try {
            // Use UTC to avoid timezone issues
            $startTime = Carbon::parse($this->start_time)->utc();
            $now = Carbon::now()->utc();

            // Make sure start time is not in the future
            if ($startTime->isFuture()) {
                Log::warning('Start time is in the future', [
                    'start_time' => $startTime->toISOString(),
                    'now' => $now->toISOString(),
                    'entry_id' => $this->id
                ]);
                return $baseElapsed;
            }

            $currentElapsed = $now->diffInSeconds($startTime);
            $manualCurrentElapsed = $now->timestamp - $startTime->timestamp;

            // Use manual calculation if Carbon gives negative or unreasonable result
            if ($currentElapsed <= 0 && $manualCurrentElapsed > 0) {
                Log::warning('Using manual calculation for current elapsed time', [
                    'entry_id' => $this->id,
                    'carbon_result' => $currentElapsed,
                    'manual_result' => $manualCurrentElapsed
                ]);
                $currentElapsed = $manualCurrentElapsed;
            }

            $total = $baseElapsed + $currentElapsed;

            Log::info('Current elapsed calculation', [
                'entry_id' => $this->id,
                'base_elapsed' => $baseElapsed,
                'start_time' => $startTime->toISOString(),
                'now' => $now->toISOString(),
                'current_elapsed_carbon' => $now->diffInSeconds($startTime),
                'current_elapsed_manual' => $manualCurrentElapsed,
                'current_elapsed_used' => $currentElapsed,
                'total' => $total
            ]);

            return max(0, $total); // Ensure result is never negative
        } catch (\Exception $e) {
            Log::error('Error calculating current elapsed seconds', [
                'error' => $e->getMessage(),
                'entry_id' => $this->id,
                'start_time' => $this->start_time,
                'base_elapsed' => $baseElapsed
            ]);
            return $baseElapsed;
        }
    }

    public function getFormattedElapsedTime()
    {
        $totalSeconds = $this->getCurrentElapsedSeconds();
        $hours = floor($totalSeconds / 3600);
        $minutes = floor(($totalSeconds % 3600) / 60);
        $seconds = $totalSeconds % 60;

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }

    public static function getRunningTimerForUser($userId)
    {
        return self::where('user_id', $userId)
                   ->where('is_running', true)
                   ->first();
    }

    public static function getTotalTimeForTask($taskId, $userId = null)
    {
        $query = self::where('task_id', $taskId);
        
        if ($userId) {
            $query->where('user_id', $userId);
        }
        
        $entries = $query->get();
        $totalSeconds = 0;
        
        foreach ($entries as $entry) {
            $entrySeconds = $entry->getCurrentElapsedSeconds();
            $totalSeconds += $entrySeconds;
        }

        return max(0, $totalSeconds); // Ensure non-negative
    }

    public function stopTimer()
    {
        if ($this->is_running && $this->start_time) {
            try {
                // Use UTC to avoid timezone issues
                $startTime = Carbon::parse($this->start_time)->utc();
                $endTime = Carbon::now()->utc();

                Log::info('Stopping timer - before calculation', [
                    'entry_id' => $this->id,
                    'start_time' => $startTime->toISOString(),
                    'end_time' => $endTime->toISOString(),
                    'start_timestamp' => $startTime->timestamp,
                    'end_timestamp' => $endTime->timestamp,
                    'current_elapsed_seconds' => $this->elapsed_seconds
                ]);

                // Ensure start time is not in the future
                if ($startTime->isFuture()) {
                    Log::warning('Stopping timer with future start time', [
                        'entry_id' => $this->id,
                        'start_time' => $startTime->toISOString(),
                        'end_time' => $endTime->toISOString()
                    ]);
                    $sessionTime = 0;
                } else {
                    // Calculate session time in seconds using multiple methods
                    $sessionTime = $endTime->diffInSeconds($startTime);

                    // Manual calculation as backup
                    $manualSessionTime = $endTime->timestamp - $startTime->timestamp;

                    Log::info('Session time calculated', [
                        'entry_id' => $this->id,
                        'session_time_carbon' => $sessionTime,
                        'session_time_manual' => $manualSessionTime,
                        'start_time' => $startTime->toISOString(),
                        'end_time' => $endTime->toISOString(),
                        'start_timestamp' => $startTime->timestamp,
                        'end_timestamp' => $endTime->timestamp
                    ]);

                    // Use manual calculation if Carbon gives 0 or negative but manual gives a positive result
                    if (($sessionTime <= 0 || $sessionTime < $manualSessionTime) && $manualSessionTime > 0) {
                        Log::warning('Using manual time calculation as Carbon returned invalid result', [
                            'entry_id' => $this->id,
                            'carbon_result' => $sessionTime,
                            'manual_result' => $manualSessionTime,
                            'reason' => $sessionTime <= 0 ? 'negative_or_zero' : 'less_than_manual'
                        ]);
                        $sessionTime = $manualSessionTime;
                    }
                }

                // Ensure session time is not negative and convert to integer
                $sessionTime = max(0, (int)$sessionTime);

                // Update the record
                $oldElapsed = $this->elapsed_seconds;
                $this->end_time = $endTime;
                $this->elapsed_seconds = max(0, $oldElapsed + $sessionTime);
                $this->is_running = false;

                Log::info('Updating timer record', [
                    'entry_id' => $this->id,
                    'old_elapsed' => $oldElapsed,
                    'session_time' => $sessionTime,
                    'new_elapsed' => $this->elapsed_seconds,
                    'is_running' => $this->is_running
                ]);

                $saveResult = $this->save();

                // Verify the save worked by checking the database
                $freshRecord = self::find($this->id);

                Log::info('Timer stopped successfully', [
                    'entry_id' => $this->id,
                    'start_time' => $startTime->toISOString(),
                    'end_time' => $endTime->toISOString(),
                    'session_time' => $sessionTime,
                    'total_elapsed_before_save' => $this->elapsed_seconds,
                    'save_result' => $saveResult,
                    'fresh_record_elapsed' => $freshRecord ? $freshRecord->elapsed_seconds : 'NOT_FOUND',
                    'fresh_record_is_running' => $freshRecord ? $freshRecord->is_running : 'NOT_FOUND',
                    'record_saved' => true
                ]);

                return $sessionTime;
            } catch (\Exception $e) {
                Log::error('Error stopping timer', [
                    'error' => $e->getMessage(),
                    'entry_id' => $this->id,
                    'start_time' => $this->start_time,
                    'trace' => $e->getTraceAsString()
                ]);

                // Fallback: just mark as stopped
                $this->is_running = false;
                $this->save();
                return 0;
            }
        }

        Log::warning('Timer stop called but conditions not met', [
            'entry_id' => $this->id,
            'is_running' => $this->is_running,
            'has_start_time' => !is_null($this->start_time)
        ]);

        return 0;
    }
}
