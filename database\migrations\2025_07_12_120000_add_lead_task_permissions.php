<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class AddLeadTaskPermissions extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Create lead task permissions if they don't exist
        $leadTaskPermissions = [
            [
                'name' => 'manage lead task',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'create lead task',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'edit lead task',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'view lead task',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'delete lead task',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        // Insert permissions only if they don't exist
        foreach ($leadTaskPermissions as $permissionData) {
            $existingPermission = Permission::where('name', $permissionData['name'])->first();
            if (!$existingPermission) {
                Permission::create($permissionData);
            }
        }

        // Clear permission cache
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Assign lead task permissions to appropriate roles
        $rolePermissions = [
            'super admin' => [
                'manage lead task',
                'create lead task',
                'edit lead task',
                'view lead task',
                'delete lead task',
            ],
            'system admin' => [
                'manage lead task',
                'create lead task',
                'edit lead task',
                'view lead task',
                'delete lead task',
            ],
            'company' => [
                'manage lead task',
                'create lead task',
                'edit lead task',
                'view lead task',
                'delete lead task',
            ],
            'accountant' => [
                'view lead task',
                'create lead task',
                'edit lead task',
            ],
            'client' => [
                'view lead task',
            ],
        ];

        foreach ($rolePermissions as $roleName => $permissions) {
            $role = Role::where('name', $roleName)->first();
            if ($role) {
                foreach ($permissions as $permissionName) {
                    $permission = Permission::where('name', $permissionName)->first();
                    if ($permission && !$role->hasPermissionTo($permission)) {
                        $role->givePermissionTo($permission);
                    }
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Remove lead task permissions from roles
        $leadTaskPermissions = [
            'manage lead task',
            'create lead task',
            'edit lead task',
            'view lead task',
            'delete lead task',
        ];

        foreach ($leadTaskPermissions as $permissionName) {
            $permission = Permission::where('name', $permissionName)->first();
            if ($permission) {
                // Remove permission from all roles
                $roles = Role::all();
                foreach ($roles as $role) {
                    if ($role->hasPermissionTo($permission)) {
                        $role->revokePermissionTo($permission);
                    }
                }
                
                // Delete the permission
                $permission->delete();
            }
        }

        // Clear permission cache
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
    }
}
