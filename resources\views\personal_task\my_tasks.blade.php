@extends('layouts.admin')

@section('page-title')
    {{ __('My Tasks') }}
@endsection

@section('action-button')
    @can('create personal task')
        <a href="#" data-size="lg" data-url="{{ route('personal-tasks.create') }}" data-ajax-popup="true"
           data-bs-toggle="tooltip" title="{{ __('Create Personal Task') }}"
           class="btn btn-sm btn-primary modern-create-btn">
            <i class="ti ti-plus"></i> {{ __('Create Personal Task') }}
        </a>
    @endcan
@endsection

@push('css-page')
<style>
.modern-create-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;
    border-radius: 0.75rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    transition: all 0.2s ease;
}

.modern-create-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
}

.modern-task-card {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 1rem;
    padding: 0;
    margin-bottom: 1.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    overflow: hidden;
}

.modern-task-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(0, 0, 0, 0.1);
    border-color: #cbd5e1;
}

.modern-card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;
    padding: 1.5rem;
}

.modern-card-body {
    padding: 1.5rem;
}

.modern-section-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.modern-section-subtitle {
    font-size: 0.875rem;
    color: #64748b;
    margin-bottom: 0;
}

.modern-filter-tabs {
    background: #f8fafc;
    border-radius: 0.75rem;
    padding: 0.25rem;
    display: inline-flex;
    gap: 0.25rem;
}

.modern-filter-tab {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: #64748b;
    background: transparent;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.modern-filter-tab.active {
    background: #ffffff;
    color: #1e293b;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.modern-filter-tab:hover:not(.active) {
    color: #475569;
    background: rgba(255, 255, 255, 0.5);
}

.modern-task-table {
    background: #ffffff;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
    margin-bottom: 1.5rem;
}

.modern-task-table table {
    margin-bottom: 0;
    table-layout: fixed;
    width: 100%;
}

.modern-task-table thead th {
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    color: #64748b;
    font-weight: 500;
    font-size: 0.75rem;
    padding: 0.75rem 1rem;
    border-top: none;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    white-space: nowrap;
}

.modern-task-table thead th:first-child {
    width: 28%;
    padding-left: 1.5rem;
}

.modern-task-table thead th:nth-child(2) {
    width: 12%;
}

.modern-task-table thead th:nth-child(3) {
    width: 12%;
}

.modern-task-table thead th:nth-child(4) {
    width: 14%;
}

.modern-task-table thead th:nth-child(5) {
    width: 14%;
}

.modern-task-table thead th:nth-child(6) {
    width: 12%;
}

.modern-task-table thead th:last-child {
    width: 8%;
    text-align: center;
    padding-right: 1.5rem;
}

/* Project tasks table - 7 columns */
#project-tasks-section .modern-task-table thead th:first-child {
    width: 24%;
}

#project-tasks-section .modern-task-table thead th:nth-child(2) {
    width: 14%;
}

#project-tasks-section .modern-task-table thead th:nth-child(3) {
    width: 11%;
}

#project-tasks-section .modern-task-table thead th:nth-child(4) {
    width: 11%;
}

#project-tasks-section .modern-task-table thead th:nth-child(5) {
    width: 13%;
}

#project-tasks-section .modern-task-table thead th:nth-child(6) {
    width: 12%;
}

#project-tasks-section .modern-task-table thead th:last-child {
    width: 15%;
}

.modern-task-table tbody td {
    padding: 1rem;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: middle;
    font-size: 0.875rem;
}

.modern-task-table tbody td:first-child {
    padding-left: 1.5rem;
}

.modern-task-table tbody td:last-child {
    text-align: center;
    padding-right: 1.5rem;
}

.modern-task-table tbody tr:last-child td {
    border-bottom: none;
}

.modern-task-table tbody tr:hover {
    background: #f8fafc;
    transition: all 0.15s ease;
}

.task-priority-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.35rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.7rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.03em;
    min-width: 70px;
    justify-content: center;
}

.priority-critical {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fee2e2;
}

.priority-high {
    background: #fffbeb;
    color: #f59e0b;
    border: 1px solid #fef3c7;
}

.priority-medium {
    background: #eff6ff;
    color: #3b82f6;
    border: 1px solid #dbeafe;
}

.priority-low {
    background: #f0fdf4;
    color: #10b981;
    border: 1px solid #dcfce7;
}

.task-status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.35rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.7rem;
    font-weight: 500;
    min-width: 75px;
    justify-content: center;
}

.status-todo {
    background: #f1f5f9;
    color: #475569;
    border: 1px solid #e2e8f0;
}

.status-inprogress {
    background: #fef3c7;
    color: #d97706;
    border: 1px solid #fed7aa;
}

.status-review {
    background: #e0e7ff;
    color: #6366f1;
    border: 1px solid #c7d2fe;
}

.status-done {
    background: #dcfce7;
    color: #16a34a;
    border: 1px solid #bbf7d0;
}

.task-name-cell {
    padding-right: 1rem;
}

.task-name-title {
    font-size: 0.8125rem;
    font-weight: 500;
    color: #1e293b;
    margin-bottom: 0.125rem;
    line-height: 1.3;
}

.task-name-description {
    font-size: 0.7rem;
    color: #64748b;
    line-height: 1.2;
    margin-bottom: 0;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.task-progress-cell {
    padding-right: 0.75rem;
}

.task-progress-bar {
    margin-bottom: 0.25rem;
}

.task-progress-text {
    font-size: 0.7rem;
    color: #64748b;
    font-weight: 400;
}

.task-assignees-cell {
    padding-right: 0.75rem;
}

.task-date-cell {
    font-size: 0.75rem;
    font-weight: 400;
    color: #475569;
}

.task-actions-cell {
    white-space: nowrap;
}

.modern-empty-state {
    text-align: center;
    padding: 3rem 1.5rem;
    background: #ffffff;
    border-radius: 1rem;
    border: 2px dashed #e2e8f0;
}

.modern-empty-icon {
    font-size: 3rem;
    color: #cbd5e1;
    margin-bottom: 1rem;
}

.modern-empty-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #475569;
    margin-bottom: 0.5rem;
}

.modern-empty-subtitle {
    font-size: 0.875rem;
    color: #64748b;
    margin-bottom: 1.5rem;
}

.modern-action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.375rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 400;
    text-decoration: none;
    transition: all 0.15s ease;
    border: 1px solid transparent;
    width: 28px;
    height: 28px;
}

.modern-action-btn-primary {
    background: #eff6ff;
    color: #3b82f6;
    border-color: #dbeafe;
}

.modern-action-btn-primary:hover {
    background: #dbeafe;
    color: #1d4ed8;
    transform: translateY(-1px);
    box-shadow: 0 1px 3px rgba(59, 130, 246, 0.2);
}

.modern-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Avatar hover effects */
.avatar-group .avatar:hover {
    transform: scale(1.05);
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15) !important;
}

/* Section spacing */
.task-section {
    margin-bottom: 2rem;
}

.modern-section-title {
    margin-bottom: 1rem;
}

/* Compact avatar styling */
.compact-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    overflow: hidden;
    border: 1.5px solid #ffffff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transition: all 0.15s ease;
}

.compact-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.compact-progress-bar {
    height: 6px;
    border-radius: 3px;
    background-color: #f1f5f9;
}

.compact-progress-bar .progress-bar {
    border-radius: 3px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

/* Text Avatar Styling */
.compact-avatar-text {
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
}

.compact-avatar-text:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

/* Section Actions Styling */
.section-actions .modern-btn {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.modern-btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: #ffffff;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.modern-btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    color: #ffffff;
    text-decoration: none;
}

/* Avatar Group Improvements */
.avatar-group {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    align-items: center;
}

.avatar-group .compact-avatar-text:not(:first-child) {
    margin-left: -0.125rem;
}

/* Responsive spacing adjustments */
@media (max-width: 768px) {
    .modern-task-table thead th,
    .modern-task-table tbody td {
        padding: 0.75rem 0.5rem;
    }

    .modern-task-table thead th:first-child,
    .modern-task-table tbody td:first-child {
        padding-left: 0.75rem;
    }

    .modern-task-table tbody td:last-child {
        padding-right: 0.75rem;
    }

    .task-priority-badge,
    .task-status-badge {
        min-width: auto;
        padding: 0.25rem 0.5rem;
        font-size: 0.65rem;
    }

    .compact-avatar {
        width: 24px;
        height: 24px;
    }

    .modern-action-btn {
        width: 24px;
        height: 24px;
        padding: 0.25rem;
    }
}

.task-count-badge {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: #ffffff;
    font-size: 0.75rem;
    font-weight: 700;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    margin-left: 0.5rem;
}

.project-badge {
    background: #f0f9ff;
    color: #0ea5e9;
    border: 1px solid #e0f2fe;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.35rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.7rem;
    font-weight: 500;
    min-width: 75px;
    justify-content: center;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
@endpush

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="modern-task-card">
                <div class="modern-card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="modern-section-title">
                                <i class="ti ti-dashboard"></i>
                                {{ __('My Tasks Overview') }}
                            </div>
                            <div class="modern-section-subtitle">
                                {{ __('Manage your personal and project tasks in one place') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="modern-filter-tabs">
                                <button type="button" class="modern-filter-tab active" id="show-all-tasks">
                                    {{ __('All Tasks') }}
                                </button>
                                <button type="button" class="modern-filter-tab" id="show-personal-tasks">
                                    {{ __('Personal') }}
                                </button>
                                <button type="button" class="modern-filter-tab" id="show-project-tasks">
                                    {{ __('Projects') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modern-card-body">
                    <!-- Personal Tasks Section -->
                    <div id="personal-tasks-section" class="task-section ps-4 pe-4 pb-4">
                        <div class="d-flex justify-content-between align-items-center mb-2 p-4">
                            <div class="modern-section-title">
                                <i class="ti ti-user-check"></i>
                                {{ __('Personal Tasks') }}
                                <span class="task-count-badge bg-primary bg-gradient">{{ count($personalTasks) }}</span>
                            </div>
                            <div class="section-actions">
                                @can('create personal task')
                                    <a href="#" class="modern-btn modern-btn-primary"
                                       data-url="{{ route('personal-tasks.create') }}"
                                       data-ajax-popup="true"
                                       data-size="lg"
                                       data-title="{{ __('Create Personal Task') }}"
                                       style="padding: 0.5rem 1rem; border-radius: 0.5rem; font-weight: 600; font-size: 0.875rem; transition: all 0.2s ease; border: none; cursor: pointer; display: inline-flex; align-items: center; gap: 0.5rem; text-decoration: none; background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: #ffffff; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);">
                                        <i class="ti ti-plus"></i>
                                        {{ __('Add Personal Task') }}
                                    </a>
                                @endcan
                            </div>
                        </div>

                        @if(count($personalTasks) > 0)
                            <div class="modern-task-table ">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>{{ __('Task') }}</th>
                                            <th>{{ __('Priority') }}</th>
                                            <th>{{ __('Stage') }}</th>
                                            <th>{{ __('Status') }}</th>
                                            <th>{{ __('Assigned To') }}</th>
                                            <th>{{ __('Due Date') }}</th>
                                            <th>{{ __('Actions') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($personalTasks as $task)
                                            <tr>
                                                <td class="task-name-cell">
                                                    <div class="d-flex align-items-center">
                                                        @if($task->is_favourite)
                                                            <i class="ti ti-star-filled text-warning me-3" style="font-size: 1.25rem;"></i>
                                                        @endif
                                                        <div>
                                                            <h6 class="task-name-title">{{ $task->name }}</h6>
                                                            @if($task->description)
                                                                <p class="task-name-description">{{ Str::limit($task->description, 60) }}</p>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="task-priority-badge priority-{{ $task->priority }}">
                                                        <i class="ti ti-flag-filled"></i>
                                                        {{ ucfirst($task->priority) }}
                                                    </span>
                                                </td>
                                                <td>
                                                    @if($task->stage)
                                                        <span class="task-status-badge status-{{ strtolower(str_replace(' ', '', $task->stage->name)) }}">
                                                            <i class="ti ti-{{
                                                                strtolower(str_replace(' ', '', $task->stage->name)) == 'todo' ? 'list' :
                                                                (strtolower(str_replace(' ', '', $task->stage->name)) == 'inprogress' ? 'loader' :
                                                                (strtolower(str_replace(' ', '', $task->stage->name)) == 'review' ? 'eye' : 'check'))
                                                            }}"></i>
                                                            {{ $task->stage->name }}
                                                        </span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if($task->status == 'completed')
                                                        <span class="task-status-badge status-done">
                                                            <i class="ti ti-check"></i> {{ __('Completed') }}
                                                        </span>
                                                    @elseif($task->status == 'on_hold')
                                                        <span class="task-status-badge status-review">
                                                            <i class="ti ti-pause"></i> {{ __('On Hold') }}
                                                        </span>
                                                    @else
                                                        <span class="task-status-badge status-inprogress">
                                                            <i class="ti ti-loader"></i> {{ __('In Progress') }}
                                                        </span>
                                                    @endif
                                                </td>
                                                <td class="task-assignees-cell">
                                                    @if($task->users()->count() > 0)
                                                        <div class="avatar-group" style="display: flex; flex-wrap: wrap; gap: 0.25rem;">
                                                            @foreach($task->users()->take(3) as $index => $user)
                                                                @php
                                                                    $colors = [
                                                                        'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)', // Blue
                                                                        'linear-gradient(135deg, #10b981 0%, #059669 100%)', // Green
                                                                        'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)', // Orange
                                                                        'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)', // Red
                                                                        'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)', // Purple
                                                                        'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)', // Cyan
                                                                    ];
                                                                    $colorIndex = $index % count($colors);
                                                                @endphp
                                                                <div class="compact-avatar-text" data-bs-toggle="tooltip" title="{{ $user->name }}"
                                                                     style="width: 28px; height: 28px; border-radius: 50%; background: {{ $colors[$colorIndex] }}; color: white; display: flex; align-items: center; justify-content: center; font-size: 0.65rem; font-weight: 600; border: 2px solid #ffffff; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                                                                    {{ strtoupper(substr($user->name, 0, 1)) }}
                                                                </div>
                                                            @endforeach
                                                            @if($task->users()->count() > 3)
                                                                <div class="compact-avatar-text"
                                                                    style="width: 28px; height: 28px; border-radius: 50%; background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%); color: white; display: flex; align-items: center; justify-content: center; font-size: 0.65rem; font-weight: 600; border: 2px solid #ffffff; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);"
                                                                    data-bs-toggle="tooltip" title="{{ $task->users()->count() - 3 }} {{ __('more users') }}">
                                                                    +{{ $task->users()->count() - 3 }}
                                                                </div>
                                                            @endif
                                                        </div>
                                                    @else
                                                        <div class="text-muted" style="font-size: 0.75rem; font-style: italic;">
                                                            <i class="ti ti-user-off me-1"></i>{{ __('Unassigned') }}
                                                        </div>
                                                    @endif
                                                </td>
                                                <td class="task-date-cell">
                                                    @if($task->end_date)
                                                        <div class="d-flex align-items-center gap-1">
                                                            <i class="ti ti-calendar-due text-muted" style="font-size: 0.75rem;"></i>
                                                            <span class="text-{{ \Carbon\Carbon::parse($task->end_date)->isPast() ? 'danger' : 'dark' }}">
                                                                {{ \Carbon\Carbon::parse($task->end_date)->format('M d, Y') }}
                                                            </span>
                                                        </div>
                                                        @if(\Carbon\Carbon::parse($task->end_date)->isPast())
                                                            <small class="text-danger" style="font-size: 0.65rem;">
                                                                <i class="ti ti-alert-triangle"></i> {{ __('Overdue') }}
                                                            </small>
                                                        @endif
                                                    @else
                                                        <div class="text-muted" style="font-size: 0.7rem; font-style: italic;">
                                                            <i class="ti ti-calendar-off me-1"></i>{{ __('No date') }}
                                                        </div>
                                                    @endif
                                                </td>
                                                <td class="task-actions-cell">
                                                    <div class="d-flex gap-1 justify-content-center">
                                                        @can('view personal task')
                                                            <a href="#" data-url="{{ route('personal-tasks.show', $task->id) }}"
                                                               data-ajax-popup="true" data-size="lg"
                                                               class="modern-action-btn modern-action-btn-primary"
                                                               data-bs-toggle="tooltip" title="{{ __('View') }}">
                                                                <i class="ti ti-eye"></i>
                                                            </a>
                                                        @endcan
                                                        @can('edit personal task')
                                                            <a href="#" data-url="{{ route('personal-tasks.edit', $task->id) }}"
                                                               data-ajax-popup="true" data-size="lg"
                                                               class="modern-action-btn"
                                                               style="background: #f0f9ff; color: #0ea5e9; border-color: #e0f2fe;"
                                                               data-bs-toggle="tooltip" title="{{ __('Edit') }}">
                                                                <i class="ti ti-edit"></i>
                                                            </a>
                                                        @endcan
                                                        @can('delete personal task')
                                                            @if($task->created_by == Auth::user()->id)
                                                                <form method="POST" action="{{ route('personal-tasks.destroy', $task->id) }}"
                                                                      style="display: inline-block;">
                                                                    @csrf
                                                                    @method('DELETE')
                                                                    <button type="submit" class="modern-action-btn"
                                                                            style="background: #fef2f2; color: #dc2626; border-color: #fee2e2;"
                                                                            data-bs-toggle="tooltip" title="{{ __('Delete') }}">
                                                                        <i class="ti ti-trash"></i>
                                                                    </button>
                                                                </form>
                                                            @endif
                                                        @endcan
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="modern-empty-state">
                                <div class="modern-empty-icon">
                                    <i class="ti ti-clipboard-list"></i>
                                </div>
                                <div class="modern-empty-title">{{ __('No personal tasks found') }}</div>
                                <div class="modern-empty-subtitle">{{ __('Create your first personal task to get started and stay organized') }}</div>
                                @can('create personal task')
                                    <a href="#" data-size="lg" data-url="{{ route('personal-tasks.create') }}"
                                       data-ajax-popup="true" class="modern-btn modern-btn-primary">
                                        <i class="ti ti-plus"></i> {{ __('Create Personal Task') }}
                                    </a>
                                @endcan
                            </div>
                        @endif
                    </div>

                    <!-- Project Tasks Section -->
                    <div id="project-tasks-section" class="task-section ps-4 pe-4 pb-4">
                        <div class="d-flex justify-content-between align-items-center mb-2 p-4">
                            <div class="modern-section-title">
                                <i class="ti ti-briefcase"></i>
                                {{ __('Project Tasks') }}
                                <span class="task-count-badge">{{ count($projectTasks) }}</span>
                            </div>
                        </div>

                        @if(count($projectTasks) > 0)
                            <div class="modern-task-table">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>{{ __('Task') }}</th>
                                            <th>{{ __('Project') }}</th>
                                            <th>{{ __('Priority') }}</th>
                                            <th>{{ __('Stage') }}</th>
                                            <th>{{ __('Progress') }}</th>
                                            <th>{{ __('Due Date') }}</th>
                                            <th>{{ __('Actions') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($projectTasks as $task)
                                            <tr>
                                                <td class="task-name-cell">
                                                    <div class="d-flex align-items-center">
                                                        @if($task->is_favourite)
                                                            <i class="ti ti-star-filled text-warning me-3" style="font-size: 1.25rem;"></i>
                                                        @endif
                                                        <div>
                                                            <h6 class="task-name-title">{{ $task->name }}</h6>
                                                            @if($task->description)
                                                                <p class="task-name-description">{{ Str::limit($task->description, 60) }}</p>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    @if($task->project)
                                                        <span class="project-badge" data-bs-toggle="tooltip" title="{{ $task->project->project_name }}">
                                                            <i class="ti ti-folder"></i>
                                                            {{ Str::limit($task->project->project_name, 12) }}
                                                        </span>
                                                    @else
                                                        <div class="text-muted" style="font-size: 0.7rem; font-style: italic;">
                                                            <i class="ti ti-folder-off me-1"></i>{{ __('No project') }}
                                                        </div>
                                                    @endif
                                                </td>
                                                <td>
                                                    <span class="task-priority-badge priority-{{ $task->priority }}">
                                                        <i class="ti ti-flag-filled"></i>
                                                        {{ ucfirst($task->priority) }}
                                                    </span>
                                                </td>
                                                <td>
                                                    @if($task->stage)
                                                        <span class="task-status-badge status-{{ strtolower(str_replace(' ', '', $task->stage->name)) }}">
                                                            <i class="ti ti-{{
                                                                strtolower(str_replace(' ', '', $task->stage->name)) == 'todo' ? 'list' :
                                                                (strtolower(str_replace(' ', '', $task->stage->name)) == 'inprogress' ? 'loader' :
                                                                (strtolower(str_replace(' ', '', $task->stage->name)) == 'review' ? 'eye' : 'check'))
                                                            }}"></i>
                                                            {{ $task->stage->name }}
                                                        </span>
                                                    @endif
                                                </td>
                                                <td class="task-progress-cell">
                                                    <div class="task-progress-bar">
                                                        <div class="progress compact-progress-bar">
                                                            <div class="progress-bar" role="progressbar"
                                                                style="width: {{ $task->progress }}%;"
                                                                aria-valuenow="{{ $task->progress }}"
                                                                aria-valuemin="0" aria-valuemax="100">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="task-progress-text">
                                                        <span class="fw-medium">{{ $task->progress }}%</span>
                                                    </div>
                                                </td>
                                                <td class="task-date-cell">
                                                    @if($task->end_date)
                                                        <div class="d-flex align-items-center gap-1">
                                                            <i class="ti ti-calendar-due text-muted" style="font-size: 0.75rem;"></i>
                                                            <span class="text-{{ \Carbon\Carbon::parse($task->end_date)->isPast() ? 'danger' : 'dark' }}">
                                                                {{ \Carbon\Carbon::parse($task->end_date)->format('M d, Y') }}
                                                            </span>
                                                        </div>
                                                        @if(\Carbon\Carbon::parse($task->end_date)->isPast())
                                                            <small class="text-danger" style="font-size: 0.65rem;">
                                                                <i class="ti ti-alert-triangle"></i> {{ __('Overdue') }}
                                                            </small>
                                                        @endif
                                                    @else
                                                        <div class="text-muted" style="font-size: 0.7rem; font-style: italic;">
                                                            <i class="ti ti-calendar-off me-1"></i>{{ __('No date') }}
                                                        </div>
                                                    @endif
                                                </td>
                                                <td class="task-actions-cell">
                                                    <div class="d-flex gap-1 justify-content-center">
                                                        @can('view project task')
                                                            <a href="#" data-url="{{ route('projects.tasks.show', [$task->project_id, $task->id]) }}"
                                                               data-ajax-popup="true" data-size="lg"
                                                               class="modern-action-btn modern-action-btn-primary"
                                                               data-bs-toggle="tooltip" title="{{ __('View') }}">
                                                                <i class="ti ti-eye"></i>
                                                            </a>
                                                        @endcan
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="modern-empty-state">
                                <div class="modern-empty-icon">
                                    <i class="ti ti-briefcase"></i>
                                </div>
                                <div class="modern-empty-title">{{ __('No project tasks assigned') }}</div>
                                <div class="modern-empty-subtitle">{{ __('You have no project tasks assigned to you at the moment') }}</div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<script>
    $(document).ready(function() {
        // Modern task filter buttons with smooth animations
        $('#show-all-tasks').click(function() {
            $('.task-section').fadeIn(300);
            $('.modern-filter-tab').removeClass('active');
            $(this).addClass('active');
        });

        $('#show-personal-tasks').click(function() {
            $('#personal-tasks-section').fadeIn(300);
            $('#project-tasks-section').fadeOut(300);
            $('.modern-filter-tab').removeClass('active');
            $(this).addClass('active');
        });

        $('#show-project-tasks').click(function() {
            $('#personal-tasks-section').fadeOut(300);
            $('#project-tasks-section').fadeIn(300);
            $('.modern-filter-tab').removeClass('active');
            $(this).addClass('active');
        });

        // Enhanced tooltips
        $('[data-bs-toggle="tooltip"]').tooltip({
            trigger: 'hover',
            placement: 'top'
        });

        // Handle delete form submissions for personal tasks (specific selector)
        $(document).on('submit', 'form[action*="personal-tasks"][action*="destroy"]', function(e) {
            e.preventDefault();
            e.stopPropagation(); // Prevent event bubbling

            const form = $(this);
            const deleteUrl = form.attr('action');
            const submitBtn = form.find('button[type="submit"]');
            const originalBtnText = submitBtn.html();

            if (confirm('Are you sure you want to delete this task? This action cannot be undone.')) {
                // Show loading state
                submitBtn.prop('disabled', true).html('<i class="ti ti-loader-2 ti-spin"></i>');

                $.ajax({
                    url: deleteUrl,
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.is_success) {
                            show_toastr('success', response.message || 'Task deleted successfully');

                            // Redirect or reload page
                            if (response.redirect) {
                                setTimeout(function() {
                                    window.location.href = response.redirect;
                                }, 1000);
                            } else {
                                setTimeout(function() {
                                    window.location.reload();
                                }, 1000);
                            }
                        } else {
                            show_toastr('error', response.message || response.error || 'Failed to delete task');
                            submitBtn.prop('disabled', false).html(originalBtnText);
                        }
                    },
                    error: function(xhr) {
                        let errorMessage = 'Failed to delete task';

                        if (xhr.responseJSON) {
                            errorMessage = xhr.responseJSON.message || xhr.responseJSON.error || errorMessage;
                        }

                        show_toastr('error', errorMessage);
                        submitBtn.prop('disabled', false).html(originalBtnText);
                    }
                });
            } else {
                // User cancelled, do nothing
                return false;
            }
        });

        // Handle AJAX form submissions for personal tasks (create/update only, exclude delete)
        // Only handle forms that are inside modals to avoid conflicts with regular forms
        $(document).on('submit', '#commonModal form[action*="personal-tasks"]:not([action*="destroy"])', function(e) {
            e.preventDefault();
            e.stopPropagation(); // Prevent event bubbling
            e.stopImmediatePropagation(); // Prevent other handlers from running

            // Check if this form has already been processed to prevent double submission
            if ($(this).data('processing')) {
                return false;
            }
            $(this).data('processing', true);

            const form = $(this);

            // Client-side validation
            const taskName = form.find('input[name="name"]').val().trim();
            if (!taskName) {
                form.find('input[name="name"]').focus().addClass('error');
                form.data('processing', false);
                show_toastr('error', 'Task name is required');
                return false;
            }

            // Check if at least one user is selected
            const selectedUsers = form.find('.user-option input[type="checkbox"]:checked').length;
            if (selectedUsers === 0) {
                form.data('processing', false);
                show_toastr('error', 'Please select at least one user to assign this task');
                return false;
            }

            const formData = new FormData(this);
            const submitBtn = form.find('button[type="submit"]');
            const originalBtnText = submitBtn.html();

            // Show loading state
            submitBtn.prop('disabled', true).html('<i class="ti ti-loader-2 ti-spin me-1"></i>Processing...');

            $.ajax({
                url: form.attr('action'),
                method: form.attr('method') || 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    // Reset processing flag and button state
                    form.data('processing', false);
                    submitBtn.prop('disabled', false).html(originalBtnText);

                    if (response.is_success) {
                        // Show success toast
                        show_toastr('success', response.message || response.success);

                        // Close modal if exists
                        $('.modal').modal('hide');

                        // Redirect or reload page
                        if (response.redirect) {
                            setTimeout(function() {
                                window.location.href = response.redirect;
                            }, 1000);
                        } else {
                            // Reload current page
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        }
                    } else {
                        // Show error toast
                        show_toastr('error', response.message || response.error || 'An error occurred');
                    }
                },
                error: function(xhr) {
                    // Reset processing flag and button state
                    form.data('processing', false);
                    submitBtn.prop('disabled', false).html(originalBtnText);

                    let errorMessage = 'An error occurred';

                    if (xhr.responseJSON) {
                        if (xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        } else if (xhr.responseJSON.error) {
                            errorMessage = xhr.responseJSON.error;
                        } else if (xhr.responseJSON.errors) {
                            // Handle validation errors
                            const errors = xhr.responseJSON.errors;
                            errorMessage = Object.values(errors).flat().join('<br>');
                        }
                    } else if (xhr.responseText) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            errorMessage = response.message || response.error || errorMessage;
                        } catch (e) {
                            // If not JSON, show generic error
                            errorMessage = 'Server error occurred';
                        }
                    }

                    // Show error toast
                    show_toastr('error', errorMessage);
                }
            });
        });

        // Smooth hover effects for action buttons
        $('.modern-action-btn').hover(
            function() {
                $(this).css('transform', 'translateY(-1px)');
            },
            function() {
                $(this).css('transform', 'translateY(0)');
            }
        );

        // Progress bar animation on page load
        $('.progress-bar').each(function() {
            const width = $(this).css('width');
            $(this).css('width', '0').animate({width: width}, 1000);
        });

        // Modern confirmation dialog for delete actions
        $('button[onclick*="confirm"]').click(function(e) {
            e.preventDefault();
            const form = $(this).closest('form');

            // Create modern confirmation modal
            const confirmModal = `
                <div class="modal fade" id="deleteConfirmModal" tabindex="-1">
                    <div class="modal-dialog modal-sm">
                        <div class="modal-content" style="border-radius: 1rem; border: none;">
                            <div class="modal-body text-center p-4">
                                <div class="mb-3">
                                    <i class="ti ti-alert-triangle text-warning" style="font-size: 3rem;"></i>
                                </div>
                                <h5 class="mb-2">{{ __('Delete Task') }}</h5>
                                <p class="text-muted mb-4">{{ __('Are you sure you want to delete this task? This action cannot be undone.') }}</p>
                                <div class="d-flex gap-2 justify-content-center">
                                    <button type="button" class="modern-btn modern-btn-cancel" data-bs-dismiss="modal">
                                        {{ __('Cancel') }}
                                    </button>
                                    <button type="button" class="modern-btn" style="background: #dc2626; color: white;" id="confirmDelete">
                                        {{ __('Delete') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('body').append(confirmModal);
            $('#deleteConfirmModal').modal('show');

            $('#confirmDelete').click(function() {
                form.submit();
            });

            $('#deleteConfirmModal').on('hidden.bs.modal', function() {
                $(this).remove();
            });
        });
    });
</script>
@endpush
