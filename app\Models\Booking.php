<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Booking extends Model
{
    protected $fillable = ['event_id', 'name', 'email', 'phone', 'date', 'time', 'custom_fields', 'custom_fields_value'];

    protected $casts = [
        'custom_fields' => 'array',
        'custom_fields_value' => 'array'
    ];

    // Relationship to CalendarEvent
    public function event()
    {
        return $this->belongsTo(CalendarEvent::class, 'event_id');
    }

    // Alternative relationship name for clarity
    public function calendarEvent()
    {
        return $this->belongsTo(CalendarEvent::class, 'event_id');
    }

    // Helper method to safely get custom fields as array
    public function getCustomFieldsArrayAttribute()
    {
        $customFields = $this->custom_fields;

        if (is_string($customFields)) {
            $customFields = json_decode($customFields, true);
        }

        return is_array($customFields) ? $customFields : [];
    }

    // Helper method to get custom fields count
    public function getCustomFieldsCountAttribute()
    {
        return count($this->custom_fields_array);
    }
}
