# CRM Webhook System Documentation

## Overview

The CRM Webhook System provides a centralized way to send webhook notifications to integrated modules when CRM actions are performed. The system automatically fetches base URLs from the `module_integrations` table and calls the `/saas-webhook` endpoint with structured data.

## Features

- **Centralized Webhook Management**: All webhook calls go through a single dispatcher
- **Automatic Module Integration**: Fetches webhook URLs from the database
- **Comprehensive Logging**: Dedicated webhook log file with detailed information
- **Action Constants**: Predefined action types for consistency
- **Error Handling**: Robust error handling and timeout management
- **Testing Interface**: Built-in testing tools for webhook validation

## Architecture

### Core Components

1. **CrmWebhookActions** (`app/Constants/CrmWebhookActions.php`)
   - Defines all available webhook action types
   - Provides categorized action lists
   - Validates action types

2. **ModuleWebhookService** (`app/Services/ModuleWebhookService.php`)
   - Handles HTTP requests to module endpoints
   - Manages authentication and headers
   - Logs all webhook attempts

3. **CrmWebhookDispatcher** (`app/Services/CrmWebhookDispatcher.php`)
   - Central dispatcher for all webhook events
   - Provides convenient methods for common actions
   - Handles data formatting and preparation

4. **WebhookLogger** (`app/Services/WebhookLogger.php`)
   - Dedicated logging service for webhooks
   - Structured logging with context
   - Log management utilities

## Available Webhook Actions

### Lead Actions
- `crm.lead_created` - When a new lead is created
- `crm.lead_updated` - When a lead is updated
- `crm.lead_deleted` - When a lead is deleted
- `crm.lead_stage_changed` - When a lead moves to a different stage
- `crm.lead_converted_to_deal` - When a lead is converted to a deal
- `crm.lead_assigned` - When a lead is assigned to a user
- `crm.lead_unassigned` - When a lead is unassigned

### Deal Actions
- `crm.deal_created` - When a new deal is created
- `crm.deal_updated` - When a deal is updated
- `crm.deal_deleted` - When a deal is deleted
- `crm.deal_stage_changed` - When a deal moves to a different stage
- `crm.deal_status_changed` - When a deal status changes
- `crm.deal_won` - When a deal is marked as won
- `crm.deal_lost` - When a deal is marked as lost

### Task Actions
- `crm.lead_task_created` - When a lead task is created
- `crm.lead_task_completed` - When a lead task is completed
- `crm.deal_task_created` - When a deal task is created
- `crm.deal_task_completed` - When a deal task is completed

### Communication Actions
- `crm.lead_email_sent` - When an email is sent to a lead
- `crm.lead_call_logged` - When a call is logged for a lead
- `crm.deal_email_sent` - When an email is sent for a deal
- `crm.deal_call_logged` - When a call is logged for a deal

### File Actions
- `crm.lead_file_uploaded` - When a file is uploaded to a lead
- `crm.deal_file_uploaded` - When a file is uploaded to a deal

### Booking & Appointment Actions
- `booking.appointment_scheduled` - When an appointment is scheduled
- `booking.appointment_rescheduled` - When an appointment is rescheduled
- `booking.appointment_cancelled` - When an appointment is cancelled
- `booking.appointment_reminder_time_reached` - When appointment reminder time is reached
- `booking.event_created` - When an event is created
- `booking.booking_form_submitted` - When a booking form is submitted
- `booking.date_override_added` - When a date override is added
- `booking.recurring_appointment_created` - When a recurring appointment is created
- `booking.appointment_location_changed` - When appointment location is changed

## Usage Examples

### Basic Usage in Controllers

```php
use App\Services\CrmWebhookDispatcher;

// In your controller method
$webhookDispatcher = new CrmWebhookDispatcher();

// For lead creation
$webhookDispatcher->dispatchLeadCreated($lead);

// For deal creation
$webhookDispatcher->dispatchDealCreated($deal);

// For lead conversion
$webhookDispatcher->dispatchLeadConvertedToDeal($lead, $deal);

// For task completion
$webhookDispatcher->dispatchLeadTaskCompleted($task);

// For appointment scheduling
$webhookDispatcher->dispatchAppointmentScheduled($appointment);

// For appointment rescheduling
$webhookDispatcher->dispatchAppointmentRescheduled($appointment, $oldDateTime, $newDateTime);

// For booking form submission
$webhookDispatcher->dispatchBookingFormSubmitted($booking, $formData);
```

### Advanced Usage with Custom Data

```php
// Send custom webhook with additional data
$additionalData = ['custom_field' => 'value'];
$webhookDispatcher->dispatch('crm.lead_updated', $lead, $additionalData);

// Send to specific module only
$webhookDispatcher->dispatchToModule('automatish', 'crm.lead_created', $lead);

// Send booking webhook with custom data
$webhookDispatcher->dispatch('booking.appointment_scheduled', $appointment, $additionalData);

// Send appointment cancellation with reason
$webhookDispatcher->dispatchAppointmentCancelled($appointment, 'Customer requested cancellation');
```

## Webhook Payload Structure

All webhooks send a standardized payload:

### CRM Payload Example
```json
{
    "action": "crm.lead_created",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "data": {
        "id": 123,
        "name": "John Doe",
        "email": "<EMAIL>",
        "stage": {
            "id": 1,
            "name": "New"
        },
        "pipeline": {
            "id": 1,
            "name": "Sales Pipeline"
        }
    },
    "user_id": 456,
    "source": {
        "system": "CRM",
        "version": "1.0",
        "url": "https://your-crm.com"
    }
}
```

### Booking System Payload Example
```json
{
    "action": "booking.appointment_scheduled",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "data": {
        "id": 789,
        "title": "Consultation Meeting",
        "start_time": "2024-01-20T14:00:00.000Z",
        "end_time": "2024-01-20T15:00:00.000Z",
        "location": "Conference Room A",
        "client_name": "Jane Smith",
        "client_email": "<EMAIL>",
        "status": "confirmed",
        "service": {
            "id": 5,
            "name": "Business Consultation"
        }
    },
    "user_id": 456,
    "source": {
        "system": "CRM",
        "version": "1.0",
        "url": "https://your-crm.com"
    }
}
```

## Configuration

### Module Integration Setup

1. Add entries to the `module_integrations` table:
   ```sql
   INSERT INTO module_integrations (name, base_url, api_token, enabled) 
   VALUES ('automatish', 'https://automatish.example.com', 'your-api-token', 1);
   ```

2. The system will automatically call: `https://automatish.example.com/saas-webhook`

### Logging Configuration

Webhook logs are stored in `storage/logs/webhook.log` with daily rotation (30 days retention).

## Testing

### Using the Test Interface

1. Navigate to `/webhook-test` in your CRM
2. Test individual module connections
3. Send test webhooks with sample data
4. View real-time logs and results

### Manual Testing

```php
use App\Services\ModuleWebhookService;
use App\Models\ModuleIntegration;

$integration = ModuleIntegration::where('name', 'Automatish')->first();
$webhookService = new ModuleWebhookService();
$result = $webhookService->testWebhook($integration);
```

## Error Handling

The system includes comprehensive error handling:

- **Connection Timeouts**: 30-second timeout for webhook calls
- **HTTP Errors**: Logged with status codes and response bodies
- **Network Failures**: Caught and logged with error messages
- **Invalid Actions**: Validated against predefined constants

## Monitoring and Debugging

### Log Analysis

Webhook logs include:
- Timestamp and action type
- Target module and URL
- Request payload and response
- Response time and status codes
- Error messages and stack traces

### Performance Monitoring

- Response times are tracked in milliseconds
- Success/failure rates are logged
- Module-specific statistics available

## Security

- API tokens are sent in Authorization headers
- Request payloads are JSON-encoded
- Source system identification in headers
- Webhook URLs are validated against configured integrations

## Migration from Old System

The new system replaces the old `WebhookSetting` model approach:

### Old Way:
```php
$module = 'New Lead';
$webhook = Utility::webhookSetting($module);
if($webhook) {
    $parameter = json_encode($lead);
    $status = Utility::WebhookCall($webhook['url'], $parameter, $webhook['method']);
}
```

### New Way:
```php
$webhookDispatcher = new CrmWebhookDispatcher();
$webhookDispatcher->dispatchLeadCreated($lead);

// For booking system
$webhookDispatcher->dispatchAppointmentScheduled($appointment);
$webhookDispatcher->dispatchAppointmentCancelled($appointment, 'Customer request');
```

## Best Practices

1. **Always use the dispatcher**: Don't call webhook services directly
2. **Use predefined actions**: Stick to constants in `CrmWebhookActions`
3. **Include relevant data**: Send complete entity information
4. **Handle failures gracefully**: Don't block user operations on webhook failures
5. **Monitor logs regularly**: Check for failed webhooks and connectivity issues
6. **Test integrations**: Use the testing interface to validate configurations

## Troubleshooting

### Common Issues

1. **Webhooks not sending**: Check if module integrations are enabled
2. **Connection failures**: Verify base URLs and network connectivity
3. **Authentication errors**: Confirm API tokens are correct
4. **Timeout issues**: Check if target endpoints are responsive

### Debug Steps

1. Check webhook logs: `storage/logs/webhook.log`
2. Test module connectivity using the test interface
3. Verify module integration configuration
4. Check network connectivity to target URLs
5. Validate API tokens and authentication

## Support

For issues or questions about the webhook system:
1. Check the webhook logs for error details
2. Use the testing interface to diagnose connectivity
3. Review module integration configurations
4. Consult this documentation for usage examples
