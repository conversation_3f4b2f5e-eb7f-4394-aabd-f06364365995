<!DOCTYPE html>
@php
    use App\Models\Utility;
    $setting = Utility::settings();
    $company_logo = $setting['company_logo_dark'] ?? '';
    $company_favicon = $setting['company_favicon'] ?? '';
    $logo = \App\Models\Utility::get_file('uploads/logo/');
    $color = !empty($setting['color']) ? $setting['color'] : '#22c55e'; // default to green
    $company_logo = \App\Models\Utility::GetLogo();
    $metatitle = isset($setting['meta_title']) ? $setting['meta_title'] : '';
    $metsdesc = isset($setting['meta_desc']) ? $setting['meta_desc'] : '';
    $meta_image = \App\Models\Utility::get_file('uploads/meta/');
    $meta_logo = isset($setting['meta_image']) ? $setting['meta_image'] : '';
    $get_cookie = isset($setting['enable_cookie']) ? $setting['enable_cookie'] : '';
@endphp
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <title>
        {{ Utility::getValByName('title_text') ? Utility::getValByName('title_text') : config('app.name', 'ERPGO') }}
        - @yield('page-title')</title>
    <meta name="title" content="{{ $metatitle }}">
    <meta name="description" content="{{ $metsdesc }}">
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <link rel="icon"
        href="{{ $logo . '/' . (isset($company_favicon) && !empty($company_favicon) ? $company_favicon : 'favicon.png')  . '?' . time() }}"
        type="image/x-icon" />
    <link rel="stylesheet" href="{{ asset('assets/fonts/tabler-icons.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/fonts/feather.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/fonts/fontawesome.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/fonts/material.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/style-dark.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/custom-auth.css') }}">
    <style>
        body {
            min-height: 100vh;
            background: #101114;
            background-image: radial-gradient(circle at 50% 60%, rgba(34,197,94,0.15) 0, rgba(16,17,20,1) 60%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .auth-bg-glow {
            position: fixed;
            top: 0; left: 0; width: 100vw; height: 100vh;
            z-index: 0;
            background: radial-gradient(circle at 50% 60%, rgba(34,197,94,0.15) 0, rgba(16,17,20,1) 60%);
        }
        .auth-card {
            background: #181a20;
            border-radius: 18px;
            box-shadow: 0 8px 32px 0 rgba(34,197,94,0.15);
            padding: 2.5rem 2rem 2rem 2rem;
            max-width: 400px;
            width: 100%;
            z-index: 2;
        }
        .auth-logo {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        .auth-logo-icon {
            background: #22c55e;
            border-radius: 16px;
            width: 56px;
            height: 56px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
        }
        .auth-logo-icon i {
            color: #fff;
            font-size: 2rem;
        }
        .auth-title {
            color: #22c55e;
            font-size: 1.7rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }
        .auth-subtitle {
            color: #b0b3b8;
            font-size: 1rem;
            margin-bottom: 1.5rem;
        }
        .auth-footer {
            color: #b0b3b8;
            font-size: 0.95rem;
            margin-top: 2rem;
            z-index: 2;
        }
        @media (max-width: 600px) {
            .auth-card {
                padding: 1.5rem 0.5rem 1.5rem 0.5rem;
                max-width: 98vw;
            }
        }
    </style>
</head>
<body>
    <div class="auth-bg-glow"></div>
    <div class="d-flex flex-column justify-content-center align-items-center min-vh-100 w-100">
        <div class="auth-logo">
            <div class="auth-logo-icon">
                <i class="ti ti-robot"></i>
            </div>
            <div class="auth-title">AI Automation</div>
            <div class="auth-subtitle">Intelligent automation platform</div>
        </div>
        <div class="auth-card">
            @yield('content')
        </div>
        <div class="auth-footer text-center">
            &copy; {{ date('Y') }} OMX. All rights reserved.
        </div>
    </div>
    @if ($get_cookie == 'on')
        @include('layouts.cookie_consent')
    @endif
    <script src="{{ asset('assets/js/vendor-all.js') }}"></script>
    <script src="{{ asset('assets/js/plugins/bootstrap.min.js') }}"></script>
    <script src="{{ asset('assets/js/plugins/feather.min.js') }}"></script>
    <script src="{{ asset('js/jquery.min.js') }}"></script>
    <script src="{{ asset('js/custom.js') }}"></script>
    <script>feather.replace();</script>
    @stack('custom-scripts')
</body>
</html>
