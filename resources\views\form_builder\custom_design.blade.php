@extends('layouts.admin')
@section('page-title')
    {{ __('Form Style Customization') }}
@endsection

@push('script-page')
<style>
    .style-controls {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    .color-picker-wrapper {
        position: relative;
        display: inline-block;
    }

    .color-preview {
        width: 40px;
        height: 40px;
        border-radius: 6px;
        border: 2px solid #dee2e6;
        cursor: pointer;
        display: inline-block;
        margin-left: 10px;
    }

    .font-preview {
        padding: 10px;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        margin-top: 5px;
        background: #f8f9fa;
    }

    .form-preview {
        background: white;
        border-radius: 8px;
        padding: 30px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        min-height: 500px;
        transition: all 0.3s ease;
    }

    .style-section {
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 15px;
    }

    .style-section h6 {
        margin-bottom: 15px;
        color: #495057;
        font-weight: 600;
    }

    .color-option {
        width: 30px;
        height: 30px;
        border-radius: 4px;
        border: 2px solid #fff;
        box-shadow: 0 0 0 1px #dee2e6;
        cursor: pointer;
        margin: 2px;
        display: inline-block;
    }

    .color-option.active {
        box-shadow: 0 0 0 2px #007bff;
    }

    .reset-btn {
        margin-top: 20px;
    }
</style>
@endpush

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item"><a href="{{route('form_builder.index')}}">{{__('Form Builder')}}</a></li>
    <li class="breadcrumb-item">{{__('Style Customization')}}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        <a href="{{ route('form_builder.index') }}" class="btn btn-sm btn-secondary">
            <i class="ti ti-arrow-left"></i> {{__('Back to Forms')}}
        </a>
        <button type="button" class="btn btn-sm btn-success" id="save-styles">
            <i class="ti ti-device-floppy"></i> {{__('Save Styles')}}
        </button>
        <button type="button" class="btn btn-sm btn-warning" id="reset-styles">
            <i class="ti ti-refresh"></i> {{__('Reset to Default')}}
        </button>
    </div>
@endsection

@section('content')
    <div class="row">
        <!-- Style Controls Sidebar -->
        <div class="col-md-4">
            <div class="style-controls">
                <h5 class="mb-3">{{__('Form Styling Options')}}</h5>

                <!-- Form Selection -->
                <div class="style-section">
                    <h6>{{__('Select Form')}}</h6>
                    <select class="form-select" id="form-selector">
                        <option value="">{{__('Choose a form to customize')}}</option>
                        @foreach($forms as $form)
                            <option value="{{ $form->id }}">{{ $form->name }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Background Colors -->
                <div class="style-section">
                    <h6>{{__('Background Color')}}</h6>
                    <div class="mb-3">
                        <label class="form-label">{{__('Form Background')}}</label>
                        <div class="d-flex align-items-center">
                            <input type="color" class="form-control form-control-color" id="bg-color" value="#ffffff">
                            <div class="color-preview" id="bg-preview" style="background-color: #ffffff;"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{__('Input Background')}}</label>
                        <div class="d-flex align-items-center">
                            <input type="color" class="form-control form-control-color" id="input-bg-color" value="#ffffff">
                            <div class="color-preview" id="input-bg-preview" style="background-color: #ffffff;"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{__('Button Background')}}</label>
                        <div class="d-flex align-items-center">
                            <input type="color" class="form-control form-control-color" id="btn-bg-color" value="#007bff">
                            <div class="color-preview" id="btn-bg-preview" style="background-color: #007bff;"></div>
                        </div>
                    </div>
                </div>

                <!-- Text Colors -->
                <div class="style-section">
                    <h6>{{__('Text Colors')}}</h6>
                    <div class="mb-3">
                        <label class="form-label">{{__('Label Color')}}</label>
                        <div class="d-flex align-items-center">
                            <input type="color" class="form-control form-control-color" id="label-color" value="#212529">
                            <div class="color-preview" id="label-preview" style="background-color: #212529;"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{__('Input Text Color')}}</label>
                        <div class="d-flex align-items-center">
                            <input type="color" class="form-control form-control-color" id="input-color" value="#495057">
                            <div class="color-preview" id="input-preview" style="background-color: #495057;"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{__('Button Text Color')}}</label>
                        <div class="d-flex align-items-center">
                            <input type="color" class="form-control form-control-color" id="btn-color" value="#ffffff">
                            <div class="color-preview" id="btn-preview" style="background-color: #ffffff;"></div>
                        </div>
                    </div>
                </div>

                <!-- Typography -->
                <div class="style-section">
                    <h6>{{__('Typography')}}</h6>
                    <div class="mb-3">
                        <label class="form-label">{{__('Font Family')}}</label>
                        <select class="form-select" id="font-family">
                            <option value="Arial, sans-serif">Arial</option>
                            <option value="Helvetica, sans-serif">Helvetica</option>
                            <option value="'Times New Roman', serif">Times New Roman</option>
                            <option value="Georgia, serif">Georgia</option>
                            <option value="'Courier New', monospace">Courier New</option>
                            <option value="Verdana, sans-serif">Verdana</option>
                            <option value="'Trebuchet MS', sans-serif">Trebuchet MS</option>
                            <option value="'Comic Sans MS', cursive">Comic Sans MS</option>
                            <option value="Impact, sans-serif">Impact</option>
                            <option value="'Lucida Console', monospace">Lucida Console</option>
                        </select>
                        <div class="font-preview" id="font-preview">
                            {{__('Sample text with selected font')}}
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{__('Font Size')}}</label>
                        <select class="form-select" id="font-size">
                            <option value="12px">12px</option>
                            <option value="14px" selected>14px</option>
                            <option value="16px">16px</option>
                            <option value="18px">18px</option>
                            <option value="20px">20px</option>
                            <option value="24px">24px</option>
                        </select>
                    </div>
                </div>

                <!-- Border & Spacing -->
                <div class="style-section">
                    <h6>{{__('Border & Spacing')}}</h6>
                    <div class="mb-3">
                        <label class="form-label">{{__('Input Border Color')}}</label>
                        <div class="d-flex align-items-center">
                            <input type="color" class="form-control form-control-color" id="border-color" value="#ced4da">
                            <div class="color-preview" id="border-preview" style="background-color: #ced4da;"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{__('Border Radius')}}</label>
                        <select class="form-select" id="border-radius">
                            <option value="0px">No Radius</option>
                            <option value="4px" selected>Small (4px)</option>
                            <option value="8px">Medium (8px)</option>
                            <option value="12px">Large (12px)</option>
                            <option value="20px">Extra Large (20px)</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{__('Field Spacing')}}</label>
                        <select class="form-select" id="field-spacing">
                            <option value="10px">Compact (10px)</option>
                            <option value="15px" selected>Normal (15px)</option>
                            <option value="20px">Comfortable (20px)</option>
                            <option value="30px">Spacious (30px)</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Preview Area -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5>{{__('Live Preview')}}</h5>
                    <div class="float-end">
                        <small class="text-muted">{{__('Changes apply instantly')}}</small>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-preview" id="form-preview">
                        <div id="preview-content">
                            <div class="text-center text-muted" style="padding: 100px 20px;">
                                <i class="ti ti-forms" style="font-size: 48px; opacity: 0.5;"></i>
                                <p>{{__('Select a form to see the preview with your custom styles')}}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@push('script-page')
<script>
$(document).ready(function() {
    let currentFormId = null;
    let currentStyles = {
        backgroundColor: '#ffffff',
        inputBackgroundColor: '#ffffff',
        buttonBackgroundColor: '#007bff',
        labelColor: '#212529',
        inputColor: '#495057',
        buttonColor: '#ffffff',
        fontFamily: 'Arial, sans-serif',
        fontSize: '14px',
        borderColor: '#ced4da',
        borderRadius: '4px',
        fieldSpacing: '15px'
    };

    // Form selector change
    $('#form-selector').off('change').on('change', function() {
        const formId = $(this).val();
        if (formId) {
            currentFormId = formId;
            loadFormPreview(formId);
        } else {
            showEmptyPreview();
        }
    });

    // Load form preview
    function loadFormPreview(formId) {
        // Make AJAX request to get form fields and styles
        $.ajax({
            url: `/form_builder/${formId}/styles`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    // Load existing styles if they exist
                    if (response.styles) {
                        loadExistingStyles(response.styles);
                    } else {
                        // Reset to default styles if no custom styles exist
                        resetToDefaultStyles();
                    }
                    // Generate preview with actual form fields
                    generateFormPreview(response.fields);
                } else {
                    $('#preview-content').html('<div class="alert alert-danger">' + response.message + '</div>');
                }
            },
            error: function() {
                $('#preview-content').html('<div class="alert alert-danger">Error loading form preview</div>');
            }
        });
    }

    // Reset to default styles without updating form controls
    function resetToDefaultStyles() {
        currentStyles = {
            backgroundColor: '#ffffff',
            inputBackgroundColor: '#ffffff',
            buttonBackgroundColor: '#007bff',
            labelColor: '#212529',
            inputColor: '#495057',
            buttonColor: '#ffffff',
            fontFamily: 'Arial, sans-serif',
            fontSize: '14px',
            borderColor: '#ced4da',
            borderRadius: '4px',
            fieldSpacing: '15px'
        };

        // Update form controls to show default values
        $('#bg-color').val(currentStyles.backgroundColor);
        $('#input-bg-color').val(currentStyles.inputBackgroundColor);
        $('#btn-bg-color').val(currentStyles.buttonBackgroundColor);
        $('#label-color').val(currentStyles.labelColor);
        $('#input-color').val(currentStyles.inputColor);
        $('#btn-color').val(currentStyles.buttonColor);
        $('#border-color').val(currentStyles.borderColor);
        $('#font-family').val(currentStyles.fontFamily);
        $('#font-size').val(currentStyles.fontSize);
        $('#border-radius').val(currentStyles.borderRadius);
        $('#field-spacing').val(currentStyles.fieldSpacing);

        // Update color previews
        $('.color-preview').each(function() {
            const colorInput = $(this).prev('input[type="color"]');
            $(this).css('background-color', colorInput.val());
        });

        // Update font preview
        $('#font-preview').css({
            'font-family': currentStyles.fontFamily,
            'font-size': currentStyles.fontSize
        });
    }

    // Load existing styles into the form controls
    function loadExistingStyles(styles) {
        currentStyles = { ...currentStyles, ...styles };

        // Update form controls
        $('#bg-color').val(currentStyles.backgroundColor || '#ffffff');
        $('#input-bg-color').val(currentStyles.inputBackgroundColor || '#ffffff');
        $('#btn-bg-color').val(currentStyles.buttonBackgroundColor || '#007bff');
        $('#label-color').val(currentStyles.labelColor || '#212529');
        $('#input-color').val(currentStyles.inputColor || '#495057');
        $('#btn-color').val(currentStyles.buttonColor || '#ffffff');
        $('#border-color').val(currentStyles.borderColor || '#ced4da');
        $('#font-family').val(currentStyles.fontFamily || 'Arial, sans-serif');
        $('#font-size').val(currentStyles.fontSize || '14px');
        $('#border-radius').val(currentStyles.borderRadius || '4px');
        $('#field-spacing').val(currentStyles.fieldSpacing || '15px');

        // Update color previews
        $('#bg-preview').css('background-color', currentStyles.backgroundColor || '#ffffff');
        $('#input-bg-preview').css('background-color', currentStyles.inputBackgroundColor || '#ffffff');
        $('#btn-bg-preview').css('background-color', currentStyles.buttonBackgroundColor || '#007bff');
        $('#label-preview').css('background-color', currentStyles.labelColor || '#212529');
        $('#input-preview').css('background-color', currentStyles.inputColor || '#495057');
        $('#btn-preview').css('background-color', currentStyles.buttonColor || '#ffffff');
        $('#border-preview').css('background-color', currentStyles.borderColor || '#ced4da');

        // Update font preview
        $('#font-preview').css({
            'font-family': currentStyles.fontFamily || 'Arial, sans-serif',
            'font-size': currentStyles.fontSize || '14px'
        });
    }

    // Generate form preview with actual form fields
    function generateFormPreview(formFields = null) {
        let fieldsToUse = [];

        if (formFields && formFields.length > 0) {
            // Use actual form fields
            fieldsToUse = formFields.map(field => ({
                type: field.type,
                label: field.name,
                required: field.required == 1,
                options: field.options || []
            }));
        } else {
            // Fallback to sample fields if no form fields available
            fieldsToUse = [
                { type: 'text', label: 'Full Name', required: true },
                { type: 'email', label: 'Email Address', required: true },
                { type: 'number', label: 'Phone Number', required: false },
                { type: 'date', label: 'Date of Birth', required: false },
                { type: 'textarea', label: 'Message', required: false },
                { type: 'select', label: 'Country', required: true, options: ['USA', 'Canada', 'UK'] },
                { type: 'radio', label: 'Gender', required: false, options: ['Male', 'Female', 'Other'] },
                { type: 'checkbox', label: 'Interests', required: false, options: ['Sports', 'Music', 'Travel'] }
            ];
        }

        let formHtml = '<form id="style-preview-form">';

        if (fieldsToUse.length > 0) {
            fieldsToUse.forEach(field => {
                formHtml += generateFieldHtml(field);
            });
        } else {
            formHtml += '<div class="alert alert-info">This form has no fields yet. Add some fields to see the preview.</div>';
        }

        formHtml += '<button type="submit" class="btn btn-primary preview-submit-btn">Submit Form</button>';
        formHtml += '</form>';

        $('#preview-content').html(formHtml);
        applyCurrentStyles();
    }

    // Generate field HTML
    function generateFieldHtml(field) {
        let fieldHtml = `<div class="mb-3 preview-field">`;
        fieldHtml += `<label class="form-label preview-label">${field.label}${field.required ? ' <span class="text-danger">*</span>' : ''}</label>`;

        switch(field.type) {
            case 'text':
                fieldHtml += `<input type="text" class="form-control preview-input" placeholder="Enter ${field.label.toLowerCase()}">`;
                break;
            case 'email':
                fieldHtml += `<input type="email" class="form-control preview-input" placeholder="Enter ${field.label.toLowerCase()}">`;
                break;
            case 'number':
                fieldHtml += `<input type="number" class="form-control preview-input" placeholder="Enter ${field.label.toLowerCase()}">`;
                break;
            case 'date':
                fieldHtml += `<input type="date" class="form-control preview-input">`;
                break;
            case 'textarea':
                fieldHtml += `<textarea class="form-control preview-input" rows="3" placeholder="Enter ${field.label.toLowerCase()}"></textarea>`;
                break;
            case 'select':
                fieldHtml += `<select class="form-control preview-input">`;
                fieldHtml += `<option value="">Choose ${field.label.toLowerCase()}</option>`;
                if (field.options) {
                    field.options.forEach(option => {
                        fieldHtml += `<option value="${option}">${option}</option>`;
                    });
                }
                fieldHtml += `</select>`;
                break;
            case 'radio':
                if (field.options) {
                    field.options.forEach((option, index) => {
                        fieldHtml += `<div class="form-check">`;
                        fieldHtml += `<input class="form-check-input preview-radio" type="radio" name="${field.label.replace(/\s+/g, '_').toLowerCase()}" id="${field.label.replace(/\s+/g, '_').toLowerCase()}_${index}">`;
                        fieldHtml += `<label class="form-check-label preview-label" for="${field.label.replace(/\s+/g, '_').toLowerCase()}_${index}">${option}</label>`;
                        fieldHtml += `</div>`;
                    });
                }
                break;
            case 'checkbox':
                if (field.options) {
                    field.options.forEach((option, index) => {
                        fieldHtml += `<div class="form-check">`;
                        fieldHtml += `<input class="form-check-input preview-checkbox" type="checkbox" id="${field.label.replace(/\s+/g, '_').toLowerCase()}_${index}">`;
                        fieldHtml += `<label class="form-check-label preview-label" for="${field.label.replace(/\s+/g, '_').toLowerCase()}_${index}">${option}</label>`;
                        fieldHtml += `</div>`;
                    });
                }
                break;
            case 'file':
                fieldHtml += `<input type="file" class="form-control preview-input">`;
                break;
        }

        fieldHtml += `</div>`;
        return fieldHtml;
    }

    // Apply current styles to preview
    function applyCurrentStyles() {
        const styleElement = $('#dynamic-styles');
        if (styleElement.length) {
            styleElement.remove();
        }

        const styles = `
            <style id="dynamic-styles">
                #form-preview {
                    background-color: ${currentStyles.backgroundColor} !important;
                    font-family: ${currentStyles.fontFamily} !important;
                    font-size: ${currentStyles.fontSize} !important;
                }
                .preview-field {
                    margin-bottom: ${currentStyles.fieldSpacing} !important;
                }
                .preview-label {
                    color: ${currentStyles.labelColor} !important;
                    font-family: ${currentStyles.fontFamily} !important;
                }
                .preview-input, .preview-input:focus {
                    background-color: ${currentStyles.inputBackgroundColor} !important;
                    color: ${currentStyles.inputColor} !important;
                    border-color: ${currentStyles.borderColor} !important;
                    border-radius: ${currentStyles.borderRadius} !important;
                    font-family: ${currentStyles.fontFamily} !important;
                    font-size: ${currentStyles.fontSize} !important;
                }
                .preview-radio, .preview-checkbox {
                    border-color: ${currentStyles.borderColor} !important;
                }
                .preview-submit-btn {
                    background-color: ${currentStyles.buttonBackgroundColor} !important;
                    color: ${currentStyles.buttonColor} !important;
                    border-color: ${currentStyles.buttonBackgroundColor} !important;
                    border-radius: ${currentStyles.borderRadius} !important;
                    font-family: ${currentStyles.fontFamily} !important;
                    font-size: ${currentStyles.fontSize} !important;
                }
                .preview-submit-btn:hover {
                    background-color: ${adjustBrightness(currentStyles.buttonBackgroundColor, -20)} !important;
                    border-color: ${adjustBrightness(currentStyles.buttonBackgroundColor, -20)} !important;
                }
            </style>
        `;

        $('head').append(styles);
    }

    // Adjust color brightness
    function adjustBrightness(hex, percent) {
        const num = parseInt(hex.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) + amt;
        const G = (num >> 8 & 0x00FF) + amt;
        const B = (num & 0x0000FF) + amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }

    // Style control event handlers
    $('#bg-color').on('input', function() {
        currentStyles.backgroundColor = $(this).val();
        $('#bg-preview').css('background-color', $(this).val());
        applyCurrentStyles();
    });

    $('#input-bg-color').on('input', function() {
        currentStyles.inputBackgroundColor = $(this).val();
        $('#input-bg-preview').css('background-color', $(this).val());
        applyCurrentStyles();
    });

    $('#btn-bg-color').on('input', function() {
        currentStyles.buttonBackgroundColor = $(this).val();
        $('#btn-bg-preview').css('background-color', $(this).val());
        applyCurrentStyles();
    });

    $('#label-color').on('input', function() {
        currentStyles.labelColor = $(this).val();
        $('#label-preview').css('background-color', $(this).val());
        applyCurrentStyles();
    });

    $('#input-color').on('input', function() {
        currentStyles.inputColor = $(this).val();
        $('#input-preview').css('background-color', $(this).val());
        applyCurrentStyles();
    });

    $('#btn-color').on('input', function() {
        currentStyles.buttonColor = $(this).val();
        $('#btn-preview').css('background-color', $(this).val());
        applyCurrentStyles();
    });

    $('#border-color').on('input', function() {
        currentStyles.borderColor = $(this).val();
        $('#border-preview').css('background-color', $(this).val());
        applyCurrentStyles();
    });

    $('#font-family').on('change', function() {
        currentStyles.fontFamily = $(this).val();
        $('#font-preview').css('font-family', $(this).val());
        applyCurrentStyles();
    });

    $('#font-size').on('change', function() {
        currentStyles.fontSize = $(this).val();
        $('#font-preview').css('font-size', $(this).val());
        applyCurrentStyles();
    });

    $('#border-radius').on('change', function() {
        currentStyles.borderRadius = $(this).val();
        applyCurrentStyles();
    });

    $('#field-spacing').on('change', function() {
        currentStyles.fieldSpacing = $(this).val();
        applyCurrentStyles();
    });

    // Show empty preview
    function showEmptyPreview() {
        $('#preview-content').html(`
            <div class="text-center text-muted" style="padding: 100px 20px;">
                <i class="ti ti-forms" style="font-size: 48px; opacity: 0.5;"></i>
                <p>Select a form to see the preview with your custom styles</p>
            </div>
        `);
        currentFormId = null;
    }

    // Save styles
    $('#save-styles').off('click').on('click', function() {
        if (!currentFormId) {
            show_toastr('Error', 'Please select a form first', 'error');
            return;
        }

        // Disable button to prevent multiple clicks
        const $btn = $(this);
        $btn.prop('disabled', true).text('Saving...');

        // Here you would save the styles to the server
        const stylesData = {
            form_id: currentFormId,
            styles: currentStyles
        };

        console.log('Saving styles:', stylesData);

        // AJAX save
        $.ajax({
            url: '/form_builder/save_styles',
            method: 'POST',
            data: stylesData,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    show_toastr('Success', response.message || 'Styles saved successfully!', 'success');
                } else {
                    show_toastr('Error', response.message || 'Error saving styles.', 'error');
                }
            },
            error: function(xhr) {
                let errorMessage = 'Error saving styles. Please try again.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                show_toastr('Error', errorMessage, 'error');
            },
            complete: function() {
                // Re-enable button
                $btn.prop('disabled', false).html('<i class="ti ti-device-floppy"></i> Save Styles');
            }
        });
    });

    // Reset styles
    $('#reset-styles').off('click').on('click', function() {
        // Show confirmation using the same popup as delete operations
        Swal.fire({
            title: 'Are you sure?',
            text: "You want to reset all styles to default?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, reset it!',
            cancelButtonText: 'No, cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                // Reset to default values
                currentStyles = {
                    backgroundColor: '#ffffff',
                    inputBackgroundColor: '#ffffff',
                    buttonBackgroundColor: '#007bff',
                    labelColor: '#212529',
                    inputColor: '#495057',
                    buttonColor: '#ffffff',
                    fontFamily: 'Arial, sans-serif',
                    fontSize: '14px',
                    borderColor: '#ced4da',
                    borderRadius: '4px',
                    fieldSpacing: '15px'
                };

                // Update form controls
                $('#bg-color').val(currentStyles.backgroundColor);
                $('#input-bg-color').val(currentStyles.inputBackgroundColor);
                $('#btn-bg-color').val(currentStyles.buttonBackgroundColor);
                $('#label-color').val(currentStyles.labelColor);
                $('#input-color').val(currentStyles.inputColor);
                $('#btn-color').val(currentStyles.buttonColor);
                $('#border-color').val(currentStyles.borderColor);
                $('#font-family').val(currentStyles.fontFamily);
                $('#font-size').val(currentStyles.fontSize);
                $('#border-radius').val(currentStyles.borderRadius);
                $('#field-spacing').val(currentStyles.fieldSpacing);

                // Update color previews
                $('.color-preview').each(function() {
                    const colorInput = $(this).prev('input[type="color"]');
                    $(this).css('background-color', colorInput.val());
                });

                // Update font preview
                $('#font-preview').css({
                    'font-family': currentStyles.fontFamily,
                    'font-size': currentStyles.fontSize
                });

                // Apply styles
                if (currentFormId) {
                    applyCurrentStyles();
                }

                show_toastr('Success', 'Styles reset to default!', 'success');
            }
        });
    });

    // Initialize
    showEmptyPreview();
});
</script>
@endpush
