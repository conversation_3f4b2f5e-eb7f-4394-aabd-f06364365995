<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class FormField extends Model
{
    protected $fillable = [
        'form_id',
        'name',
        'type',
        'options',
        'required',
        'placeholder',
        'order',
        'created_by',
    ];

    protected $casts = [
        'options' => 'array',
    ];

    // Override the options accessor to ensure it always returns an array
    public function getOptionsAttribute($value)
    {
        if (is_null($value)) {
            return [];
        }

        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return is_array($decoded) ? $decoded : [];
        }

        if (is_array($value)) {
            return $value;
        }

        return [];
    }

    public function form()
    {
        return $this->belongsTo(FormBuilder::class, 'form_id');
    }

    // Scope to order fields
    public function scopeOrdered($query)
    {
        return $query->orderBy('order', 'asc')->orderBy('id', 'asc');
    }
}
