<?php

/**
 * Test script to demonstrate Automatish integration logging
 * This will show you exactly what payload is sent and what response is received
 * 
 * Usage: php test_automatish_logging.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\ModuleIntegration;
use App\Http\Controllers\ModuleIntegrationController;
use Illuminate\Support\Facades\Log;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Automatish Integration Logging Test ===\n\n";

// Check if Automatish module exists
$automatishModule = ModuleIntegration::where('name', 'Automatish')->first();

if (!$automatishModule) {
    echo "❌ ERROR: Automatish module not found in module_integrations table\n";
    echo "Please add Automatish module to the database first.\n";
    exit(1);
}

echo "✅ Automatish module configuration:\n";
echo "   - Name: {$automatishModule->name}\n";
echo "   - Base URL: {$automatishModule->base_url}\n";
echo "   - Sync Endpoint: {$automatishModule->sync_endpoint}\n";
echo "   - Full Sync URL: " . rtrim($automatishModule->base_url, '/') . '/' . ltrim($automatishModule->sync_endpoint, '/') . "\n";
echo "   - API Token: " . substr($automatishModule->api_token, 0, 10) . "...\n";
echo "   - Enabled: " . ($automatishModule->enabled ? 'Yes' : 'No') . "\n\n";

// Create or get test user
$testEmail = 'automatish.test.' . time() . '@example.com';
$testUser = new User();
$testUser->name = 'Automatish Test User';
$testUser->email = $testEmail;
$testUser->password = bcrypt('testpassword123');
$testUser->type = 'company';
$testUser->created_by = 1;
$testUser->save();

echo "✅ Created test user:\n";
echo "   - ID: {$testUser->id}\n";
echo "   - Name: {$testUser->name}\n";
echo "   - Email: {$testUser->email}\n";
echo "   - Password (plain): testpassword123\n\n";

echo "📤 Expected payload to be sent to Automatish:\n";
$expectedPayload = [
    'fullName' => $testUser->name,
    'email' => $testUser->email,
    'password' => 'testpassword123'
];
echo json_encode($expectedPayload, JSON_PRETTY_PRINT) . "\n\n";

echo "🔄 Attempting sync to Automatish...\n";
echo "Check your Laravel logs (storage/logs/laravel.log) for detailed information.\n\n";

// Test the sync
$controller = new ModuleIntegrationController();

try {
    $result = $controller->syncUserToAutomatish($testUser, 'testpassword123');
    
    if ($result) {
        echo "✅ Sync completed successfully!\n";
    } else {
        echo "❌ Sync failed!\n";
    }
} catch (Exception $e) {
    echo "❌ Exception during sync: " . $e->getMessage() . "\n";
}

echo "\n📋 To see the full logs, run:\n";
echo "tail -f storage/logs/laravel.log | grep -A 20 -B 5 'Automatish'\n\n";

echo "🔍 Log entries to look for:\n";
echo "1. 'Sending request to Automatish' - Shows the exact payload and headers\n";
echo "2. 'Automatish response received' - Shows the full response from Automatish\n";
echo "3. 'User successfully synced to Automatish' - Success confirmation\n";
echo "4. 'Failed to sync user to Automatish' - Error details if sync fails\n";
echo "5. 'Exception during Automatish sync' - Exception details if something goes wrong\n\n";

// Clean up test user
$testUser->delete();
echo "🧹 Test user cleaned up.\n";

echo "\n=== Test Complete ===\n";
echo "The logs will show you:\n";
echo "- Exact URL being called\n";
echo "- Complete payload being sent\n";
echo "- Authorization header (partially masked)\n";
echo "- Full response status and body\n";
echo "- Response headers\n";
echo "- Any errors or exceptions\n";
