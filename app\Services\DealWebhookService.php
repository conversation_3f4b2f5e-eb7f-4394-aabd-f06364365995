<?php

namespace App\Services;

use App\Models\Deal;
use App\Services\CrmWebhookDispatcher;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class DealWebhookService
{
    protected $webhookDispatcher;

    public function __construct()
    {
        $this->webhookDispatcher = new CrmWebhookDispatcher();
    }

    /**
     * Check and trigger webhooks for deals with approaching deadlines
     *
     * @param int $days Number of days before deadline to trigger webhook
     * @return array Results of webhook dispatches
     */
    public function checkApproachingDeadlines($days = 3)
    {
        $results = [
            'success' => 0,
            'failed' => 0,
            'deals' => []
        ];

        $deals = Deal::whereNotNull('deadline')
            ->where('deadline', '<=', Carbon::now()->addDays($days))
            ->where('deadline', '>', Carbon::now())
            ->where('status', 'Active')
            ->get();

        foreach ($deals as $deal) {
            $daysUntilDeadline = Carbon::now()->diffInDays(Carbon::parse($deal->deadline));
            
            try {
                $this->webhookDispatcher->dispatchDealDeadlineApproaching($deal, $daysUntilDeadline);
                
                $results['success']++;
                $results['deals'][] = [
                    'id' => $deal->id,
                    'name' => $deal->name,
                    'days_until_deadline' => $daysUntilDeadline,
                    'deadline' => $deal->deadline,
                    'status' => 'success'
                ];

                Log::info("Deal deadline webhook sent successfully", [
                    'deal_id' => $deal->id,
                    'deal_name' => $deal->name,
                    'days_until_deadline' => $daysUntilDeadline
                ]);

            } catch (\Exception $e) {
                $results['failed']++;
                $results['deals'][] = [
                    'id' => $deal->id,
                    'name' => $deal->name,
                    'days_until_deadline' => $daysUntilDeadline,
                    'deadline' => $deal->deadline,
                    'status' => 'failed',
                    'error' => $e->getMessage()
                ];

                Log::error("Deal deadline webhook failed", [
                    'deal_id' => $deal->id,
                    'deal_name' => $deal->name,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $results;
    }

    /**
     * Check and trigger webhooks for inactive deals
     *
     * @param int $days Number of days of inactivity to trigger webhook
     * @return array Results of webhook dispatches
     */
    public function checkInactiveDeals($days = 7)
    {
        $results = [
            'success' => 0,
            'failed' => 0,
            'deals' => []
        ];

        $deals = Deal::where('updated_at', '<=', Carbon::now()->subDays($days))
            ->where('status', 'Active')
            ->get();

        foreach ($deals as $deal) {
            $daysSinceUpdate = Carbon::now()->diffInDays(Carbon::parse($deal->updated_at));
            
            try {
                $this->webhookDispatcher->dispatchDealInactiveForDays($deal, $daysSinceUpdate);
                
                $results['success']++;
                $results['deals'][] = [
                    'id' => $deal->id,
                    'name' => $deal->name,
                    'days_inactive' => $daysSinceUpdate,
                    'last_updated' => $deal->updated_at,
                    'status' => 'success'
                ];

                Log::info("Deal inactive webhook sent successfully", [
                    'deal_id' => $deal->id,
                    'deal_name' => $deal->name,
                    'days_inactive' => $daysSinceUpdate
                ]);

            } catch (\Exception $e) {
                $results['failed']++;
                $results['deals'][] = [
                    'id' => $deal->id,
                    'name' => $deal->name,
                    'days_inactive' => $daysSinceUpdate,
                    'last_updated' => $deal->updated_at,
                    'status' => 'failed',
                    'error' => $e->getMessage()
                ];

                Log::error("Deal inactive webhook failed", [
                    'deal_id' => $deal->id,
                    'deal_name' => $deal->name,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $results;
    }

    /**
     * Check both approaching deadlines and inactive deals
     *
     * @param int $deadlineDays Days before deadline to trigger webhook
     * @param int $inactiveDays Days of inactivity to trigger webhook
     * @return array Combined results
     */
    public function checkAll($deadlineDays = 3, $inactiveDays = 7)
    {
        $deadlineResults = $this->checkApproachingDeadlines($deadlineDays);
        $inactiveResults = $this->checkInactiveDeals($inactiveDays);

        return [
            'deadline_webhooks' => $deadlineResults,
            'inactive_webhooks' => $inactiveResults,
            'total_success' => $deadlineResults['success'] + $inactiveResults['success'],
            'total_failed' => $deadlineResults['failed'] + $inactiveResults['failed'],
            'summary' => [
                'deadline_deals' => count($deadlineResults['deals']),
                'inactive_deals' => count($inactiveResults['deals']),
                'total_deals' => count($deadlineResults['deals']) + count($inactiveResults['deals'])
            ]
        ];
    }

    /**
     * Get deals that would trigger deadline webhooks (without sending)
     *
     * @param int $days
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getDealsWithApproachingDeadlines($days = 3)
    {
        return Deal::whereNotNull('deadline')
            ->where('deadline', '<=', Carbon::now()->addDays($days))
            ->where('deadline', '>', Carbon::now())
            ->where('status', 'Active')
            ->get();
    }

    /**
     * Get deals that would trigger inactive webhooks (without sending)
     *
     * @param int $days
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getInactiveDeals($days = 7)
    {
        return Deal::where('updated_at', '<=', Carbon::now()->subDays($days))
            ->where('status', 'Active')
            ->get();
    }
}
