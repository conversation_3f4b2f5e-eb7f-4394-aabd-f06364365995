<?php

/**
 * Test script to verify employee permissions are working correctly
 * Run this from the root directory: php test_employee_permissions.php
 */

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Employee;
use App\Models\PricingPlan;
use Illuminate\Support\Facades\Hash;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing Employee Permission System\n";
echo "==================================\n\n";

try {
    // Test 1: Check if a company user has a pricing plan
    echo "Test 1: Checking company user pricing plan...\n";
    $companyUser = User::where('type', 'company')->first();
    if ($companyUser) {
        $plan = $companyUser->plan;
        if (is_numeric($plan)) {
            $pricingPlan = PricingPlan::find($plan);
            echo "✓ Company user has pricing plan: " . ($pricingPlan ? $pricingPlan->name : 'Not found') . "\n";
            if ($pricingPlan && $pricingPlan->module_permissions) {
                echo "✓ Pricing plan has modules: " . implode(', ', array_keys($pricingPlan->module_permissions)) . "\n";
            }
        } else {
            echo "✗ Company user doesn't have a valid pricing plan\n";
        }
    } else {
        echo "✗ No company user found\n";
    }
    echo "\n";

    // Test 2: Check if employees have module permissions
    echo "Test 2: Checking employee module permissions...\n";
    $employees = User::where('type', 'employee')->limit(5)->get();
    if ($employees->count() > 0) {
        foreach ($employees as $employee) {
            echo "Employee: {$employee->name} ({$employee->email})\n";
            if (!empty($employee->module_permissions)) {
                echo "  ✓ Has module permissions: " . implode(', ', array_keys($employee->module_permissions)) . "\n";
                
                // Test permission assignment
                $employee->assignEmployeeModulePermissions();
                $permissions = $employee->permissions()->pluck('name')->toArray();
                echo "  ✓ Assigned Spatie permissions: " . (count($permissions) > 0 ? implode(', ', array_slice($permissions, 0, 5)) . (count($permissions) > 5 ? '...' : '') : 'None') . "\n";
            } else {
                echo "  ✗ No module permissions found\n";
            }
            echo "\n";
        }
    } else {
        echo "✗ No employees found\n";
    }
    echo "\n";

    // Test 3: Test specific permission checks
    echo "Test 3: Testing specific permission checks...\n";
    if ($employees->count() > 0) {
        $testEmployee = $employees->first();
        echo "Testing employee: {$testEmployee->name}\n";
        
        // Test some common permissions
        $testPermissions = ['manage lead', 'manage deal', 'manage customer', 'manage employee'];
        foreach ($testPermissions as $permission) {
            $hasPermission = $testEmployee->can($permission);
            echo "  " . ($hasPermission ? "✓" : "✗") . " Can {$permission}: " . ($hasPermission ? "Yes" : "No") . "\n";
        }
    }
    echo "\n";

    // Test 4: Check if company's plan modules are accessible to employees
    echo "Test 4: Checking if employees can access company plan modules...\n";
    if ($companyUser && $employees->count() > 0) {
        $testEmployee = $employees->first();
        $companyPlan = is_numeric($companyUser->plan) ? PricingPlan::find($companyUser->plan) : null;
        
        if ($companyPlan && $companyPlan->module_permissions) {
            echo "Company plan modules: " . implode(', ', array_keys($companyPlan->module_permissions)) . "\n";
            echo "Employee can access these modules based on their permissions:\n";
            
            foreach (array_keys($companyPlan->module_permissions) as $module) {
                $hasModuleAccess = false;
                if (!empty($testEmployee->module_permissions) && isset($testEmployee->module_permissions[$module])) {
                    $hasModuleAccess = true;
                }
                echo "  " . ($hasModuleAccess ? "✓" : "✗") . " {$module}: " . ($hasModuleAccess ? "Yes" : "No") . "\n";
            }
        }
    }
    echo "\n";

    echo "Test completed successfully!\n";
    echo "\nRecommendations:\n";
    echo "1. Ensure employees are created with proper module_permissions\n";
    echo "2. Call assignEmployeeModulePermissions() after creating/updating employees\n";
    echo "3. Use Gate::check() or @can directives in views to check permissions\n";
    echo "4. The sidebar should now show modules based on employee permissions\n";

} catch (Exception $e) {
    echo "Error during testing: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
