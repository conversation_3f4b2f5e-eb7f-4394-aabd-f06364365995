<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\PricingPlan;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get all existing pricing plans
        $pricingPlans = PricingPlan::all();
        
        // Define automatish permissions (module access only, no internal permissions)
        $automatishPermissions = [
            'access automatish',
        ];
        
        foreach ($pricingPlans as $plan) {
            $modulePermissions = $plan->module_permissions ?? [];
            
            // Add automatish module to existing plans
            $modulePermissions['automatish'] = $automatishPermissions;
            
            $plan->update([
                'module_permissions' => $modulePermissions
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Get all existing pricing plans
        $pricingPlans = PricingPlan::all();
        
        foreach ($pricingPlans as $plan) {
            $modulePermissions = $plan->module_permissions ?? [];
            
            // Remove automatish module from existing plans
            unset($modulePermissions['automatish']);
            
            $plan->update([
                'module_permissions' => $modulePermissions
            ]);
        }
    }
};
