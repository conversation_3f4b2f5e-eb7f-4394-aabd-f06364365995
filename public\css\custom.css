/* Modern Project Cards */
.modern-project-card {
    background: #ffffff;
    border-radius: 16px;
    border: 1px solid #f0f0f0;
    padding: 20px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.modern-project-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #e0e0e0;
}

/* Card Header */
.card-header-modern {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.category-badge {
    font-size: 11px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.category-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* Pastel Theme Colors for Project Status */
.category-badge.bg-success {
    background: linear-gradient(135deg, #a7f3d0 0%, #d1fae5 100%) !important;
    color: #065f46 !important;
    border: 1px solid #6ee7b7;
}
.category-badge.bg-warning {
    background: linear-gradient(135deg, #fde68a 0%, #fef3c7 100%) !important;
    color: #92400e !important;
    border: 1px solid #fcd34d;
}
.category-badge.bg-danger {
    background: linear-gradient(135deg, #fecaca 0%, #fee2e2 100%) !important;
    color: #991b1b !important;
    border: 1px solid #fca5a5;
}
.category-badge.bg-info {
    background: linear-gradient(135deg, #bfdbfe 0%, #dbeafe 100%) !important;
    color: #1e40af !important;
    border: 1px solid #93c5fd;
}
.category-badge.bg-primary {
    background: linear-gradient(135deg, rgba(var(--theme-color-rgb), 0.15) 0%, rgba(var(--theme-color-rgb), 0.25) 100%) !important;
    color: var(--theme-color) !important;
    border: 1px solid rgba(var(--theme-color-rgb), 0.3);
}
.category-badge.bg-secondary {
    background: linear-gradient(135deg, #e5e7eb 0%, #f3f4f6 100%) !important;
    color: #374151 !important;
    border: 1px solid #d1d5db;
}

/* Additional pastel variations for different project types */
.category-badge.bg-purple {
    background: linear-gradient(135deg, #e9d5ff 0%, #f3e8ff 100%) !important;
    color: #7c3aed !important;
    border: 1px solid #c4b5fd;
}
.category-badge.bg-pink {
    background: linear-gradient(135deg, #fce7f3 0%, #fdf2f8 100%) !important;
    color: #be185d !important;
    border: 1px solid #f9a8d4;
}
.category-badge.bg-indigo {
    background: linear-gradient(135deg, #e0e7ff 0%, #eef2ff 100%) !important;
    color: #4338ca !important;
    border: 1px solid #a5b4fc;
}
.category-badge.bg-teal {
    background: linear-gradient(135deg, #ccfbf1 0%, #f0fdfa 100%) !important;
    color: #0f766e !important;
    border: 1px solid #5eead4;
}

.action-dots {
    background: none;
    border: none;
    color: #9ca3af;
    font-size: 16px;
    padding: 4px;
    border-radius: 6px;
    transition: all 0.2s ease;
    position: relative;
    z-index: 10;
}

.action-dots:hover {
    background-color: #f3f4f6;
    color: #374151;
}

/* Card Body */
.card-body-modern {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.project-title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 8px;
    line-height: 1.3;
}

.project-description {
    font-size: 13px;
    color: #6b7280;
    line-height: 1.5;
    margin-bottom: 20px;
    flex: 1;
}

/* Project Brand Section */
.project-brand {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding: 12px;
    background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);
    border-radius: 12px;
    border: 1px solid #f3f4f6;
    transition: all 0.2s ease;
}

.project-brand:hover {
    background: linear-gradient(135deg, #f3f4f6 0%, #f9fafb 100%);
    border-color: #e5e7eb;
}

.project-logo {
    width: 32px;
    height: 32px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
    color: white;
    flex-shrink: 0;
    background: linear-gradient(135deg, var(--theme-color) 0%, rgba(var(--theme-color-rgb), 0.8) 100%) !important;
    box-shadow: 0 2px 8px rgba(var(--theme-color-rgb), 0.3);
    transition: all 0.2s ease;
}

.project-logo:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(var(--theme-color-rgb), 0.4);
}

.brand-info {
    display: flex;
    flex-direction: column;
    min-width: 0;
}

.brand-name {
    font-size: 13px;
    font-weight: 500;
    color: #374151;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.brand-url {
    font-size: 11px;
    color: #9ca3af;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Card Footer */
.card-footer-modern {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 16px;
    border-top: 1px solid #f3f4f6;
}

.team-members {
    display: flex;
    align-items: center;
    gap: -8px;
}

.member-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: 500;
    color: white;
    border: 2px solid white;
    margin-left: -8px;
    position: relative;
    z-index: 1;
}

.member-avatar:first-child {
    margin-left: 0;
}

.member-avatar:nth-child(1) { background: var(--theme-color); }
.member-avatar:nth-child(2) {
    background: var(--theme-color);
    opacity: 0.9;
}
.member-avatar:nth-child(3) {
    background: var(--theme-color);
    opacity: 0.8;
}
.member-avatar:nth-child(4) {
    background: var(--theme-color);
    opacity: 0.7;
}

.member-avatar.more-members {
    background: #e5e7eb !important;
    color: #6b7280;
    font-size: 10px;
}

.project-date {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #6b7280;
}

.project-date i {
    font-size: 14px;
}

/* Additional card enhancements */
.modern-project-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--theme-color);
    border-radius: 16px 16px 0 0;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modern-project-card:hover::before {
    opacity: 1;
}

/* Smooth transitions for all interactive elements */
.modern-project-card * {
    transition: all 0.2s ease;
}

/* Focus states for accessibility */
.modern-project-card:focus-within {
    outline: 2px solid var(--theme-color);
    outline-offset: 2px;
}

.action-dots:focus {
    outline: 2px solid var(--theme-color);
    outline-offset: 2px;
}

/* Theme-based hover effects */
.action-dots:hover {
    background-color: rgba(var(--theme-color-rgb), 0.1);
    color: var(--theme-color);
}

.project-date i {
    color: var(--theme-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .modern-project-card {
        padding: 16px;
    }

    .project-title {
        font-size: 16px;
    }

    .project-brand {
        padding: 10px;
    }

    .team-members {
        gap: -6px;
    }

    .member-avatar {
        width: 24px;
        height: 24px;
        font-size: 10px;
        margin-left: -6px;
    }
}

/* theme-color css */
body.theme-1 {
    --theme-color: #0CAF60 !important;
    --theme-color-rgb: 12, 175, 96;
}
body.theme-2 {
    --theme-color: #584ED2 !important;
    --theme-color-rgb: 88, 78, 210;
}
body.theme-3 {
    --theme-color: #6FD943 !important;
    --theme-color-rgb: 111, 217, 67;
}
body.theme-4 {
    --theme-color: #145388 !important;
    --theme-color-rgb: 20, 83, 136;
}
body.theme-5 {
    --theme-color: #B9406B !important;
    --theme-color-rgb: 185, 64, 107;
}
body.theme-6 {
    --theme-color: #008ECC !important;
    --theme-color-rgb: 0, 142, 204;
}
body.theme-7 {
    --theme-color: #922C88 !important;
    --theme-color-rgb: 146, 44, 136;
}
body.theme-8 {
    --theme-color: #C0A145 !important;
    --theme-color-rgb: 192, 161, 69;
}
body.theme-9 {
    --theme-color: #48494B !important;
    --theme-color-rgb: 72, 73, 75;
}
body.theme-10 {
    --theme-color: #0C7785 !important;
    --theme-color-rgb: 12, 119, 133;
}
body.custom-color {
    --theme-color: var(--color-customColor) !important;
}
.Permission {
    white-space: inherit !important;
}
.action-btn {
    width: 29px;
    height: 28px;
    border-radius: 9.3552px;
    color: #fff;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    font-size: 20px;
    -ms-flex-negative: 0;
    flex-shrink: 0;
}
.repeater-action-btn {
    /* width: 23px;
    height: 23px;
    border-radius: 9.3552px;
    color: #fff;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0; */
}
.delete-form-btn {
    display: inline;
}

/* Form Builder Context Menu Styles */
.action-btn .dropdown-toggle::after {
    display: none !important;
}

.action-btn .dropdown-toggle {
    border: none !important;
    box-shadow: none !important;
}

.action-btn .dropdown-menu {
    border: 1px solid #e0e0e0;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    padding: 0.5rem 0;
    min-width: 200px;
}

.action-btn .dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    color: #495057;
    transition: all 0.15s ease-in-out;
}

.action-btn .dropdown-item:hover {
    background-color: #f8f9fa;
    color: #495057;
}

.action-btn .dropdown-item.text-danger:hover {
    background-color: #f8d7da;
    color: #721c24;
}

.action-btn .dropdown-divider {
    margin: 0.5rem 0;
}
.dash-sidebar .main-logo {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    /*height: 100%;*/
    min-height: 80px;
    max-height: 80px;
    width: 100%;
    min-width: 255px;
    /*max-width: 255px;*/
}
/*a.b-brand {*/
/*    height: 100%;*/
/*    width: 100%;*/
/*}*/
.dash-sidebar .main-logo a img {
    width: 100%;
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
    width: auto !important;
    height: auto;
    max-width: -webkit-fill-available !important;
    max-height: -webkit-fill-available !important;
    max-width: -moz-available;
    max-height: -moz-available;
    margin: 0 auto;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    vertical-align: middle;
}
.price-card {
    text-align: center;
    position: relative;
    margin-top: 30px;
    height: 100%;
    max-height: 510px;
}
.price-card.price-2 {
    color: #fff;
}
.price-card.price-2 .price-badge {
    color: #fff;
    background: #1C232F;
}
.price-card .p-price {
    font-size: 80px;
}
.price-card .price-badge {
    color: #fff;
    padding: 7px 24px;
    border-radius: 4px;
    position: absolute;
    top: 0;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}
.price-card .list-unstyled {
    display: inline-block;
}
.price-card .list-unstyled li {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.price-card .list-unstyled li+li {
    margin-top: 8px;
}
.price-card .list-unstyled .theme-avtar {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    width: 30px;
    height: 30px;
    border-radius: 10px;
    background: #fff;
    margin-right: 15px;
    margin-left: 0px;
}
.side-feature {
    overflow: hidden;
}
.faq .accordion .accordion-item {
    border-radius: 10px;
    overflow: hidden;
    border: none;
    margin-bottom: 10px;
}
.faq .accordion .accordion-item .accordion-button {
    font-weight: 500;
    padding: 1.3rem 1.25rem;
}
.faq .accordion .accordion-item .accordion-button span>i {
    font-size: 20px;
    margin-right: 8px;
}
.faq .accordion .accordion-item .accordion-button:not(.collapsed) {
    border-radius: 10px;
    background: transparent;
    -webkit-box-shadow: 0 6px 30px rgba(182, 186, 203, 0.3);
    box-shadow: 0 6px 30px rgba(182, 186, 203, 0.3);
}
.faq .accordion .accordion-item .accordion-body {
    /*padding: 2.3rem 2.3rem 2.3rem 3rem;*/
    padding: 1rem 1.25rem;
}
.choose-files div {
    color: #fff;
    background: #584ED2 !important;
    border: none;
    border-radius: 4px;
    padding: 8px 15px;
    max-width: 155px !important;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
}
.file {
    position: relative !important;
    left: 0;
    opacity: 0;
    top: 0;
    bottom: 0;
    width: 80%;
    border: none;
    padding: 0;
    margin: 0;
    cursor: pointer;
}
.file-icon {
    width: 30px;
    height: 30px;
    background: #0F5EF7;
    border-radius: 50px;
    float: left;
    text-align: center;
}
.file-icon i {
    color: #fff;
    z-index: 9999;
    position: relative;
    font-size: 14px;
}
.first-file {
    width: 100%;
    float: left;
    padding-bottom: 20px;
    position: relative;
}
.file-des {
    width: calc(100% - 40px);
    float: right;
    color: #A3AFBB;
    font-size: 12px;
}
.file-des span {
    width: 100%;
    float: left;
    color: #011C4B;
}
.general-tab .column-card {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
}
.first-file:before {
    position: absolute;
    bottom: 0;
    width: 3px;
    height: 100%;
    background: var(--bs-primary) !important;
    content: "";
    left: 25px;
}
.first-file:last-child:before {
    background: none;
}
.setting-favimg {
    width: 100px;
}
.setting-logoimg {
    width: 200px;
}
.colorinput {
    margin: 0;
    position: relative;
    cursor: pointer;
}
.colorinput-input {
    position: absolute;
    z-index: -1;
    opacity: 0;
}
.colorinput-color {
    background-color: #fdfdff;
    border-color: #e4e6fc;
    border-width: 1px;
    border-style: solid;
    display: inline-block;
    width: 1.75rem;
    height: 1.75rem;
    border-radius: 3px;
    color: #fff;
    -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}
.colorinput-color:before {
    content: '';
    opacity: 0;
    position: absolute;
    top: .25rem;
    left: .25rem;
    height: 1.25rem;
    width: 1.25rem;
    -webkit-transition: .3s opacity;
    -o-transition: .3s opacity;
    transition: .3s opacity;
    background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E") no-repeat center center/50% 50%;
}
.colorinput-input:checked~.colorinput-color:before {
    opacity: 1;
}
.img_setting {
    -webkit-filter: drop-shadow(2px 3px 7px #011C4B);
    filter: drop-shadow(2px 3px 7px #011C4B);
}
.btn-apply {
    font-size: 31px;
}
.avatar {
    text-align: center;
    border-radius: 100%;
    overflow: hidden;
    /* background-color: #eee; */
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
}
.avatar-sm {
    width: 2.4375rem;
    height: 2.4375rem;
    font-size: 0.75rem;
    border-radius: 0.2rem;
}
.avatar {
    position: relative;
    color: #FFF;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    vertical-align: middle;
    font-size: 1rem;
    font-weight: 600;
    height: 3.125rem;
    width: 3.125rem;
    border-radius: 0.25rem;
}
.avatar img {
    width: 100%;
    border-radius: 0.25rem;
}
.avatar.rounded-circle img {
    border-radius: 50%;
}
/*.avatar span {*/
/*    background-color: #051C4B;*/
/*}*/
.avatar+.avatar {
    margin-left: .25rem;
}
.avatar+.avatar-content {
    display: inline-block;
    margin-left: .75rem;
}
.avatar-xl {
    width: 6rem;
    height: 6rem;
    font-size: 1.375rem;
}
.avatar-lg {
    width: 4rem;
    height: 4rem;
    font-size: 1.25rem;
}
.avatar-sm {
    width: 2.4375rem;
    height: 2.4375rem;
    font-size: 0.75rem;
    border-radius: 0.2rem;
}
.avatar-group {
    display: inline-block;
    line-height: 1;
}
.avatar-group .avatar {
    z-index: 1;
    -webkit-transition: margin 0.15s ease-in-out;
    -o-transition: margin 0.15s ease-in-out;
    transition: margin 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
    .avatar-group .avatar {
        -webkit-transition: none;
        -o-transition: none;
        transition: none;
    }
}
.avatar-group .avatar img {
    border: 2px solid #FFF;
}
.avatar-group .avatar:hover {
    z-index: 2;
}
.avatar-group .avatar+.avatar {
    margin-left: -1.25rem;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
.avatar-group .avatar-sm+.avatar-sm {
    margin-left: -1rem;
}
.avatar-group:hover .avatar {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}
.avatar-group:hover .avatar-sm {
    border-top-left-radius: 0.2rem;
    border-bottom-left-radius: 0.2rem;
}
.hover-avatar-ungroup:hover .avatar:not(:first-child) {
    margin-left: 0;
}
.avatar-parent-child {
    display: inline-block;
    position: relative;
}
.avatar-child {
    position: absolute;
    right: 0;
    bottom: 0;
    background-color: #fff;
    border: 2px solid #FFF;
    border-radius: 0.2rem;
}
.avatar.rounded-circle+.avatar-child {
    border-radius: 50%;
}
.avatar+.avatar-child {
    width: 20px;
    height: 20px;
}
.avatar-lg+.avatar-child {
    width: 24px;
    height: 24px;
}
.avatar-sm+.avatar-child {
    width: 16px;
    height: 16px;
}
.avatar+.avatar-badge {
    width: 14px;
    height: 14px;
    right: -6px;
    bottom: 15px;
}
.avatar-lg+.avatar-badge {
    width: 16px;
    height: 16px;
    right: -5px;
    bottom: 20px;
}
.avatar-sm+.badge {
    width: 12px;
    height: 12px;
    right: -6px;
    bottom: 10px;
}
.avatar-connect {
    position: relative;
}
.avatar-connect:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    border-bottom: 2px dashed #EFF2F7;
}
.avatar-connect ul {
    margin: 0;
    padding: 0;
    list-style: none;
}
.rating {
    border: none;
    float: left;
}
.rating>input {
    display: none;
}
.rating>label:before {
    margin: 5px;
    font-size: 1.25em;
    font-family: FontAwesome;
    display: inline-block;
    content: "\f005";
}
.rating>.half:before {
    content: "\f089";
    position: absolute;
}
.rating>label {
    color: #ddd;
    float: right;
}
.rating>input:checked~label,
.rating:not(:checked)>label:hover,
.rating:not(:checked)>label:hover~label {
    color: #FFD700;
}
.rating>input:checked+label:hover,
.rating>input:checked~label:hover,
.rating>label:hover~input:checked~label,
.rating>input:checked~label:hover~label {
    color: #FFED85;
}
.table td .progress {
    height: 7px;
    width: 120px;
    margin: 0;
}
.mtt {
    margin-top: 35px;
}
.custom_messanger_counter {
    position: relative;
    top: -15px;
    left: -5px;
}
.dash-sidebar .main-logo {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    /*height: 100%;*/
    min-height: 80px;
    max-height: 80px;
    width: 100%;
    min-width: 255px;
    /*max-width: 255px;*/
}
a.b-brand {
    height: 100%;
    width: 100%;
}
.dash-sidebar .main-logo a img {
    width: 100%;
    height: 100%;
    -o-object-fit: contain;
    object-fit: contain;
    width: auto !important;
    height: auto;
    max-width: -webkit-fill-available !important;
    max-height: -webkit-fill-available !important;
    max-width: -moz-available;
    max-height: -moz-available;
    margin: 0 auto;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    vertical-align: middle;
}
.m-view-btn {
    width: 65px;
    padding: 8px 10px;
    line-height: normal;
    border-radius: 10px;
    color: #fff;
}
.m-view-btn a {
    float: right;
    color: #fff;
    text-decoration: underline;
}
.white-sapce-nowrap {
    white-space: nowrap;
}
.list-group-flush>.list-group-item:last-child {
    border: none;
    border-bottom-width: 0;
}
.active_color {
    border: 2px solid #000 !important;
}
.display-total-time {
    font-size: 14px;
    font-weight: 500;
    height: 50px;
    border: 1px solid rgba(221, 221, 221, 1);
    padding: 12px;
    background: rgba(241, 241, 241, 1);
    text-align: center;
    border-radius: 4px;
    width: 100%;
    color: black;
}
.swiper-container {
    margin: 0 auto;
    position: relative;
    overflow: hidden;
    list-style: none;
    padding: 0;
    z-index: 1;
}
.rating-stars ul {
    list-style-type: none;
    padding: 0;
    -moz-user-select: none;
    -webkit-user-select: none;
}
.rating-stars ul>li.star {
    display: inline-block;
}
.rating-stars ul>li.star.selected>i.ti {
    color: #FF912C;
}
.rating-stars ul>li.star.selected>i.fa {
    fill: #FF912C;
}
.navbar.default.top-nav-collapse {
    background: transparent;
    -webkit-box-shadow: none;
    box-shadow: none;
}
.active_color {
    border: 2px solid #000 !important;
}
.login-deafult {
    width: 139px !important;
}
/* card-icon-text-space */
.card-icon-text-space {
    margin-right: 5px;
}
.horizontal-scroll-cards p {
    width: 120px;
}
.horizontal-scroll-cards img {
    border: 2px solid #fff;
    padding: 0px;
}
.full-circle {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}
.card-body.project_table {
    height: 400px;
    overflow-y: auto;
}
.project_table::-webkit-scrollbar {
    width: 3px;
}
.project_table::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 3px #f2f2f2;
    box-shadow: inset 0 0 3px #f2f2f2;
}
.project_table::-webkit-scrollbar-thumb {
    background-color: #bababa;
}
.round-img {
    width: 80px;
    height: 80px;
    object: cover !important;
}
.big-logo {
    width: 150px;
    height: 60px;
}
.card-2 {
    height: 100%;
    /* max-height: 480px; */
    margin-bottom: 0;
}
.full-card {
    min-height: 236px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
}
.fulls-card {
    min-height: 180px;
}
.kanban-card img {
    position: relative;
    width: 39px;
    height: 38px;
    border-radius: 50% !important;
    z-index: 2;
    -webkit-transition: all 0.1s ease-in-out;
    -o-transition: all 0.1s ease-in-out;
    transition: all 0.1s ease-in-out;
    border: 2px solid #ffffff;
}
.grid_user_image img {
    margin-left: -10px;
    border: 2px solid #dbdbdb;
}
.dataTable-table>thead>tr>th {
    padding: 15px 40px 15px 0;
}
.dataTable-sorter::before,
.dataTable-sorter::after {
    right: -20px;
}
.status_badge {
    min-width: 95px;
}
.plan_card {
    width: 25%;
    float: left;
    margin-bottom: 20px;
}
.plan_card .card-body {
    min-height: 450px;
}
.active-tag {
    position: absolute;
    right: 20px;
}
.display-total-time {
    margin-top: 0;
}
@media only screen and (max-width: 1700px) {
    .plan_card {
        width: 33.33%;
        float: left;
    }
}
@media only screen and (max-width: 1440px) {
    .plan_card {
        width: 33.33%;
        float: left;
    }
}
@media only screen and (max-width: 1366px) {
    .plan_card {
        width: 50%;
        float: left;
    }
}
@media only screen and (max-width: 1199px) {
    .plan_card {
        width: 50%;
        float: left;
    }
}
@media only screen and (max-width: 991px) {
    .plan_card {
        width: 100%;
        float: left;
    }
}
.doc_status_badge {
    min-width: 100px;
}
.list_card {
    min-height: 400px;
}
.customer_card {
    height: 100%;
}
.vendor_card {
    height: 180px;
}
.logo_card {
    min-height: 280px;
}
.img_preview {
    width: 150px;
    height: 130px;
}
.setting_logo {
    top: -35px;
}
.choose-files input[type="file"] {
    display: none;
}
.drp-languages .drp-language .dropdown-toggle {
    color: #525B69;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 0.6rem 0.7rem;
    margin: 0 7.5px;
    border-radius: 4px;
    position: relative;
    font-weight: 500;
    border-radius: 8px;
    border: 1px solid rgba(110 106 106 / 20%);
}
.email_temp {
    height: 450px !important;
    overflow-y: scroll;
}
.emp_details {
    min-height: 420px !important;
}
.green-text {
    color: green;
}
.red-text {
    color: red;
}
.activity-scroll {
    overflow: auto;
    height: 400px;
}
.email-sidebar{
    max-height: 542px  !important;
    overflow-y: auto;
}
.leads-scroll {
    overflow-y: auto;
    max-height: 400px;
}
.job-create {
    min-height: 488px;
}
.email-color {
    background: #FFFFFF;
}
.svg-inline--fa.fa-w-16 {
    width: 1em;
}
.svg-inline--fa.fa-w-20 {
    width: 1.25em;
}
/*POS SYSTEM CSS*/
.purchase_status {
    min-width: 95px;
}
.pos-top-bar {
    background: #6fd944;
    border-radius: 10px;
    padding: 15px;
}
.product-list-block .product-custom-card {
    border-radius: 10px;
    -webkit-box-shadow: 0 4px 20px 1px rgba(0, 0, 0, .06), 0 1px 4px rgba(0, 0, 0, .08);
    box-shadow: 0 4px 20px 1px rgba(0, 0, 0, .06), 0 1px 4px rgba(0, 0, 0, .08);
    cursor: pointer;
    margin-bottom: 16px;
    margin-left: 8px;
    margin-right: 8px;
    outline: 1px solid #e0e3ff;
    width: calc(24.96% - 16px);
}
.product-list-block .product-custom-card .card {
    border-color: transparent !important;
    border-radius: 10px;
    border: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    margin-bottom: 0;
}
.product-list-block .product-custom-card .card .card-img-top {
    height: 100px !important;
    max-height: 100px !important;
    -o-object-fit: contain;
    object-fit: contain;
    width: 100% !important;
    border: 0;
}
.product-list-block .product-custom-card h6 {
    font-size: 14px;
}
.product-list-block .product-custom-card .fs-small {
    font-size: 12px;
}
.product-list-block .product-custom-card .item-badges {
    position: absolute;
    left: 0;
    top: -3px;
}
.product-list-block .product-custom-card__card-badge {
    border-radius: 4px;
    font-size: 11px;
    font-weight: 400;
    line-height: 1;
    padding: .25em .4em;
    text-align: center;
    vertical-align: baseline;
    white-space: nowrap;
}
.right-content .product-list-block {
    height: auto;
    max-height: 500px;
    overflow-y: auto;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    padding-top: 25px;
}
.right-content .button-list__item-active {
    background-color: #6571ff !important;
    border-color: #6571ff !important;
    color: #FFFFFF;
}
.sub-total .total-price {
    padding-bottom: 15px;
    border-bottom: 2px dashed #c5c5c5;
}
.button-list .button-list__item {
    margin-bottom: 0.5rem;
}
.cat-pad {
    background-color: #6fd944;
}
@media screen and (max-width:767px) {
    .product-list-block .product-custom-card {
        width: 100%;
    }
}
.carttable .quantity.buttons_added {
    display: block;
    text-align: center;
    position: relative;
    white-space: nowrap;
    vertical-align: middle;
    border: 1px solid #efefef;
}
.carttable .quantity.buttons_added .minus {
    border-right: 0;
}
.carttable .quantity.buttons_added input {
    display: inline-block;
    margin: 0;
    vertical-align: middle;
    -webkit-box-shadow: none;
    box-shadow: none;
    font-size: 14px;
    border: 0;
}
.carttable .quantity .input-number {
    width: 35px;
    height: 35px;
    padding: 0 5px;
    text-align: center;
    background-color: transparent;
}
.carttable .quantity .input-number::-webkit-inner-spin-button,
.carttable .quantity .input-number::-webkit-outer-spin-button {
    -webkit-appearance: none;
}
.carttable .quantity.buttons_added .plus {
    border-left: 0;
}
.carttable .quantity.buttons_added .minus,
.carttable .quantity.buttons_added .plus {
    padding: 4px 10px 8px;
    height: 35px;
    background-color: #ffffff;
    cursor: pointer;
}
.form-row>.zoom-in {
    -webkit-transition-property: background-color, border-color, color, fill, opacity, -webkit-box-shadow, -webkit-transform;
    transition-property: background-color, border-color, color, fill, opacity, -webkit-box-shadow, -webkit-transform;
    -o-transition-property: background-color, border-color, color, fill, opacity, box-shadow, transform;
    transition-property: background-color, border-color, color, fill, opacity, box-shadow, transform;
    transition-property: background-color, border-color, color, fill, opacity, box-shadow, transform, -webkit-box-shadow, -webkit-transform;
    -webkit-transition-duration: .3s;
    -o-transition-duration: .3s;
    transition-duration: .3s;
    -webkit-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    -o-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --transform-translate-x: 0;
    --transform-translate-y: 0;
    --transform-rotate: 0;
    --transform-skew-x: 0;
    --transform-skew-y: 0;
    --transform-scale-x: 1;
    --transform-scale-y: 1;
    -webkit-transform: translateX(var(--transform-translate-x)) translateY(var(--transform-translate-y)) rotate(var(--transform-rotate)) skewX(var(--transform-skew-x)) skewY(var(--transform-skew-y)) scaleX(var(--transform-scale-x)) scaleY(var(--transform-scale-y));
    -ms-transform: translateX(var(--transform-translate-x)) translateY(var(--transform-translate-y)) rotate(var(--transform-rotate)) skewX(var(--transform-skew-x)) skewY(var(--transform-skew-y)) scaleX(var(--transform-scale-x)) scaleY(var(--transform-scale-y));
    transform: translateX(var(--transform-translate-x)) translateY(var(--transform-translate-y)) rotate(var(--transform-rotate)) skewX(var(--transform-skew-x)) skewY(var(--transform-skew-y)) scaleX(var(--transform-scale-x)) scaleY(var(--transform-scale-y));
    cursor: pointer;
    margin-bottom: 10px !important;
}
.zoom-in:hover {
    --transform-scale-x: 1.05;
    --transform-scale-y: 1.05;
}
#product-listing .toacart {
    cursor: pointer;
}
.cat-list-btn .btn-primary {
    color: #3f3f3f !important;
}
body.theme-1 .cat-list-btn .btn-primary {
    background: #fff !important;
    border-color: #fff !important;
}
body.theme-1 .cat-list-btn .cat-active .btn-primary {
    background: #0CAF60 !important;
    border-color: #0CAF60 !important;
    color: #fff !important;
}
body.theme-2 .cat-list-btn .btn-primary {
    background-color: #fff !important;
    border-color: #fff !important;
}
body.theme-2 .cat-list-btn .cat-active .btn-primary {
    background-color: #584ED2 !important;
    border-color: #584ED2 !important;
    color: #fff !important;
}
body.theme-3 .cat-list-btn .btn-primary {
    background-color: #fff !important;
    border-color: #fff !important;
}
body.theme-3 .cat-list-btn .cat-active .btn-primary {
    background-color: #6fd943 !important;
    border-color: #6fd943 !important;
    color: #fff !important;
}
body.theme-4 .cat-list-btn .btn-primary {
    background-color: #fff !important;
    border-color: #fff !important;
}
body.theme-4 .cat-list-btn .cat-active .btn-primary {
    background-color: #145388 !important;
    border-color: #145388 !important;
    color: #fff !important;
}
body.theme-5 .cat-list-btn .btn-primary {
    background-color: #fff !important;
    border-color: #fff !important;
}
body.theme-5 .cat-list-btn .cat-active .btn-primary {
    background-color: #B9406B !important;
    border-color: #B9406B !important;
    color: #fff !important;
}
body.theme-6 .cat-list-btn .btn-primary {
    background-color: #fff !important;
    border-color: #fff !important;
}
body.theme-6 .cat-list-btn .cat-active .btn-primary {
    background-color: #008ECC !important;
    border-color: #008ECC !important;
    color: #fff !important;
}
body.theme-7 .cat-list-btn .btn-primary {
    background-color: #fff !important;
    border-color: #fff !important;
}
body.theme-7 .cat-list-btn .cat-active .btn-primary {
    background-color: #922C88 !important;
    border-color: #922C88 !important;
    color: #fff !important;
}
body.theme-8 .cat-list-btn .btn-primary {
    background-color: #fff !important;
    border-color: #fff !important;
}
body.theme-8 .cat-list-btn .cat-active .btn-primary {
    background-color: #C0A145 !important;
    border-color: #C0A145 !important;
    color: #fff !important;
}
body.theme-9 .cat-list-btn .btn-primary {
    background-color: #fff !important;
    border-color: #fff !important;
}
body.theme-9 .cat-list-btn .cat-active .btn-primary {
    background-color: #48494B !important;
    border-color: #48494B !important;
    color: #fff !important;
}
body.theme-10 .cat-list-btn .btn-primary {
    background-color: #fff !important;
    border-color: #fff !important;
}
body.theme-10 .cat-list-btn .cat-active .btn-primary {
    background-color: #0C7785 !important;
    border-color: #0C7785 !important;
    color: #fff !important;
}
.top-badge {
    position: absolute;
    top: 10px;
    right: 10px;
}
.product-title-name {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    min-height: 33px;
    font-size: 14px;
}

.product-title-name.small {
    font-size: 14px;
}
.cat-active .btn {
    color: #fff;
}
.tab-btns {
    min-width: 100px;
    white-space: nowrap;
    border-radius: 0.625rem !important;
    padding: 10px 20px;
    font-size: 12px;
}
.cart-product-list .table tr th {
    padding: 5px 15px !important;
}
.total-price h6 {
    font-size: 11px;
}
.product-body-nop .card:not(:last-of-type) {
    margin-bottom: 12px;
}
.product-body-nop {
    height: calc(100vh - 260px);
    overflow-y: auto;
    overflow-x: hidden;
    margin-right: -7px;
    padding-right: 7px;
}
.product-body-nop::-webkit-scrollbar {
    width: 5px;
    margin-right: -5px;
}
.product-body-nop::-webkit-scrollbar-thumb {
    background-color: lightgrey;
    border-radius: 10px;
}
.sop-card {
    margin-bottom: 10px;
}
.carttable-scroll {
    height: calc(100vh - 115px);
}
.carttable-scroll .table-responsive {
    height: calc(100vh - 300px);
}
.carttable-scroll .table-responsive::-webkit-scrollbar {
    width: 5px;
    height: 5px;
    margin-right: -5px;
}
.carttable-scroll .table-responsive::-webkit-scrollbar-thumb {
    background-color: lightgrey;
    border-radius: 10px;
}
.carttable-scroll .name,
.carttable-scroll .tax {
    padding: 0 !important;
}
.carttable-scroll .tax {
    text-align: center;
}
.total-section {
    width: 100%;
    background: #f1f1f1;
    padding: 15px;
    border-radius: 10px;
    -webkit-box-shadow: 0px 10px 10px -10px #97979780;
    box-shadow: 0px 10px 10px -10px #97979780;
}
.sop-card {
    height: 100%;
}
.product-body-nop .card {
    width: 100%;
    padding: 15px;
}

.right-content .button-list .card .btn-primary {
    min-width: auto;
    padding: 10px 16px;
    color: #060606 !important;
    font-weight: 600;
    border: 1px solid #e5e5e5 !important;
    border-radius: 8px !important;
    background-color: transparent !important;
    font-size: 14px;

}

.right-content .button-list .card.cat-active .btn-primary {
    background-color: var(--theme-color) !important;
    border-color: var(--theme-color) !important;
    color: var(--bs-white) !important;
}

.right-content .button-list .card {
    box-shadow: none;
    border-radius: 0;
}

.right-content .button-list .form-row {
    gap: 10px;
}


.product-body-nop .card .avatar {
    -o-object-fit: scale-down;
    object-fit: scale-down;
    padding: 20px 0 10px;
    background: transparent;
    min-height: 9rem;
    width: 100%;
}
.product-body-nop .card .card-body {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    padding: 25px 25px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
}
.product-body-nop .card .badge {
    padding: 4px 10px;
    width: auto;
    max-width: 100px;
    margin: 0 auto 0 0;
}
.product-body-nop .card .shadow {
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
}
.toacart {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}
.product-body-nop .card .product-title-name {
    min-height: unset;
}
/*END POS SYSTEM CSS*/
/*for messages counter*/
.message-counter {
    position: absolute !important;
    top: fpx !important;
    right: 4px !important;
    border-radius: 50%;
    font-size: 10px;
    left: 22px !important;
    width: 15px !important;
    height: 15px;
    text-align: center !important;
    color: #fff !important;
}
/*for messages counter*/
.status-drp .dash-head-link {
    color: #525b69;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 10px;
    padding: 0.6rem 0.7rem;
    border-radius: 6px;
    position: relative;
    font-weight: 500;
    border: 1px solid rgba(110 106 106 / 20%);
    background-color: #fff;
}
/* start Rating Star Widgets Style - job application star*/
.rating-stars ul {
    list-style-type: none;
    padding: 0;
    -moz-user-select: none;
    -webkit-user-select: none;
}
.rating-stars ul>li.star {
    display: inline-block;
}
.rating-stars ul>li.star>i.fas {
    font-size: 1.5em;
    /* Change the size of the stars */
    color: #ccc;
    /* Color on idle state */
}
/* Hover state of the stars */
.rating-stars ul>li.star.hover>i.fas {
    color: #FFCC36;
}
/* Selected state of the stars */
.rating-stars ul>li.star.selected>i.fas {
    color: #ffa21d;
}
.static-rating {
    display: inline-block;
}
.static-rating .star {
    color: #E0E6ED;
}
.static-rating .voted {
    color: #ffa21d;
}
/* end start Rating Star Widgets Style - job application star*/
/* project_report */
.img_group {
    margin-left: -14px;
}
.circular-progressbar .flex-wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
}
.circular-progressbar .single-chart {
    width: 60%;
    -ms-flex-pack: distribute;
    justify-content: space-around;
}
.circular-progressbar .circular-chart {
    display: block;
    margin: 10px auto;
    max-width: 100%;
    max-height: 550px;
}
.circular-progressbar .circle-bg {
    fill: none;
    stroke: #eee;
    stroke-width: 3.8;
}
.circular-progressbar .circle {
    fill: none;
    stroke-width: 2.8;
    stroke-linecap: round;
    -webkit-animation: progress 1s ease-out forwards;
    animation: progress 1s ease-out forwards;
}
@-webkit-keyframes progress {
    0% {
        stroke-dasharray: 0 100;
    }
}
@keyframes progress {
    0% {
        stroke-dasharray: 0 100;
    }
}
.circular-progressbar .circular-chart.red .circle {
    stroke: #ff3a6e;
}
.circular-progressbar .circular-chart.orange .circle {
    stroke: #fd7e14;
}
.circular-progressbar .circular-chart.green .circle {
    stroke: #6fd943;
}
.circular-progressbar .circular-chart.blue .circle {
    stroke: #3c9ee5;
}
.circular-progressbar .percentage {
    /* fill: #666;*/
    font-family: sans-serif;
    font-size: 0.5em;
    text-anchor: middle;
}
.circular-progressbar .flex-wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
}
.circular-progressbar .single-chart {
    width: 60%;
    -ms-flex-pack: distribute;
    justify-content: space-around;
}
.circular-progressbar .circular-chart {
    display: block;
    margin: 10px auto;
    max-width: 100%;
    max-height: 550px;
}
.circular-progressbar .circle-bg {
    fill: none;
    stroke: #eee;
    stroke-width: 3.8;
}
.circular-progressbar .circle {
    fill: none;
    stroke-width: 2.8;
    stroke-linecap: round;
    -webkit-animation: progress 1s ease-out forwards;
    animation: progress 1s ease-out forwards;
}
@keyframes progress {
    0% {
        stroke-dasharray: 0 100;
    }
}
.circular-progressbar .circular-chart.red .circle {
    stroke: #ff3a6e;
}
.circular-progressbar .circular-chart.orange .circle {
    stroke: #fd7e14;
}
.circular-progressbar .circular-chart.green .circle {
    stroke: #6fd943;
}
.circular-progressbar .circular-chart.blue .circle {
    stroke: #3c9ee5;
}
.circular-progressbar .percentage {
    /* fill: #666;*/
    font-family: sans-serif;
    font-size: 0.5em;
    text-anchor: middle;
}
.pos-header {
    background-color: #e7e9ec;
}
.table.modal-table td,
.table.modal-table th {
    white-space: normal;
}
.task-calendar-scroll {
    overflow-y: scroll;
    height: 700px;
}
/*start pos thermal print */
.pos-module-tbl td,
.pos-module-tbl th {
    padding: 0px !important;
}
.pos-module .product-border {
    border-bottom: 3px dotted #d8d8d8 !important;
}
/*end pos print */
/*start balance-sheet new theme*/
.nav-pills.cust-nav {
    background: #E1E9ED;
}
.nav-pills.cust-nav .nav-item .nav-link {
    color: #162C4E;
}
.data-wrapper {
    /* height: 100%; */
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
}
.data-wrapper .data-body {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
}
/*.data-wrapper .data-body .list-group-item:nth-child(2) {*/
/*    flex: 1;*/
/*}*/
/* end balance-sheet new theme*/
/* JOB PAGE START */
.job-wrapper .navbar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
}
.job-banner {
    position: relative;
    background-color: unset;
}
.job-banner .job-banner-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}
.job-banner .job-banner-bg img {
    width: 100%;
    height: 100%;
}
.job-banner .job-banner-content {
    padding: 5.9% 0;
    max-width: 360px;
    width: 100%;
    margin: auto;
}
.placedjob-section .section-title {
    padding: 35px 25px;
    background-color: var(--bs-white);
    border-radius: 10px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
    text-align: center;
}
.job-card-body {
    padding: 15px;
    border-radius: 10px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
    border: 1px solid var(--theme-color);
}
.placedjob-section {
    padding-bottom: 80px;
    background-color: unset;
}
.job-content .container {
    max-width: 1540px;
}
@media screen and (max-width:767px) {
    .job-banner .job-banner-content {
        padding: 10% 0;
    }
}
@media screen and (max-width:575px) {
    .job-banner .job-banner-content {
        padding: 21% 0;
    }
    .account-main-inner p,
    .account-inner {
        padding-left: 0 !important;
    }
}
.job-card {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.job-card .job-card-body {
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
}
.job-card .job-card-body h5 {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
}
/* JOB PAGE END */
/* apply job section */
.apply-job-section {
    padding-bottom: 80px;
    background-color: unset;
}
.apply-job-section .apply-job-wrapper {
    padding: 35px 25px;
    border-radius: 15px;
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    -ms-border-radius: 15px;
    -o-border-radius: 15px;
    margin-top: -20px;
}
@media screen and (max-width:767px) {
    .job-banner .job-banner-content {
        padding: 10% 0;
    }
    .apply-job-section .apply-job-wrapper {
        padding: 35px 15px;
    }
    .placedjob-section .section-title {
        padding: 30px 15px;
    }
}
@media screen and (max-width:575px) {
    .job-banner .job-banner-content {
        padding: 26% 0;
    }
}
/* apply job section */
.stage li {
    cursor: pointer;
}
@media (min-width: 420px) {
    .seo_image {
        height: 200px;
        width: 360px
    }
}
@media (max-width: 420px) {
    .seo_image {
        height: 150px;
        width: 200px
    }
    .schedule-wrp .schedule-item {
        -webkit-box-align: start !important;
        -ms-flex-align: start !important;
        align-items: start !important;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        gap: 10px !important;
    }

    .schedule-wrp .schedule-item .date {
        margin-bottom: 4px !important;
    }
}
.disabledCookie {
    pointer-events: none;
    opacity: 0.4;
}
.cookie_btn {
    margin-right: 15px;
}
/*start for payment setting*/
.setting-accordion .accordion-item {
    border: 1px solid #E0E6EF !important;
    border-radius: 7px;
}
.setting-accordion .accordion-header {
    background: #F8F8F8;
    border-radius: 7px;
    -webkit-border-radius: 7px;
    -moz-border-radius: 7px;
    -ms-border-radius: 7px;
    -o-border-radius: 7px;
}
.setting-accordion .accordion-header .accordion-button {
    background: #F8F8F8 !important;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    border-radius: 7px;
    -webkit-box-shadow: none;
    box-shadow: none;
    border-bottom: 1px solid transparent;
}

.setting-accordion .accordion-header .accordion-button span {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
}
.setting-accordion .accordion-header .accordion-button::after {
    margin: 0 0 0 5px;
    width: 24px;
    height: 24px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='25' viewBox='0 0 24 25' fill='none'%3E%3Cpath opacity='0.4' d='M12 22.4146C17.5228 22.4146 22 17.9374 22 12.4146C22 6.8917 17.5228 2.41455 12 2.41455C6.47715 2.41455 2 6.8917 2 12.4146C2 17.9374 6.47715 22.4146 12 22.4146Z' fill='%2325314C'/%3E%3Cpath d='M15.5301 12.8845C15.2371 12.5915 14.762 12.5915 14.469 12.8845L12.749 14.6045V8.41455C12.749 8.00055 12.413 7.66455 11.999 7.66455C11.585 7.66455 11.249 8.00055 11.249 8.41455L11.249 14.6035L9.52908 12.8835C9.23608 12.5905 8.76104 12.5905 8.46804 12.8835C8.17504 13.1765 8.17504 13.6516 8.46804 13.9446L11.468 16.9446C11.537 17.0136 11.62 17.0684 11.711 17.1064C11.802 17.1444 11.9001 17.1646 11.9981 17.1646C12.0961 17.1646 12.1929 17.1444 12.2849 17.1064C12.3769 17.0684 12.4591 17.0136 12.5281 16.9446L15.5281 13.9446C15.8231 13.6516 15.8231 13.1775 15.5301 12.8845Z' fill='%2325314C'/%3E%3C/svg%3E");
    background-size: 24px;
    border-radius: 100%;
    -webkit-border-radius: 100%;
    -moz-border-radius: 100%;
    -ms-border-radius: 100%;
    -o-border-radius: 100%;
}
.setting-accordion .accordion-item:not(:last-of-type) {
    margin-bottom: 15px;
}
/*end for payment setting*/
.float-end a>i {
    color: #FFFFFF;
}
.modal-footer .btn-light {
    margin-right: 13px;
}
/*.fc-timegrid-event-harness-inset{*/
/*    inset: 494px 0% -566px !important;*/
/*}*/
.fc-timegrid-event-harness {
    position: absolute !important;
}
.status-btn {
    border-radius: 8px;
    color: #FFFFFF;
}
/* .ps--active-y  {
    height: 100vh !important;
} */
/*start - date:19-jun-2023*/
body.no-scroll {
    overflow: hidden;
    position: relative;
}
.auth-wrapper .navbar .navbar-brand {
    display: block;
    width: 100%;
    max-width: 150px;
}
.auth-wrapper .navbar .navbar-brand img {
    width: 100%;
}
@media (max-width: 1024px) {
    .ps {
        height: 100vh !important;
    }
}
.ps {
    overflow: hidden !important;
    overflow-anchor: none;
    -ms-touch-action: auto;
    touch-action: auto;
}
.dash-sidebar .navbar-content {
    height: calc(100vh - 70px);
}
/*end- date:19-jun-2023*/
.language_option_bg option {
    background-color: #fff;
    color: #000;
}
/*[data-action] {*/
/*    background: gray !important;*/
/*}*/
/*start for balancesheet*/
.account-first span:first-child {
    width: 35%;
}
.data-wrapper .data-body .list-group-item span:nth-child(1) {
    width: 35%;
}
/*end for balancesheet*/
/*start for input search*/
.searchBoxElement {
    background-color: white;
    border: 1px solid #aaa;
    position: absolute;
    max-height: 150px;
    overflow-x: hidden;
    overflow-y: auto;
    margin: 0;
    padding: 0;
    line-height: 23px;
    list-style: none;
    z-index: 1;
    -ms-overflow-style: none;
    scrollbar-width: none;
}
.searchBoxElement span {
    padding: 0 5px;
}
.searchBoxElement li {
    background-color: white;
    color: black;
}
.searchBoxElement li:hover {
    background-color: #50a0ff;
    color: white;
}
.searchBoxElement li.selected {
    background-color: #50a0ff;
    color: white;
}
.formTextbox {
    display: block;
    width: 100%;
    padding: 0.575rem 1rem;
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.5;
    color: #293240;
    background-color: #ffffff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: 6px;
    -webkit-transition: border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
    transition: border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
    -o-transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
}
body.theme-1 .formTextbox:focus {
    border-color: var(--theme1-color);
    -webkit-box-shadow: 0 0 0 0.2rem rgb(12, 175, 96);
    box-shadow: 0 0 0 0.2rem rgb(12, 175, 96);
}
body.theme-2 .formTextbox:focus {
    border-color: var(--theme2-color);
    -webkit-box-shadow: 0 0 0 0.2rem rgb(117, 194, 81);
    box-shadow: 0 0 0 0.2rem rgb(117, 194, 81);
}
body.theme-3 .formTextbox:focus {
    border-color: var(--theme3-color);
    -webkit-box-shadow: 0 0 0 0.2rem rgb(88, 78, 210, 25%);
    box-shadow: 0 0 0 0.2rem rgb(88, 78, 210, 25%);
}
body.theme-4 .formTextbox:focus {
    border-color: var(--theme4-color);
    -webkit-box-shadow: 0 0 0 0.2rem rgb(20, 83, 136, 25%);
    box-shadow: 0 0 0 0.2rem rgb(20, 83, 136, 25%);
}
body.theme-5 .formTextbox:focus {
    border-color: var(--theme5-color);
    -webkit-box-shadow: 0 0 0 0.2rem rgb(185, 64, 107, 25%);
    box-shadow: 0 0 0 0.2rem rgb(185, 64, 107, 25%);
}
body.theme-6 .formTextbox:focus {
    border-color: var(--theme6-color);
    -webkit-box-shadow: 0 0 0 0.2rem rgb(0, 142, 204, 25%);
    box-shadow: 0 0 0 0.2rem rgb(0, 142, 204, 25%);
}
body.theme-7 .formTextbox:focus {
    border-color: var(--theme7-color);
    -webkit-box-shadow: 0 0 0 0.2rem rgb(146, 44, 136, 25%);
    box-shadow: 0 0 0 0.2rem rgb(146, 44, 136, 25%);
}
body.theme-8 .formTextbox:focus {
    border-color: var(--theme8-color);
    -webkit-box-shadow: 0 0 0 0.2rem rgb(192, 161, 69, 25%);
    box-shadow: 0 0 0 0.2rem rgb(192, 161, 69, 25%);
}
body.theme-9 .formTextbox:focus {
    border-color: var(--theme9-color);
    -webkit-box-shadow: 0 0 0 0.2rem rgb(72, 73, 75, 25%);
    box-shadow: 0 0 0 0.2rem rgb(72, 73, 75, 25%);
}
body.theme-10 .formTextbox:focus {
    border-color: var(--theme10-color);
    -webkit-box-shadow: 0 0 0 0.2rem rgb(12, 119, 133, 25%);
    box-shadow: 0 0 0 0.2rem rgb(12, 119, 133, 25%);
}
/*end for input search*/
.account-inner p {
    max-width: 25%;
    width: 100%;
}
.aacount-title h6 {
    max-width: 25%;
    width: 100%;
}
.list_colume_notifi {
    position: relative;
    display: block;
    padding: 16.66667px 25px;
    color: #212529;
    /* background-color: #ffffff; */
    border: 1px solid #f1f1f1;
}
.account-arrow {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 5px;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
}
.account-arrow p {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
}
.account-arrow .account-icon {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    color: #ffffff;
    margin-left: -20px;
    font-size: 12px;
}
body.theme-1 .account-arrow .account-icon {
    background-color: #0CAF60;
}
body.theme-2 .account-arrow .account-icon {
    background-color: #584ED2;
}
body.theme-3 .account-arrow .account-icon {
    background-color: #6FD943;
}
body.theme-4 .account-arrow .account-icon {
    background-color: #145388;
}
body.theme-5 .account-arrow .account-icon {
    background-color: #B9406B;
}
body.theme-6 .account-arrow .account-icon {
    background-color: #008ECC;
}
body.theme-7 .account-arrow .account-icon {
    background-color: #922C88;
}
body.theme-8 .account-arrow .account-icon {
    background-color: #C0A145;
}
body.theme-9 .account-arrow .account-icon {
    background-color: #48494B;
}
body.theme-10 .account-arrow .account-icon {
    background-color: #0C7785;
}
.account-arrow .account-icon i {
    -webkit-transition: .5s all ease-in-out;
    -o-transition: .5s all ease-in-out;
    transition: .5s all ease-in-out;
}
.collapse-view .account-arrow i {
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
}
.subAccount {
    font-weight: 600;
}
.apexcharts-legend-text {
    margin-right: 10px;
}
[dir="rtl"] .apexcharts-xaxis-texts-g {
    -webkit-transform: translate(10px, 40px) !important;
    -ms-transform: translate(10px, 40px) !important;
    transform: translate(10px, 40px) !important;
}
.apexcharts-yaxis-title-text {
    -webkit-transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    transform: rotate(-90deg);
    -webkit-transform-origin: 0px 133.34800720214844px !important;
    -ms-transform-origin: 0px 133.34800720214844px !important;
    transform-origin: 0px 133.34800720214844px !important;
}
.apexcharts-yaxis {
    -webkit-transform: translate(0px, 0px) !important;
    -ms-transform: translate(0px, 0px) !important;
    transform: translate(0px, 0px) !important;
}
/* .apexcharts-pie-area
{
    display: none;
} */
.color-wrp .color-picker-wrp input[type="color"] {
    background-color: #fff;
    height: 55px;
    cursor: pointer;
    border-radius: 3px;
    margin: 0px;
    padding: 0px;
    border: 0;
    margin-bottom: 5px;
    margin-left: 5px;
}
.color-wrp {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-top: 15px;
}
.color-wrp .theme-color {
    margin: 0;
}
.color-wrp .color-picker-wrp {
    width: 100px;
}
#calender_type {
    float: right;
    width: 150px;
}
.dash-header .drp-notification {
    margin-left: 10px;
}
.budget .btn {
    margin-top: 10px;
    margin-right: 10px;
}
.theme-avtar-logo {
    width: 80px;
    border-radius: 17.3552px;
    color: #fff;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    font-size: 20px;
}
/* google recapch  */
.grecaptcha-badge {
    z-index: 2;
}
.border-grey {
    border: 1px solid #cbcbcb !important;
}
.upgrade-line hr {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
}
/* IMPORT DATA TABLE SELECT CSS */
.import-data-table select {
    width: auto;
    padding-right: 35px;
    min-width: 100%;
}
/* this css for badge */
.badge {
    border-radius: 4px !important;
}
/* Kanban css */
.kanban-wrapper .card {
    -webkit-box-shadow: 0px 4px 17px #c1c5bfb3;
    box-shadow: 0px 4px 17px #c1c5bfb3;
}
.kanban-wrapper .card .card-header,
.kanban-wrapper .card .card-body {
    padding: 20px
}
.kanban-wrapper .card-list .kanban-box .card:last-of-type {
    margin-bottom: 0px;
}
/*  Image profile and other section users */
.users-image {
    position: relative;
    display: block;
    padding-top: 20%;
    height: 40px;
    max-width: 40px;
    width: 100%;
}
.users-image img {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 100%;
    width: 100%;
    -o-object-fit: contain;
    object-fit: contain;
}
.image-fixsize {
    position: relative;
    display: block;
}
.image-fixsize img {
    height: 80px;
    width: 80px;
    -o-object-fit: contain;
    object-fit: contain;
}
/* for comment */
.comment-card {
    background: var(--bs-body-bg);
    width: -webkit-fit-content;
    width: -moz-fit-content;
    width: fit-content;
    padding: 10px;
}
/* datatable  */
div.dataTables_wrapper div.dataTables_filter input {
    margin: 0 0.5em;
}
/* *{
    scrollbar-width: none !important;
} */
/* border-color */
body.theme-1 .border-primary {
    border-color: #0CAF60 !important;
}
body.theme-2 .border-primary {
    border-color: #584ed2 !important;
}
body.theme-3 .border-primary {
    border-color: #6fd943 !important;
}
body.theme-4 .border-primary {
    border-color: #145388 !important;
}
body.theme-5 .border-primary {
    border-color: #b9406b !important;
}
body.theme-6 .border-primary {
    border-color: #008ecc !important;
}
body.theme-7 .border-primary {
    border-color: #922c88 !important;
}
body.theme-8 .border-primary {
    border-color: #c0a145 !important;
}
body.theme-9 .border-primary {
    border-color: #48494b !important;
}
body.theme-10 .border-primary {
    border-color: #0c7785 !important;
}
/* button color list start */
.btn-primary-subtle {
    background-color: #0CAF60 !important;
}
.bg-warning-subtle {
    background-color: #009eff !important;
}
.bg-brown-subtitle {
    background: #674636 !important;
}
.bg-light-blue-subtitle {
    background: #3CAEA3 !important;
}
.bg-blue-subtitle {
    background: #20639B !important;
}
.bg-light-green-subtitle {
    background: #9bb958 !important;
}
/* button color list end */
/* 15-11-2024 */
.task-product-card {
    border: none !important;
    -webkit-box-shadow: 0 6px 30px rgba(182, 186, 203, 0.3) !important;
    box-shadow: 0 6px 30px rgba(182, 186, 203, 0.3) !important;
}
.task-product-wrapper .col-xl-3 {
    padding: 0 10px;
}
.task-product-wrapper {
    margin: 0 -10px;
}
/* .project-image-item{
    width: 45px;
    height:45px;
}
.project-image-item img{
    width:100%;
    height:100%;
} */
@media (max-width: 1400px) {
    .task-product-card .card-body,
    .full-card {
        padding: 20px 15px;
    }
    .task-product-wrapper .avatar-group .avatar {
        width: 30px;
        height: 30px;
    }
}
/* Dashboard Link */
.dashboard-link {
    color: #060606 !important;
    text-transform: capitalize;
}
/* All model close button css */
.btn-close:focus,
.btn-close:hover {
    outline: 0;
    -webkit-box-shadow: 0 0 0 0.2rem rgba(81, 69, 157, 0.25);
    box-shadow: 0 0 0 0.2rem rgba(81, 69, 157, 0.25);
    opacity: 1;
    border-radius: 0.25rem;
    -webkit-border-radius: 0.25rem;
    -moz-border-radius: 0.25rem;
    -ms-border-radius: 0.25rem;
    -o-border-radius: 0.25rem;
}
/* All model close button css */
/* templates css */
.invoice-row {
    gap: 20px 0;
}
.language-sidebar {
    max-height: 500px;
    overflow-y: auto;
}
/* templates css */
/* hrm system setup tab */
.hrm_setup_tab li {
    -webkit-box-flex: 0 !important;
    -ms-flex: 0 0 auto !important;
    flex: 0 0 auto !important;
}
.information-tab {
    padding: 20px !important;
    gap: 12px;
}

.information-tab>.nav-item>a>.nav-link {
    border-radius: 5px !important;
    color: var(--theme-color);
}
/* delete confirm */
.swal2-container {
    z-index: 1073 !important;
}/* dashboard card design css */
.dash-info-card .info-card-inner {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    z-index: 1;
}

.dash-info-card .info-card-inner .star-bg,
.pos-dash-card .pos-card-inner .star-bg,
.warehouse-card .warehouse-card-inner .star-bg {
    position: absolute;
    z-index: -1;
    top: 10px;
    left: 40px;
}

.bottom-svg {
    position: absolute;
    bottom: 0;
    right: 0;
    opacity: 0.4;
    z-index: -1;
}

.dash-info-card:nth-child(4n + 1) .info-card-inner .star-bg path,
.pos-dash-card:nth-child(4n + 1) .pos-card-inner .star-bg path,
.warehouse-card:nth-child(3n + 1) .warehouse-card-inner svg path,
.project-dash-card:nth-child(3n + 1) .project-card-inner .star-bg path {
    fill: #FF3A6E;
}

.dash-info-card:nth-child(4n + 2) .info-card-inner .star-bg path,
.pos-dash-card:nth-child(4n + 2) .pos-card-inner .star-bg path,
.warehouse-card:nth-child(3n + 2) .warehouse-card-inner svg path,
.project-dash-card:nth-child(3n + 2) .project-card-inner .star-bg path {
    fill: #0CAF60;
}

.dash-info-card:nth-child(4n + 3) .info-card-inner .star-bg path,
.pos-dash-card:nth-child(4n + 3) .pos-card-inner .star-bg path,
.warehouse-card:nth-child(3n + 3) .warehouse-card-inner svg path,
.project-dash-card:nth-child(3n + 3) .project-card-inner .star-bg path {
    fill: #FFA21D;
}

.dash-info-card:nth-child(4n + 4) .info-card-inner .star-bg path,
.pos-dash-card:nth-child(4n + 4) .pos-card-inner .star-bg path {
    fill: #3EC9D6;
}

.dash-info-card:nth-child(4n + 1) .info-card-inner,
.pos-dash-card:nth-child(4n + 1) .pos-card-inner,
.deals-card:nth-child(4n + 1) .deals-card-inner,
.support-ticket-card:nth-child(4n + 1) .support-card-inner,
.crm-dash-card:nth-child(3n + 1) .crm-card-inner,
.payout-card:nth-child(3n + 1) .payout-card-inner,
.job-info-card:nth-child(3n + 1) .job-card-inner,
/* Leave cards with green box shadow */
.leave-card .leave-card-inner {
    background: white;
    box-shadow: 0 0 15px rgba(12, 175, 96, 0.2);
    border-radius: 8px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.leave-card .leave-card-inner:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(12, 175, 96, 0.3);
}

.project-dash-card:nth-child(3n + 1) .project-card-inner {
    background: rgb(255, 255, 255);
}

.dash-info-card:nth-child(4n + 2) .info-card-inner,
.pos-dash-card:nth-child(4n + 2) .pos-card-inner,
.deals-card:nth-child(4n + 2) .deals-card-inner,
.support-ticket-card:nth-child(4n + 2) .support-card-inner,
.crm-dash-card:nth-child(3n + 2) .crm-card-inner,
.payout-card:nth-child(3n + 2) .payout-card-inner,
.job-info-card:nth-child(3n + 2) .job-card-inner,
.project-dash-card:nth-child(3n + 2) .project-card-inner {
    background: rgb(255, 255, 255);
}

.dash-info-card:nth-child(4n + 3) .info-card-inner,
.pos-dash-card:nth-child(4n + 3) .pos-card-inner,
.deals-card:nth-child(4n + 3) .deals-card-inner,
.support-ticket-card:nth-child(4n + 3) .support-card-inner,
.crm-dash-card:nth-child(3n + 3) .crm-card-inner,
.payout-card:nth-child(3n + 3) .payout-card-inner,
.job-info-card:nth-child(3n + 3) .job-card-inner,
.project-dash-card:nth-child(3n + 3) .project-card-inner {
    background: rgb(255, 255, 255);
}

.dash-info-card:nth-child(4n + 4) .info-card-inner,
.pos-dash-card:nth-child(4n + 4) .pos-card-inner,
.deals-card:nth-child(4n + 4) .deals-card-inner,
.support-ticket-card:nth-child(4n + 4) .support-card-inner,
.leave-card:nth-child(4n + 4) .leave-card-inner {
    background: rgb(255, 255, 255);
}

.dash-info-card .info-card-inner .info-icon,
.pos-dash-card .pos-card-inner .pos-icon,
.warehouse-card .warehouse-card-inner .warehouse-icon,
.job-info-card .job-card-inner .job-icon,
.deals-card .deals-card-inner .deals-icon,
.support-ticket-card .support-card-inner .support-icon,
.payout-card .payout-card-inner .payout-icon {
    height: 45px;
    width: 45px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding: 6px;
    background-color: #fff;
    border-radius: 4px;
}

.support-ticket-card .support-card-inner .icon-inner,
.payout-card .payout-card-inner .icon-inner {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    border-radius: 5px;
}

.dash-info-card .info-card-inner .info-icon svg,
.pos-dash-card .pos-card-inner .pos-icon svg,
.warehouse-card .warehouse-card-inner .warehouse-icon svg,
.job-info-card .job-card-inner .job-icon svg,
.deals-card .deals-card-inner .deals-icon svg,
.support-ticket-card .support-card-inner .support-icon svg,
.payout-card .payout-card-inner .payout-icon svg {
    height: 24px;
    width: 24px;
}

.dash-info-card .info-card-inner .info-icon svg path,
.pos-dash-card .pos-card-inner .pos-icon svg path,
.job-info-card .job-card-inner .job-icon svg path,
.deals-card .deals-card-inner .deals-icon svg path,
.support-ticket-card .support-card-inner .support-icon svg path,
.payout-card .payout-card-inner .payout-icon svg path {
    fill: #fff;
}

.dash-info-card:nth-child(4n + 1) .info-card-inner .info-icon svg path,
.dash-info-card:nth-child(4n + 1) .info-card-inner .bottom-svg path,
.pos-dash-card:nth-child(4n + 1) .pos-card-inner .bottom-svg path,
.dash-info-card:nth-child(4n + 1) .info-card-inner .bottom-svg rect,
.pos-dash-card:nth-child(4n + 1) .pos-card-inner .pos-icon svg path {
    fill: #FF3A6E;
}

.dash-info-card:nth-child(4n + 2) .info-card-inner .info-icon svg path,
.dash-info-card:nth-child(4n + 2) .info-card-inner .bottom-svg path,
.dash-info-card:nth-child(4n + 2) .info-card-inner .bottom-svg rect,
.pos-dash-card:nth-child(4n + 2) .pos-card-inner .pos-icon svg path,
.pos-dash-card:nth-child(4n + 2) .pos-card-inner .bottom-svg path {
    fill: #0CAF60;
}

.dash-info-card:nth-child(4n + 3) .info-card-inner .info-icon svg path,
.dash-info-card:nth-child(4n + 3) .info-card-inner .bottom-svg path,
.dash-info-card:nth-child(4n + 3) .info-card-inner .bottom-svg rect,
.pos-dash-card:nth-child(4n + 3) .pos-card-inner .bottom-svg path,
.pos-dash-card:nth-child(4n + 3) .pos-card-inner .pos-icon svg path {
    fill: #FFA21D;
}

.dash-info-card:nth-child(4n + 4) .info-card-inner .info-icon svg path,
.dash-info-card:nth-child(4n + 4) .info-card-inner .bottom-svg path,
.dash-info-card:nth-child(4n + 4) .info-card-inner .bottom-svg rect,
.pos-dash-card:nth-child(4n + 4) .pos-card-inner .bottom-svg path,
.pos-dash-card:nth-child(4n + 4) .pos-card-inner .pos-icon svg path {
    fill: #3EC9D6;
}

.dash-info-card:nth-child(4n + 1) .info-card-inner .info-content .info-link,
.pos-dash-card:nth-child(4n + 1) .pos-info a {
    color: #FF3A6E !important;
}

.dash-info-card:nth-child(4n + 2) .info-card-inner .info-content .info-link,
.pos-dash-card:nth-child(4n + 2) .pos-info a {
    color: #0CAF60 !important;
}

.dash-info-card:nth-child(4n + 3) .info-card-inner .info-content .info-link,
.pos-dash-card:nth-child(4n + 3) .pos-info a {
    color: #FFA21D !important;
}

.dash-info-card:nth-child(4n + 4) .info-card-inner .info-content .info-link,
.pos-dash-card:nth-child(4n + 4) .pos-info a {
    color: #3EC9D6 !important;
}


.dash-info-card .info-card-inner .card-label {
    border-radius: 6px;
    padding: 7px 12px;
    background-color: #FF3A6E;
    color: var(--bs-white);
    font-weight: 600;
}

.dash-info-card:nth-child(4n + 2) .info-card-inner .card-label {
    background-color: #0CAF60;
}

.dash-info-card:nth-child(4n + 3) .info-card-inner .card-label {
    background-color: #FFA21D;
}

.job-content span,
.support-content span,
.payout-content span {
    color: #060606;
}

/* income-card css */
.income-card .income-card-inner {
    position: relative;
    z-index: 1;
}

.income-card .income-card-inner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    height: 100%;
    width: 2px;
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);
    background: -o-radial-gradient(#6FD943 0%, #ffffff 100%, #6FD943 0%);
    background: radial-gradient(#6FD943 0%, #ffffff 100%, #6FD943 0%);
}

.income-card .income-card-inner .income-card-left,
.income-card .income-card-inner .income-card-right {
    max-width: 40%;
    width: 100%;
}

.income-card .income-card-left .income-info:not(:last-of-type),
.income-card .income-card-right .income-info:not(:last-of-type) {
    margin: 0 0 20px;
}

.income-card .income-info .income-icon-wrp {
    gap: 12px;
    margin: 0 0 14px;
}

.income-card .income-info .income-icon {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    height: 35px;
    width: 35px;
    border-radius: 4px;
}

.income-card .income-info.iday .income-icon {
    background-color: #6FD943;
}

.income-card .income-info.imonth .income-icon {
    background-color: #FFA21D;
}

.income-card .income-info.eday .income-icon {
    background-color: #3EC9D6;
}

.income-card .income-info.emonth .income-icon {
    background-color: #FF3A6E;
}

.income-card .income-info .income-icon svg {
    height: 20px;
    width: 20px;
}

.income-card .income-info .income-icon svg path {
    fill: #fff;
}

.income-card .income-info .income-text {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

.income-card .income-info .progress-line {
    position: relative;
    height: 4px;
    width: 100%;
    border-radius: 10px;
}

.income-card .income-info.iday .progress-line {
    background-color: rgba(111, 217, 67, 0.16);
}

.income-card .income-info.imonth .progress-line {
    background-color: rgba(255, 162, 29, 0.16);
}

.income-card .income-info.eday .progress-line {
    background-color: rgba(62, 201, 214, 0.16);
}


.income-card .income-info.emonth .progress-line {
    background-color: rgba(255, 58, 110, 0.16);
}

.income-card .income-info .progress-line::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 55%;
    border-radius: 10px;
    -webkit-animation: load 3s normal forwards;
    animation: load 3s normal forwards;
}

.income-card .income-info.iday .progress-line::before {
    background: #6FD943;
}

.income-card .income-info.imonth .progress-line::before {
    background: #FFA21D;
}

.income-card .income-info.eday .progress-line::before {
    background: #3EC9D6;
}

.income-card .income-info.emonth .progress-line::before {
    background: #FF3A6E;
}

.support-ticket-card:nth-child(4n + 1) .support-card-inner .support-icon svg path,
.job-info-card:nth-child(4n + 1) .job-card-inner .job-icon svg path,
.payout-card:nth-child(2n + 1) .payout-card-inner .payout-icon svg path,
.crm-dash-card:nth-child(3n + 1) .crm-card-inner .crm-icon svg path,
.deals-card:nth-child(4n + 1) .deals-card-inner .deals-icon svg path,
.leave-card:nth-child(4n + 1) .leave-card-inner .leave-icon svg path,
.project-dash-card:nth-child(3n + 1) .project-card-inner .project-icon svg path {
    fill: #FF3A6E;
}

.support-ticket-card:nth-child(4n + 2) .support-card-inner .support-icon svg path,
.job-info-card:nth-child(4n + 2) .job-card-inner .job-icon svg path,
.payout-card:nth-child(2n + 2) .payout-card-inner .payout-icon svg path,
.crm-dash-card:nth-child(3n + 2) .crm-card-inner .crm-icon svg path,
.deals-card:nth-child(4n + 2) .deals-card-inner .deals-icon svg path,
.leave-card:nth-child(4n + 2) .leave-card-inner .leave-icon svg path,
.project-dash-card:nth-child(3n + 2) .project-card-inner .project-icon svg path{
    fill: #0CAF60;
}

.support-ticket-card:nth-child(4n + 3) .support-card-inner .support-icon svg path,
.job-info-card:nth-child(4n + 3) .job-card-inner .job-icon svg path,
.crm-dash-card:nth-child(3n + 3) .crm-card-inner .crm-icon svg path,
.deals-card:nth-child(4n + 3) .deals-card-inner .deals-icon svg path,
.leave-card:nth-child(4n + 3) .leave-card-inner .leave-icon svg path,
.project-dash-card:nth-child(3n + 3) .project-card-inner .project-icon svg path{
    fill: #FFA21D;
}

.support-ticket-card:nth-child(4n + 4) .support-card-inner .support-icon svg path,
.job-info-card:nth-child(4n + 4) .job-card-inner .job-icon svg path,
.deals-card:nth-child(4n + 4) .deals-card-inner .deals-icon svg path,
.leave-card:nth-child(4n + 4) .leave-card-inner .leave-icon svg path {
    fill: #3EC9D6;
}

.leave-card:nth-child(4n + 1) .leave-card-inner .leave-icon i{
    color: #FF3A6E !important;
}
.leave-card:nth-child(4n + 2) .leave-card-inner .leave-icon i{
    color: #0CAF60 !important;
}
.leave-card:nth-child(4n + 3) .leave-card-inner .leave-icon i{
    color: #FFA21D !important;
}
.leave-card:nth-child(4n + 4) .leave-card-inner .leave-icon i{
    color: #3EC9D6 !important;
}

.warehouse-card:nth-child(3n + 1) .warehouse-card-inner .icon-inner,
.support-ticket-card:nth-child(4n + 1) .support-card-inner .icon-inner {
    background-color: #ff3a6e;
}

.support-ticket-card:nth-child(4n + 2) .support-card-inner .icon-inner {
    background-color: #0CAF60;
}

.warehouse-card:nth-child(3n + 3) .warehouse-card-inner .icon-inner,
.support-ticket-card:nth-child(4n + 3) .support-card-inner .icon-inner {
    background-color: #FFA21D;
}

.warehouse-card:nth-child(3n + 2) .warehouse-card-inner .icon-inner,
.support-ticket-card:nth-child(4n + 4) .support-card-inner .icon-inner {
    background-color: #3EC9D6;
}

.deals-card .deals-card-inner .deals-icon svg path {
    fill: var(--bs-white);
}

.deals-card:nth-child(4n + 1) .deals-card-inner h5 {
    color: #FF3A6E;
}

.deals-card:nth-child(4n + 2) .deals-card-inner h5 {
    color: #0CAF60;
}

.deals-card:nth-child(4n + 3) .deals-card-inner h5 {
    color: #FFA21D;
}

.deals-card:nth-child(4n + 4) .deals-card-inner h5 {
    color: #3EC9D6;
}

/* deals-card-css-end */
.active-checkmark {
    fill: var(--bs-white) !important;
}
@-webkit-keyframes load {
    0% {
        width: 0;
    }
    100% {
        width: 55%;
    }
}
@keyframes load {
    0% {
        width: 0;
    }
    100% {
        width: 55%;
    }
}
/* scrollbar css */
::-webkit-scrollbar {
    height: 6px;
    width: 6px;
}
::-webkit-scrollbar-thumb {
    background: #c6ceed;
    border-radius: 10px;
}
/* nav-ul css */
.nav-pills {
    gap: 0.5rem;
}
.nav .nav-item .nav-link {
    position: relative;
    border-radius: 4px;
    border: 1px solid var(--theme-color);
}

/* staff-info-card css */
.staff-info-card .staff-info-inner {
    padding: 12px;
    border-radius: 10px;
}

.staff-info-card .staff-info p {
    font-weight: 600;
}

.staff-info-card {
    padding: 0 6px;
}

.staff-info-card-wrp .row {
    margin: 0 -6px;
}

.warehouse-card:nth-child(3n + 1) .warehouse-card-inner {
    background: rgba(255, 58, 110, 0.1);
}

.warehouse-card:nth-child(3n + 2) .warehouse-card-inner {
    background: rgba(111, 217, 67, 0.1);
}


.warehouse-card:nth-child(3n + 3) .warehouse-card-inner {
    background: rgba(255, 162, 29, 0.1);
}

.staff-info-card .staff-info {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

.staff-info-card .staff-info-icon {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    height: 40px;
    width: 40px;
    border-radius: 5px;
    background-color: var(--bs-white);
}

.staff-info-card:nth-child(4n + 1) .staff-info-inner{
    background-color: rgba(255, 255, 255, 0);
}
.staff-info-card:nth-child(4n + 2) .staff-info-inner{
    background-color: rgba(255, 255, 255, 0.1);

}
.staff-info-card:nth-child(4n + 3) .staff-info-inner {
    background-color: rgba(255, 255, 255, 0.1);
}
.staff-info-card:nth-child(4n + 4) .staff-info-inner{
    background-color: rgba(255, 255, 255, 0.1);
}

.staff-info-card:nth-child(4n + 1) .staff-info-icon svg path {
    fill: #FF3A6E;
}

.staff-info-card:nth-child(4n + 2) .staff-info-icon svg path {
    fill: #0CAF60;
}

.staff-info-card:nth-child(4n + 3) .staff-info-icon svg path {
    fill: #ffa21d;
}

/* Add green box shadow to all staff info cards */
.staff-info-card .staff-info-inner {
    box-shadow: 0 4px 12px rgba(12, 175, 96, 0.15);
    transition: all 0.3s ease;
}

.staff-info-card .staff-info-inner:hover {
    box-shadow: 0 8px 16px rgba(12, 175, 96, 0.2);
    transform: translateY(-2px);
}

.staff-info-card:nth-child(4n + 4) .staff-info-icon svg path {
    fill: #3EC9D6;
}

.staff-info-card .staff-info-icon svg {
    height: 25px;
    width: 25px;
}

.staff-info-card .staff-info-icon svg path {
    fill: #fff;
}

/* report-card css */
.report-card .card-body {
    padding: 15px;
}

.report-card .report-icon {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    height: 40px;
    width: 40px;
    border-radius: 5px;
    background-color: var(--theme-color);
}

.report-card .report-icon svg {
    height: 22px;
    width: 22px;
}

.report-card .report-icon svg path,
.report-card .report-icon svg rect {
    fill: var(--bs-white);
}

/* leave-card css */
.leave-card .leave-card-inner,
.project-dash-card .project-card-inner,
.warehouse-card .warehouse-card-inner,
.deals-card .deals-card-inner {
    position: relative;
    z-index: 1;
    padding: 15px;
    height: 100%;
    overflow: hidden;
    border-radius: 10px;
}

.leave-card .leave-card-inner .top-svg {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0.1;
    z-index: -1;
}



.leave-card:nth-child(3n + 1) .leave-card-inner .top-svg path,
.leave-card:nth-child(3n + 1) .leave-card-inner .bottom-svg path,
.project-dash-card:nth-child(3n + 1) .project-card-inner .bg-svg rect {
    fill: #FF3A6E;
}

.leave-card:nth-child(3n + 2) .leave-card-inner .top-svg path,
.leave-card:nth-child(3n + 2) .leave-card-inner .bottom-svg path,
.project-dash-card:nth-child(3n + 2) .project-card-inner .bg-svg rect {
    fill: #FFA21D;
}

.leave-card:nth-child(3n + 3) .leave-card-inner .top-svg path,
.leave-card:nth-child(3n + 3) .leave-card-inner .bottom-svg path,
.project-dash-card:nth-child(3n + 3) .project-card-inner .bg-svg rect {
    fill: #3EC9D6;
}


.leave-card .leave-card-inner .leave-info {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

.leave-card .leave-card-inner .leave-icon,
.crm-dash-card .crm-card-inner .crm-icon,
.project-dash-card .project-card-inner .project-icon {
    height: 45px;
    width: 45px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding: 6px;
    background-color: #fff;
    border-radius: 4px;
}

.leave-card .leave-card-inner .leave-icon svg,
.crm-dash-card .crm-card-inner .crm-icon svg,
.project-dash-card .project-card-inner .project-icon svg {
    height: 24px;
    width: 24px;
}

.leave-card .leave-card-inner .leave-icon svg path,
.crm-dash-card .crm-card-inner .crm-icon svg path,
.project-dash-card .project-card-inner .project-icon svg path {
    fill: #fff;
}



.dash-row .info-icon-wrp {
    margin-bottom: 35px;
}

/* crm-dash-card css */
.crm-dash-card .crm-card-inner,
.job-info-card .job-card-inner,
.support-ticket-card .support-card-inner,
.payout-card .payout-card-inner,
.contract-details-wrp .leave-card .leave-card-inner {
    position: relative;
    z-index: 1;
    padding: 15px;
    height: 100%;
    border-radius: 10px;
    background-color: var(--bs-white);
    overflow: hidden;
}

.contract-details-wrp .leave-card .leave-card-inner {
    border: 1px solid var(--bs-border-color);
    -webkit-box-shadow: rgba(149, 157, 165, 0.1) 0px 0px 16px 4px;
    box-shadow: rgba(149, 157, 165, 0.1) 0px 0px 16px 4px;
}

.crm-dash-card .crm-card-inner::before,
.job-info-card .job-card-inner::before,
.support-ticket-card .support-card-inner::before,
.payout-card .payout-card-inner::before {
    height: 55%;
}

.crm-dash-card .crm-card-inner .crm-content {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    text-align: center;
}

.contract-details-wrp .leave-card:nth-child(3n + 1) .leave-card-inner .leave-icon {
    background-color: #6FD943;
}

.contract-details-wrp .leave-card:nth-child(3n + 2) .leave-card-inner .leave-icon {
    background-color: #FFA21D;
}

.contract-details-wrp .leave-card:nth-child(3n + 3) .leave-card-inner .leave-icon {
    background-color: #3EC9D6;
}

.contract-details-wrp .leave-card .leave-card-inner .leave-icon i {
    color: var(--bs-white) !important;
}

/* project-dash-card css */
.project-dash-card .project-card-inner .bg-svg {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    z-index: -1;
    opacity: 0.1;
}

.project-dash-card .project-right span {
    padding: 8px 10px;
    background-color: #fff;
    border-radius: 5px;
    line-height: 1;
    font-weight: 700;
}

.project-dash-card .project-right p {
    color: #060606;
    font-weight: 600;
}

.project-dash-card:nth-child(3n + 1) .project-right span {
    color: #FF3A6E;
}

.project-dash-card:nth-child(3n + 2) .project-right span {
    color: #0CAF60;
}

.project-dash-card:nth-child(3n + 3) .project-right span {
    color: #FFA21D;
}

.project-dash-card .project-card-inner .project-left h2 {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

/* pos-dash-card css */
.pos-dash-card .pos-card-inner {
    position: relative;
    z-index: 1;
    padding: 15px;
    overflow: hidden;
    height: 100%;
    border-radius: 10px;
}

.flex-1 {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

.pos-price {
    text-transform: capitalize;
    font-weight: 600;
}

.pos-price h3 {
    font-size: 18px;
    line-height: 1;
}

/* support-card-css */

.project-card-inner .star-bg {
    position: absolute;
    z-index: -1;
    top: 0;
    left: 35px;
    height: 60px;
    width: 60px;
}

.contact-item span{
    color: #000000;
}

/* warehouse-card css */
/* tooltip css */
.tooltip {
    z-index: 1000;
}

/* new dropdown css */
.dropdown-menu {
    padding: 7px 0 !important;
    overflow: hidden;
}

.dropdown-menu .dropdown-item {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    cursor: pointer;
}

.dropdown-menu .dropdown-item i {
    font-size: 18px;
}

/* schedule card css */
.schedule-wrp .schedule-item,
.schedule-wrp .schedule-item .schedule-item-right,
.event-wrp .event-item {
    gap: 12px;
}

.schedule-wrp .schedule-item .action-btns .btn,
.event-wrp .event-item .action-btns .btn {
    padding: 7px;

}

.schedule-wrp .schedule-item .date {
    color: #060606;
}

/* btn-group-colors css */
.btn-group-colors.event-tag>label {
    border: 1px solid transparent;
}

.btn-group-colors.event-tag>label:has(input:checked) {
    border-color: #060606;
}

/* event-card css */
.event-wrp .event-item .date-wrp {
    gap: 5px 10px;
}

.event-wrp .event-item .date-wrp .date {
    font-size: 13px;
}

.event-wrp .event-item .date-wrp .date:not(:last-of-type) {
    position: relative;
    padding: 0 12px 0 0;
}

.event-wrp .event-item .date-wrp .date:not(:last-of-type)::before,
.client-card .project-info-wrp .project-info:not(:last-of-type)::before,
.support-user-card .project-info-wrp .project-info:not(:last-of-type)::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 2px;
    border-radius: 10px;
    background: -o-radial-gradient(var(--theme-color) 0%, #ffffff 100%, var(--theme-color) 0%);
    background: radial-gradient(var(--theme-color) 0%, #ffffff 100%, var(--theme-color) 0%);
}

/* bill-timeline-card css */
.bill-timeline-card {
    position: relative;
    z-index: 1;
    padding: 25px;
    background: var(--bs-white);
    -webkit-box-shadow: 0 6px 30px rgba(182, 186, 203, 0.3);
    box-shadow: 0 6px 30px rgba(182, 186, 203, 0.3);
    border-radius: 10px;
}

.timeline-wrapper {
    padding: 0;
}

.bill-timeline-card .invoice {
    position: relative;
    z-index: 1;
}

.bill-timeline-card .bill-timeline-inner .timeline-icon {
    height: 35px;
    width: 35px;
    border-radius: 50%;
    background-color: var(--bs-white);
    border: 1px solid #f1f1f1;
}

.user-card .user-count-wrp .user-count:nth-child(3n + 1) .user-icon,
.user-card .date-wrp .date .date-icon,
.client-card .date-wrp .date .date-icon {
    background-color: #0CAF60;
}

.user-card .user-count-wrp .user-count:nth-child(3n + 2) .user-icon {
    background-color: #ffa21d;
}

.user-card .user-count-wrp .user-count:nth-child(3n + 3) .user-icon,
.user-card .date-wrp .time .time-icon,
.client-card .date-wrp .time .time-icon {
    background-color: #0CAF60;
}

.bill-timeline-card .bill-timeline-inner .timeline-icon svg {
    height: 18px;
    width: 18px;
}

.bill-timeline-card .bill-timeline-inner .timeline-icon svg path {
    fill: #fff;
}

.bill-timeline-card .create-invoice .timeline-icon svg path {
    fill: #6FD943;
}

.bill-timeline-card .send-invoice .timeline-icon svg path {
    fill: #FFA21D;
}

.bill-timeline-card .get-paid .timeline-icon svg path {
    fill: #3EC9D6;
}

.bill-timeline-card .create-invoice .timeline-content h5 {
    color: #6FD943;
}

.bill-timeline-card .send-invoice .timeline-content h5 {
    color: #FFA21D;
}

.bill-timeline-card .get-paid .timeline-content h5 {
    color: #3EC9D6;
}

.bill-timeline-card .timeline-content .btn {
    border: 1px solid #ced4da;
}

.bill-timeline-card .timeline-content .btn i {
    font-size: 16px;
}

.timeline-wrapper .progress {
    background: var(--bs-border-color);
    border-radius: 50px;
    position: relative;
    height: 8px;
    width: 100%;
}

.timeline-wrapper .progress-value {
    background-color: #6FD943;
    -webkit-animation: invoice-load 3s normal forwards;
    animation: invoice-load 3s normal forwards;
    -webkit-box-shadow: 0 10px 40px -10px #fff;
    box-shadow: 0 10px 40px -10px #fff;
    border-radius: 50px;
    height: 8px;
    width: 0;
}

@keyframes invoice-load {
    0% {
        width: 0;
    }

    100% {
        width: 100%;
    }
}

/* Modern Kanban Card Design */

.crm-sales-card .card-header,
.crm-sales-card .sales-item-wrp {
    padding: 12px;
}

.crm-sales-card {
    background-color: #fff;
    border: 1px solid #e1e5e9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border-radius: 12px;
    width: 300px;
    transition: all 0.2s ease;
}

.crm-sales-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border-color: #c1c7d0;
}

.crm-sales-card .card-header {
    background: #fafbfc;
    border-bottom: 1px solid #f4f5f7;
    border-radius: 12px 12px 0 0;
    padding: 16px;
}

.crm-sales-card .card-header h4 {
    color: #172b4d;
    font-weight: 600;
    font-size: 14px;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.crm-sales-card .card-header > span {
    background: rgba(var(--theme-color-rgb), 0.1);
    color: var(--theme-color);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    border: none;
    line-height: 1;
}

/* Modern Task Card */
.crm-sales-card .sales-item {
    background-color: #fff;
    border: 1px solid #f4f5f7;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.crm-sales-card .sales-item:hover {
    border-color: #c1c7d0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
}

.crm-sales-card .sales-item:not(:last-of-type) {
    margin: 0 0 8px;
}

.crm-sales-card .sales-item .sales-item-top {
    padding: 12px;
    border-bottom: 1px solid #f8f9fa;
}

.crm-sales-card .sales-item .sales-item-body {
    padding: 12px;
}

.crm-sales-card .sales-item .sales-item-top h5 {
    font-size: 14px;
    font-weight: 600;
    color: #172b4d;
    margin: 0;
    line-height: 1.3;
}

.crm-sales-card .sales-item .sales-item-top h5 a {
    color: #172b4d;
    text-decoration: none;
    transition: color 0.2s ease;
}

.crm-sales-card .sales-item .sales-item-top h5 a:hover {
    color: var(--theme-color);
}

/* Priority and Status Badges */
.crm-sales-card .sales-item .badge {
    font-size: 10px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.crm-sales-card .sales-item .badge.bg-danger {
    background: #ffebe6 !important;
    color: #bf2600 !important;
    border: 1px solid #ffbdad;
}

.crm-sales-card .sales-item .badge.bg-warning {
    background: #fff4e6 !important;
    color: #974f0c !important;
    border: 1px solid #ffd591;
}

.crm-sales-card .sales-item .badge.bg-primary {
    background: #e6f2ff !important;
    color: #0052cc !important;
    border: 1px solid #b3d4ff;
}

.crm-sales-card .sales-item .badge.bg-info {
    background: #e3fcef !important;
    color: #006644 !important;
    border: 1px solid #abf5d1;
}

.crm-sales-card .sales-item .badge.bg-secondary {
    background: #f4f5f7 !important;
    color: #5e6c84 !important;
    border: 1px solid #dfe1e6;
}

.dash-content {
    gap: 2px;
}

/* Modern Task Card Specific Styles */
.modern-task-card {
    position: relative;
}

.modern-task-card .task-title a {
    font-weight: 600;
    line-height: 1.3;
    word-break: break-word;
}

.modern-task-card .task-menu {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.modern-task-card:hover .task-menu {
    opacity: 1;
}

.modern-task-card .task-menu .btn-ghost {
    background: transparent;
    border: none;
    color: #5e6c84;
    border-radius: 4px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modern-task-card .task-menu .btn-ghost:hover {
    background: #f4f5f7;
    color: #172b4d;
}

.modern-task-card .task-badges {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

.modern-task-card .task-badges .badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.modern-task-card .task-badges .badge i {
    font-size: 8px;
}

.modern-task-card .task-description {
    font-size: 13px;
    color: #5e6c84;
    line-height: 1.4;
    margin-bottom: 12px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.modern-task-card .task-meta {
    border-top: 1px solid #f4f5f7;
    padding-top: 8px;
    margin-top: 8px;
}

.modern-task-card .task-assignees .kanban-card-avatar {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-left: -2px;
    border: 2px solid white;
    transition: all 0.2s ease;
}

.modern-task-card .task-assignees .kanban-card-avatar:first-child {
    margin-left: 0;
}

.modern-task-card .task-assignees .kanban-card-avatar:hover {
    transform: scale(1.1);
    z-index: 2;
}

.modern-task-card .more-assignees {
    font-size: 10px;
    color: #5e6c84;
    margin-left: 4px;
    font-weight: 500;
}

.modern-task-card .task-stats {
    display: flex;
    gap: 8px;
}

.modern-task-card .stat-item {
    display: inline-flex;
    align-items: center;
    gap: 2px;
    font-size: 11px;
    color: #5e6c84;
    font-weight: 500;
}

.modern-task-card .stat-item i {
    font-size: 12px;
}

.modern-task-card .task-due-date {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.modern-task-card .task-due-date small {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    color: #5e6c84;
}

.modern-task-card .task-due-date i {
    font-size: 12px;
}

/* Dropdown Menu Styling */
.modern-task-card .dropdown-menu {
    border: 1px solid #e1e5e9;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    padding: 4px 0;
    min-width: 140px;
}

.modern-task-card .dropdown-item {
    padding: 6px 12px;
    font-size: 12px;
    color: #172b4d;
    display: flex;
    align-items: center;
    gap: 8px;
}

.modern-task-card .dropdown-item:hover {
    background: #f4f5f7;
    color: #172b4d;
}

.modern-task-card .dropdown-item.text-danger:hover {
    background: #ffebe6;
    color: #bf2600;
}

.modern-task-card .dropdown-item i {
    font-size: 12px;
    width: 12px;
}

/* Responsive adjustments for task cards */
@media (max-width: 768px) {
    .modern-task-card .task-assignees .kanban-card-avatar {
        width: 18px;
        height: 18px;
    }

    .modern-task-card .task-stats {
        gap: 6px;
    }

    .modern-task-card .stat-item {
        font-size: 10px;
    }
}

/* Animation for drag and drop */
.modern-task-card.gu-mirror {
    transform: rotate(2deg);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    border-color: var(--theme-color);
}

.modern-task-card.gu-transit {
    opacity: 0.6;
}

/* Hover state for better interactivity */
.modern-task-card:hover {
    border-color: #c1c7d0;
}

.modern-task-card:hover .task-title a {
    color: var(--theme-color);
}

/* Loading state */
.modern-task-card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.modern-task-card.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Better spacing for kanban columns */
.crm-sales-card .sales-item-wrp {
    min-height: 200px;
    padding: 8px;
    transform: translateZ(0); /* Force hardware acceleration */
}

/* Optimized kanban wrapper for smooth scrolling */
.kanban-wrapper.horizontal-scroll-cards {
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    transform: translateZ(0); /* Force hardware acceleration */
}

.kanban-wrapper.horizontal-scroll-cards .kanban-col {
    display: inline-block;
    vertical-align: top;
    white-space: normal;
    width: 300px;
    margin-right: 16px;
    transform: translateZ(0); /* Force hardware acceleration */
}

/* Smooth scrollbar styling */
.kanban-wrapper.horizontal-scroll-cards::-webkit-scrollbar {
    height: 8px;
}

.kanban-wrapper.horizontal-scroll-cards::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.kanban-wrapper.horizontal-scroll-cards::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.kanban-wrapper.horizontal-scroll-cards::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

/* Improved column headers */
.crm-sales-card .card-header .btn-light-primary {
    background: rgba(var(--theme-color-rgb), 0.1);
    border-color: rgba(var(--theme-color-rgb), 0.2);
    color: var(--theme-color);
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.crm-sales-card .card-header .btn-light-primary:hover {
    background: rgba(var(--theme-color-rgb), 0.15);
    border-color: rgba(var(--theme-color-rgb), 0.3);
    transform: translateY(-1px);
}

/* Empty column state */
.sales-item-wrp:empty::after {
    content: 'Drop tasks here';
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100px;
    color: #a5adba;
    font-size: 13px;
    border: 2px dashed #e1e5e9;
    border-radius: 8px;
    margin: 8px 0;
}

.sales-item-wrp:not(:empty)::after {
    display: none;
}

/* Minimalistic Task Card Design */
.minimal-task-card {
    background: #ffffff;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
}

.minimal-task-card:hover {
    border-color: #dadce0;
    box-shadow: 0 1px 6px rgba(32, 33, 36, 0.08);
    transform: translateY(-1px);
}

/* Task Content */
.minimal-task-card .task-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* Task Header */
.minimal-task-card .task-header-minimal {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: 8px;
}

.minimal-task-card .task-title-row {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    flex: 1;
    min-width: 0;
}

.minimal-task-card .task-title-minimal {
    font-size: 14px;
    font-weight: 500;
    color: #202124;
    margin: 0;
    line-height: 1.3;
    flex: 1;
    min-width: 0;
}

.minimal-task-card .task-title-minimal a {
    color: #202124;
    text-decoration: none;
    display: block;
    word-break: break-word;
}

.minimal-task-card .task-title-minimal a:hover {
    color: #1a73e8;
}

/* Priority Badge */
.minimal-task-card .priority-badge-minimal {
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
    flex-shrink: 0;
}

.minimal-task-card .priority-badge-minimal.priority-critical {
    background: #fce8e6;
    color: #d93025;
}

.minimal-task-card .priority-badge-minimal.priority-high {
    background: #fef7e0;
    color: #f9ab00;
}

.minimal-task-card .priority-badge-minimal.priority-medium {
    background: #e8f0fe;
    color: #1a73e8;
}

.minimal-task-card .priority-badge-minimal.priority-low {
    background: #e6f4ea;
    color: #137333;
}

/* Task Menu */
.minimal-task-card .task-menu-minimal {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.minimal-task-card:hover .task-menu-minimal {
    opacity: 1;
}

.minimal-task-card .btn-menu-minimal {
    background: none;
    border: none;
    color: #5f6368;
    padding: 2px;
    border-radius: 4px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.minimal-task-card .btn-menu-minimal:hover {
    background: #f1f3f4;
    color: #202124;
}

/* Task Description */
.minimal-task-card .task-description-minimal {
    font-size: 12px;
    color: #5f6368;
    line-height: 1.4;
    margin: 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Task Footer */
.minimal-task-card .task-footer-minimal {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
}

.minimal-task-card .task-footer-left {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 0;
}

/* Assignees */
.minimal-task-card .assignees-minimal {
    display: flex;
    align-items: center;
    gap: -2px;
}

.minimal-task-card .avatar-minimal {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #ffffff;
    margin-left: -2px;
    transition: all 0.2s ease;
}

.minimal-task-card .avatar-minimal:first-child {
    margin-left: 0;
}

.minimal-task-card .avatar-minimal:hover {
    transform: scale(1.1);
    z-index: 2;
}

.minimal-task-card .more-count {
    font-size: 10px;
    color: #5f6368;
    margin-left: 4px;
    font-weight: 500;
}

/* Stats */
.minimal-task-card .stats-minimal {
    display: flex;
    align-items: center;
    gap: 8px;
}

.minimal-task-card .stat-minimal {
    display: flex;
    align-items: center;
    gap: 2px;
    font-size: 11px;
    color: #5f6368;
    font-weight: 400;
}

.minimal-task-card .stat-minimal i {
    font-size: 12px;
}

/* Due Date */
.minimal-task-card .due-date-minimal {
    flex-shrink: 0;
}

.minimal-task-card .date-text {
    font-size: 11px;
    color: #5f6368;
    font-weight: 400;
}

/* Dropdown Menu */
.minimal-task-card .dropdown-menu {
    border: 1px solid #dadce0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 4px 0;
    min-width: 120px;
}

.minimal-task-card .dropdown-item {
    padding: 6px 12px;
    font-size: 12px;
    color: #202124;
    display: flex;
    align-items: center;
    gap: 8px;
}

.minimal-task-card .dropdown-item:hover {
    background: #f8f9fa;
    color: #202124;
}

.minimal-task-card .dropdown-item.text-danger:hover {
    background: #fce8e6;
    color: #d93025;
}

.minimal-task-card .dropdown-item i {
    font-size: 12px;
    width: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .minimal-task-card {
        padding: 10px;
    }

    .minimal-task-card .task-title-minimal {
        font-size: 13px;
    }

    .minimal-task-card .avatar-minimal {
        width: 18px;
        height: 18px;
    }

    .minimal-task-card .stats-minimal {
        gap: 6px;
    }

    .minimal-task-card .stat-minimal {
        font-size: 10px;
    }
}

/* Animation for new cards */
@keyframes slideInCard {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.minimal-task-card {
    animation: slideInCard 0.3s ease-out;
}

/* Drag and drop states */
.minimal-task-card.gu-mirror {
    transform: rotate(1deg);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    border-color: #1a73e8;
}

.minimal-task-card.gu-transit {
    opacity: 0.6;
}
/* deals-card css */
.deals-card .deals-card-inner .top-svg {
    position: absolute;
    z-index: -1;
    opacity: 0.3;
    top: 0;
    left: 0;
}
/* note-editor css */
.note-editor.note-frame {
    overflow: hidden;
}
.note-editor.card .card-header {
    background-color: #fff;
}
/* task-item css */
.task-item-wrp .task-item:not(:last-of-type),
.event-wrp .event-item:not(:last-of-type),
.schedule-wrp .schedule-item:not(:last-of-type) {
    margin: 0 0 15px;
}



.task-item-wrp,
.schedule-wrp,
.event-wrp {
    padding: 0 0 0 15px;
    border-left: 1px dashed var(--theme-color);
}

.task-item-wrp .task-item,
.event-wrp .event-item,
.schedule-wrp .schedule-item {
    position: relative;
    padding: 12px;
    background-color: var(--bs-white);
    border: 1px solid rgba(0, 0, 0, 0.06);
    -webkit-box-shadow: 0px 4px 17px 0px rgba(0, 0, 0, 0.05);
    box-shadow: 0px 4px 17px 0px rgba(0, 0, 0, 0.05);
    border-radius: 10px;
}

.task-item-wrp .task-item .task-item-icon {
    background-color: var(--theme-color);
    height: 35px;
    width: 35px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding: 5px;
    border-radius: 4px;
}

.task-item-wrp .task-item::before,
.event-wrp .event-item::before,
.schedule-wrp .schedule-item::before {
    content: '';
    position: absolute;
    background-color: var(--theme-color);
    top: 21px;
    left: -21px;
    height: 10px;
    width: 10px;
    border-radius: 50%;
}


/* user-card css */
.user-card,
.client-card,
.support-user-card {
    padding: 15px;
    border-radius: 10px;
    background-color: #fff;
    -webkit-box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
    box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
}
.user-card .user-image,
.client-card .client-image,
.support-user-card .user-image {
    height: 60px;
    width: 60px;
    overflow: hidden;
}
.user-card .user-image img,
.client-card .client-image img,
.support-user-card .user-image img {
    -o-object-fit: cover;
    object-fit: cover;
}
.user-card .user-count-wrp .user-count .user-icon,
.user-card .date-wrp .date .date-icon,
.user-card .date-wrp .time .time-icon,
.client-card .date-wrp .date .date-icon,
.client-card .date-wrp .time .time-icon {
    height: 25px;
    width: 25px;
    border-radius: 4px;
}
/* client-card css */
.client-card .project-info-wrp .project-info,
.support-user-card .project-info-wrp .project-info {
    position: relative;
}
.client-card .project-info-wrp .project-info:not(:last-of-type)::before,
.support-user-card .project-info-wrp .project-info:not(:last-of-type)::before {
    right: -0.5rem;
}
/* support-user-card css */
.support-user-card .date-wrp .date .date-icon {
    height: 31px;
    width: 31px;
    border-radius: 4px;
    background-color: var(--theme-color);
}
/* super admin dashboard-info-card css */
.dash-info-card .icon-wrp {
    position: relative;
    z-index: 1;
}
.dash-info-card .icon-wrp::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    z-index: -1;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}
.dash-info-card:nth-child(4n + 1) .icon-wrp::before {
    border: 1px dashed #FF3A6E;
}
.dash-info-card:nth-child(4n + 2) .icon-wrp::before {
    border: 1px dashed #6FD943;
}
.dash-info-card:nth-child(4n + 3) .icon-wrp::before {
    border: 1px dashed #51459D;
}
.dash-info-card:nth-child(4n + 4) .icon-wrp::before {
    border: 1px dashed #FFA21D;
}
.dash-info-card .icon-wrp .card-label {
    padding: 10px 15px;
    line-height: 1;
    color: #fff;
    border-radius: 5px;
    max-width: -webkit-fit-content;
    max-width: -moz-fit-content;
    max-width: fit-content;
}
.dash-info-card.super-admin .info-card-inner::after {
    display: none;
}





.support-ticket-card:nth-child(4n + 1) .support-card-inner .bottom-svg path,
.crm-dash-card:nth-child(4n + 1) .crm-card-inner .bottom-svg path,
.payout-card:nth-child(2n + 1) .payout-card-inner .bottom-svg path,
.deals-card:nth-child(4n + 1) .deals-card-inner .bottom-svg path,
.job-info-card:nth-child(3n + 1) .job-card-inner .bottom-svg path,
.leave-card:nth-child(4n + 1) .leave-card-inner .bottom-svg path,
.project-dash-card:nth-child(3n + 1) .project-card-inner .bottom-svg path {
    fill: #FF3A6E;
}

.support-ticket-card:nth-child(4n + 2) .support-card-inner .bottom-svg path,
.crm-dash-card:nth-child(4n + 2) .crm-card-inner .bottom-svg path,
.payout-card:nth-child(2n + 2) .payout-card-inner .bottom-svg path,
.deals-card:nth-child(4n + 2) .deals-card-inner .bottom-svg path,
.job-info-card:nth-child(3n + 2) .job-card-inner .bottom-svg path,
.leave-card:nth-child(4n + 2) .leave-card-inner .bottom-svg path,
.project-dash-card:nth-child(3n + 2) .project-card-inner .bottom-svg path {
    fill: #0CAF60;
}

.support-ticket-card:nth-child(4n + 3) .support-card-inner .bottom-svg path,
.crm-dash-card:nth-child(4n + 3) .crm-card-inner .bottom-svg path,
.deals-card:nth-child(4n + 3) .deals-card-inner .bottom-svg path,
.job-info-card:nth-child(3n + 3) .job-card-inner .bottom-svg path,
.leave-card:nth-child(4n + 3) .leave-card-inner .bottom-svg path,
.project-dash-card:nth-child(3n + 3) .project-card-inner .bottom-svg path {
    fill: #FFA21D;
}

.support-ticket-card:nth-child(4n + 4) .support-card-inner .bottom-svg path,
.crm-dash-card:nth-child(4n + 4) .crm-card-inner .bottom-svg path,
.deals-card:nth-child(4n + 4) .deals-card-inner .bottom-svg path,
.leave-card:nth-child(4n + 4) .leave-card-inner .bottom-svg path {
    fill: #3EC9D6;
}


.support-ticket-card .bottom-svg,
.crm-dash-card .bottom-svg,
.payout-card .bottom-svg,
.deals-card .bottom-svg,
.job-info-card .bottom-svg,
.leave-card .bottom-svg,
.project-card-inner .bottom-svg,
.warehouse-card-inner .bottom-svg {
    bottom: -22px;
    height: 75px;
    width: 75px;
}

.leave-card .leave-card-inner .leave-icon i {
    color: var(--bs-white);
}


/* setting page css */
.setting-sidebar {
    max-height: 100vh !important;
    overflow-y: auto;
}
/* table css */
.dataTable-top {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    gap: 15px;
}
.card:not(.table-card) .dataTable-bottom {
    padding-bottom: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    gap: 10px;
}
.dataTable-top::after,
.dataTable-bottom::after {
    display: none;
}
.dataTable-bottom .dataTable-info {
    margin: 0;
}
.dataTable-pagination .dataTable-pagination-list {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    gap: 10px;
}
.dataTable-pagination .dataTable-pagination-list a {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    color: #000 !important;
    border-color: #dee2e6;
    font-size: 14px;
    height: 35px;
    width: 35px;
    padding: 0;
    border-radius: 4px;
    margin: 0;
}
.dataTable-pagination li.active a,
.dataTable-pagination li a:focus,
.dataTable-pagination li a:hover,
.dataTable-pagination li.active a:focus,
.dataTable-pagination li.active a:hover {
    background-color: var(--theme-color);
    border-color: var(--theme-color);
    color: #fff !important;
}
#useradd-sidenav .list-group-item i {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
}
.card .progress .progress-bar {
    -webkit-box-shadow: none;
    box-shadow: none;
}
@media screen and (max-width: 767px) {
    /* dash-info-card css */
    .dash-info-card .info-card-inner .info-icon,
    .project-dash-card .project-card-inner .project-icon,
    .pos-dash-card .pos-card-inner .pos-icon,
    .warehouse-card .warehouse-card-inner .warehouse-icon,
    .job-info-card .job-card-inner .job-icon,
    .deals-card .deals-card-inner .deals-icon,
    .support-ticket-card .support-card-inner .support-icon,
    .payout-card .payout-card-inner .payout-icon {
        height: 40px;
        width: 40px;
        padding: 5px;
    }
    .dash-info-card .info-card-inner .info-icon svg,
    .project-dash-card .project-card-inner .project-icon svg,
    .pos-dash-card .pos-card-inner .pos-icon svg,
    .warehouse-card .warehouse-card-inner .warehouse-icon svg,
    .job-info-card .job-card-inner .job-icon svg,
    .deals-card .deals-card-inner .deals-icon svg,
    .support-ticket-card .support-card-inner .support-icon svg,
    .payout-card .payout-card-inner .payout-icon svg {
        height: 20px;
        width: 20px;
    }
    .dash-info-card .info-card-inner .info-content h3,
    .pos-dash-card .pos-card-inner .pos-price h4,
    .warehouse-card .warehouse-card-inner h4,
    .warehouse-card .warehouse-card-inner .h4 {
        font-size: 18px;
    }
    /* leave-card css */
    .leave-card {
        height: auto;
    }
    /* card css */
    .card-header,
    .card-body,
    .card-footer {
        padding: 20px 15px;
    }
    .card .card-header:not(.border-0) h5:after,
    .card .card-header:not(.border-0) .h5:after {
        left: -15px;
    }
    .card:not(.table-card) .table-responsive {
        margin: -20px -15px 0 -15px;
        width: calc(100% + 30px);
    }
    .card:not(.table-card) .dataTable-bottom,
    .card:not(.table-card) .dataTable-top {
        padding: 20px 15px;
    }
    .card:not(.table-card) .table tr td:first-child,
    .card:not(.table-card) .table tr th:first-child {
        padding-left: 15px;
    }
    /* crm-sales-card css */
    .crm-sales-card .card-header,
    .crm-sales-card .sales-item-wrp {
        padding: 15px;
    }
    .product-body-nop .card {
        padding: 10px;
    }
}
@media screen and (max-width: 575px) {
    /* income-card css */
    .income-card .income-card-inner::before {
        display: none;
    }
    .income-card .income-card-inner {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        gap: 20px;
    }
    .income-card .income-card-inner .income-card-left,
    .income-card .income-card-inner .income-card-right {
        max-width: 100%;
    }
    .income-card .income-info h4 {
        font-size: 16px;
    }
    /* leave-card css */
    .leave-card .leave-card-inner .leave-icon {
        height: 45px;
        width: 45px;
        padding: 5px;
    }
    .leave-card .leave-card-inner .leave-icon svg {
        height: 24px;
        width: 24px;
    }
      /* dash-info-card-css */
      .dash-info-card .info-card-inner .card-label {
        font-size: 12px;
    }
}
#cache-settings .input-group-text {
    border-radius: 0 6px 6px 0;
}
[dir="rtl"] #cache-settings .input-group-text {
    border-radius: 6px 0 0 6px !important;
}
/* 03.11 copy payslip */
.payslip-number {
    max-width: 170px;
    width: 100%;
}
/* 03.11 copy payslip */
/* Tooltip radius  */
.tooltip-inner {
    border-radius: 4px !important;
    text-transform: capitalize;
}
/* messenger scrollbar */
.messenger-tab {
    max-height: 680px;
    height: 100% !important;
}



/* client-side-css */
.total-client-card .client-card-content p {
    color: #060606;
}

.total-client-card:nth-child(3n + 1) .progress-bar {
    background: #6FD943 !important;
}

.total-client-card:nth-child(3n + 2) .progress-bar {
    background: #FFA21D !important;
}

.total-client-card:nth-child(3n + 3) .progress-bar {
    background: #3EC9D6 !important;
}

.total-client-card:nth-child(3n + 1) .client-card-content h3 {
    color: #6FD943;
}

.total-client-card:nth-child(3n + 2) .client-card-content h3 {
    color: #FFA21D;
}

.total-client-card:nth-child(3n + 3) .client-card-content h3 {
    color: #3EC9D6;
}

/* For progress background */
.total-client-card:nth-child(3n + 1) .progress {
    background-color: rgba(111, 217, 67, 0.16);
}

.total-client-card:nth-child(3n + 2) .progress {
    background-color: rgba(255, 162, 29, 0.16);
}

.total-client-card:nth-child(3n + 3) .progress {
    background-color: rgba(62, 201, 214, 0.16);
}


.client-card-wrp .card-body {
    padding: 15px;
}

/* choose-file-img-css-start */
.choose-file-img {
    width: 60px;
    height: 40px;
    border-radius: 6px;
    overflow: hidden;
}

.choose-file-img img {
    height: 100%;
    width: 100%;
    object-fit: cover;
}

/* choose-file-img-css-end */


.form-label {
    color: #060606;
    font-weight: 600;
}

.form-group .text-xs {
    font-size: 0.700rem !important;
}

.row-gap-1 {
    row-gap: 16px;
}

.row-gap-2 {
    row-gap: 20px;
}

.project-info-inner b,
.info,
.list-group-item .client-name {
    color: #060606;
}

.card .table td,
.card .table th {
    border-top: 0;
}


select {
    text-overflow: ellipsis;
}

.col-form-label {
    padding-top: 0;
}

/* Reduce kanban column width */
.kanban-wrapper .col {
    flex: 0 0 auto;
    width: 25% !important; /* Adjust this percentage as needed */
    padding-left: 5px;
    padding-right: 5px;
}

/* Kanban Card Avatar Styling - Keep Original Design */
.kanban-card-avatar {
    transition: all 0.2s ease;
    z-index: 2;
}

.kanban-card-avatar:hover {
    transform: scale(1.1);
    z-index: 3;
}

/* Ensure kanban cards are not affected by task detail styles */
.sales-item .user-avatar-text {
    /* Reset any task detail styles that might affect kanban cards */
    width: auto;
    height: auto;
    background: transparent;
    color: inherit;
    border-radius: 0;
    display: inline;
    font-size: inherit;
    font-weight: inherit;
    text-transform: none;
}

/* Performance optimizations for kanban cards */
.kanban-card {
    contain: layout style paint;
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

.kanban-card.sales-item {
    margin-bottom: 12px;
    border-radius: 8px;
    background: #fff;
    border: 1px solid #e1e5e9;
    transition: transform 0.15s ease, box-shadow 0.15s ease;
    will-change: transform;
}

/* Optimize dropdown menus during drag */
.sales-item .dropdown-menu {
    will-change: auto;
    transform: translateZ(0);
}

/* Prevent layout shifts during drag */
.sales-item-wrp {
    contain: layout;
}

/* Delete File Modal Styling */
#deleteFileModal .modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
}

#deleteFileModal .modal-header {
    background: transparent;
    padding: 24px 24px 0;
}

#deleteFileModal .modal-body {
    padding: 0 24px;
}

#deleteFileModal .modal-footer {
    padding: 0 24px 24px;
    justify-content: center;
    gap: 12px;
}

#deleteFileModal .avatar {
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#deleteFileModal .bg-danger-light {
    background-color: rgba(220, 53, 69, 0.1);
    border: 2px solid rgba(220, 53, 69, 0.2);
}

#deleteFileModal .btn {
    min-width: 100px;
    border-radius: 8px;
    font-weight: 500;
    padding: 8px 20px;
}

#deleteFileModal .btn-light {
    background-color: #f8f9fa;
    border-color: #e9ecef;
    color: #6c757d;
}

#deleteFileModal .btn-light:hover {
    background-color: #e9ecef;
    border-color: #dee2e6;
    color: #495057;
}

#deleteFileModal .btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    transition: all 0.2s ease;
}

#deleteFileModal .btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
    transform: translateY(-1px);
}

#deleteFileModal .btn-danger:disabled {
    opacity: 0.7;
    transform: none;
}

#deleteFileModal .modal-title {
    font-weight: 600;
    color: #495057;
}

#deleteFileModal #deleteFileName {
    color: var(--theme-color);
}

/* Enhanced Drag and Drop Styles */
.draggable-item {
    cursor: grab;
    transition: transform 0.15s ease, box-shadow 0.15s ease;
    will-change: transform;
}

.draggable-item:active {
    cursor: grabbing;
}

.draggable-item.gu-mirror {
    cursor: grabbing;
    transform: rotate(2deg) scale(1.02);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.25);
    z-index: 9999;
    opacity: 0.95;
    border-radius: 8px;
    will-change: transform;
}

.draggable-item.gu-transit {
    opacity: 0.4;
    transform: scale(0.98);
    transition: opacity 0.15s ease, transform 0.15s ease;
}

/* Optimized styles for leads kanban */
.sales-item {
    cursor: grab;
    transition: transform 0.15s ease, box-shadow 0.15s ease, opacity 0.15s ease;
    will-change: transform;
    transform: translateZ(0); /* Force hardware acceleration */
}

.sales-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.sales-item.gu-mirror {
    cursor: grabbing;
    transform: rotate(1deg) scale(1.02) translateZ(0);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    z-index: 9999;
    opacity: 0.95;
    border-radius: 8px;
}

.sales-item.gu-transit-optimized {
    opacity: 0.5;
    transform: scale(0.98) translateZ(0);
    transition: opacity 0.15s ease, transform 0.15s ease;
}

.sales-item.updating-lead {
    opacity: 0.7;
    pointer-events: none;
    position: relative;
}

.sales-item.updating-lead::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--theme-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 10;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Body state during dragging */
body.dragging-leads {
    cursor: grabbing;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

body.dragging-leads * {
    cursor: grabbing !important;
}

/* Kanban box drop zone styling */
.kanban-box,
.sales-item-wrp {
    min-height: 100px;
    transition: background-color 0.2s ease, border-color 0.2s ease;
    border-radius: 8px;
}

.sales-item-wrp.drag-over-highlight {
    background-color: rgba(var(--theme-color-rgb), 0.08);
    border: 2px dashed var(--theme-color);
    border-radius: 8px;
    animation: pulse-highlight 1s ease-in-out infinite alternate;
}

@keyframes pulse-highlight {
    0% { background-color: rgba(var(--theme-color-rgb), 0.05); }
    100% { background-color: rgba(var(--theme-color-rgb), 0.12); }
}

.kanban-box.gu-over {
    background-color: rgba(var(--theme-color-rgb), 0.05);
    border: 2px dashed var(--theme-color);
    border-radius: 8px;
}

/* Prevent text selection during drag */
.dragging-leads .sales-item *,
.dragging-task .sales-item * {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* Improve clickable elements inside draggable items */
.draggable-item a,
.draggable-item button,
.draggable-item .btn,
.sales-item a,
.sales-item button,
.sales-item .btn {
    position: relative;
    z-index: 10;
    pointer-events: auto;
}

/* Empty kanban box state */
.kanban-box:empty::before {
    content: 'Drop tasks here';
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
    color: #8c8c8c;
    font-size: 14px;
    border: 2px dashed #e0e0e0;
    border-radius: 8px;
    margin: 8px 0;
    background-color: #fafafa;
}

/* Drag Handle Styling */
.drag-handle {
    cursor: grab;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
    opacity: 0.6;
}

/* File Upload Dropzone Improvements */
.dropzone {
    border: 2px dashed #e1e5e9;
    border-radius: 8px;
    background: #fafbfc;
    transition: all 0.3s ease;
    min-height: 150px;
}

.dropzone:hover {
    border-color: var(--theme-color);
    background: rgba(var(--theme-color-rgb), 0.05);
}

.dropzone.dz-drag-hover {
    border-color: var(--theme-color);
    background: rgba(var(--theme-color-rgb), 0.1);
    transform: scale(1.02);
}

.dropzone .dz-message {
    font-size: 16px;
    color: #6c757d;
    margin: 2em 0;
}

.dropzone .dz-preview {
    margin: 16px;
    min-height: 0;
}

.dropzone .dz-preview.uploading {
    opacity: 0.7;
    transform: scale(0.98);
}

.dropzone .dz-preview .dz-image {
    border-radius: 8px;
    overflow: hidden;
}

.dropzone .dz-preview .dz-details {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 6px;
    padding: 8px;
}

.dropzone .dz-preview .dz-progress {
    height: 4px;
    border-radius: 2px;
    background: #e9ecef;
    overflow: hidden;
}

.dropzone .dz-preview .dz-upload {
    background: var(--theme-color);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.dropzone .dz-preview .dz-error-message {
    background: #dc3545;
    color: white;
    border-radius: 4px;
    padding: 8px;
    font-size: 12px;
}

.dropzone .dz-preview .dz-success-mark,
.dropzone .dz-preview .dz-error-mark {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
}

/* File upload table styling */
#uploaded-files-table {
    margin-top: 20px;
}

#uploaded-files-table .btn {
    padding: 4px 8px;
    font-size: 12px;
}

#uploaded-files-table td {
    vertical-align: middle;
    padding: 12px 8px;
}

#uploaded-files-table .text-break {
    word-break: break-word;
    max-width: 200px;
}

/* Loading animation for file operations */
.file-loading {
    position: relative;
    opacity: 0.6;
    pointer-events: none;
}

.file-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--theme-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 10;
}

.drag-handle:hover {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.05);
}

.drag-handle:active {
    cursor: grabbing;
}

.draggable-item:hover .drag-handle {
    opacity: 1;
}

/* Touch support for mobile devices */
.draggable-item.touch-active {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 100;
}

/* Hide drag handle on mobile for better touch experience */
@media (max-width: 768px) {
    .drag-handle {
        display: none;
    }

    .draggable-item {
        cursor: default;
        touch-action: pan-y;
    }

    .draggable-item:active {
        transform: scale(0.98);
    }
}

/* Modern Task Detail Dialog Styles - Only apply to task detail modal */
.task-detail-modern {
    padding: 24px;
    background: #fafbfc;
    min-height: 600px;
}

/* Task Header */
.task-header {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid #e1e5e9;
}

.task-title {
    font-size: 20px;
    font-weight: 600;
    color: #172b4d;
    line-height: 1.3;
    margin: 0;
}

.task-description {
    font-size: 14px;
    line-height: 1.5;
    color: #5e6c84;
    margin-top: 8px;
}

.priority-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.priority-badge.priority-critical {
    background: #ffebe6;
    color: #bf2600;
    border: 1px solid #ffbdad;
}

.priority-badge.priority-high {
    background: #fff4e6;
    color: #974f0c;
    border: 1px solid #ffd591;
}

.priority-badge.priority-medium {
    background: #e6f2ff;
    color: #0052cc;
    border: 1px solid #b3d4ff;
}

.priority-badge.priority-low {
    background: #e3fcef;
    color: #006644;
    border: 1px solid #abf5d1;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    background: #f4f5f7;
    color: #5e6c84;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid #dfe1e6;
}

/* Task Details Grid */
.task-details-grid {
    margin-bottom: 24px;
}

.detail-card {
    display: flex;
    align-items: center;
    gap: 12px;
    background: white;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
    transition: all 0.2s ease;
}

.detail-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border-color: #c1c7d0;
}

.detail-icon {
    width: 32px;
    height: 32px;
    background: rgba(var(--theme-color-rgb), 0.1);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--theme-color);
    font-size: 16px;
}

.detail-content {
    flex: 1;
    min-width: 0;
}

.detail-label {
    display: block;
    font-size: 12px;
    color: #5e6c84;
    font-weight: 500;
    margin-bottom: 2px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-value {
    display: block;
    font-size: 14px;
    color: #172b4d;
    font-weight: 600;
    word-break: break-word;
}

/* Assignees */
.assignees-list {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-wrap: wrap;
}

/* User avatar text - Only for task detail dialog */
.task-detail-modern .user-avatar-text {
    width: 24px;
    height: 24px;
    background: var(--theme-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    cursor: pointer;
    transition: all 0.2s ease;
}

.task-detail-modern .user-avatar-text:hover {
    transform: scale(1.1);
}

.more-users {
    font-size: 12px;
    color: #5e6c84;
    margin-left: 4px;
}

/* Progress Section */
.progress-section {
    background: white;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
}

.progress-wrapper {
    width: 100%;
}

.progress-bar-container {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.progress-bar-container .progress {
    flex: 1;
    background: #f4f5f7;
    border-radius: 4px;
}

.progress-text {
    font-size: 12px;
    font-weight: 600;
    color: #5e6c84;
    min-width: 35px;
    text-align: right;
}

.task_progress {
    width: 100%;
    height: 6px;
    background: #f4f5f7;
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
}

.task_progress::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    background: var(--theme-color);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.task_progress::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: var(--theme-color);
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Task Sections */
.task-section {
    background: white;
    border-radius: 12px;
    border: 1px solid #e1e5e9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    margin-bottom: 16px;
    overflow: hidden;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #f4f5f7;
    background: #fafbfc;
}

.section-header .d-flex {
    align-items: center;
}

.section-icon {
    font-size: 16px;
    color: var(--theme-color);
}

.section-title {
    font-size: 14px;
    font-weight: 600;
    color: #172b4d;
}

.checklist-progress,
.attachment-count,
.activity-count,
.comment-count {
    background: #f4f5f7;
    color: #5e6c84;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.btn-ghost {
    background: transparent;
    border: 1px solid #dfe1e6;
    color: #5e6c84;
    padding: 4px 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.btn-ghost:hover {
    background: #f4f5f7;
    border-color: #c1c7d0;
    color: #172b4d;
}

.section-content {
    padding: 20px;
}

/* Add Item Forms */
.add-item-form {
    margin-bottom: 16px;
}

.add-item-container {
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

/* Checklist Styles */
.checklist-items {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.checklist-item {
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #f4f5f7;
    transition: all 0.2s ease;
}

.checklist-item:hover {
    background: #f8f9fa;
    border-color: #e9ecef;
}

.checklist-label {
    font-size: 14px;
    color: #172b4d;
    cursor: pointer;
    flex: 1;
    margin: 0;
    line-height: 1.4;
}

.checklist-label.completed {
    text-decoration: line-through;
    color: #5e6c84;
}

.checklist-actions {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.checklist-item:hover .checklist-actions {
    opacity: 1;
}

/* Attachments Styles */
.attachments-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.attachment-item {
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #f4f5f7;
    transition: all 0.2s ease;
}

.attachment-item:hover {
    background: #f8f9fa;
    border-color: #e9ecef;
}

.file-icon {
    width: 32px;
    height: 32px;
    background: rgba(var(--theme-color-rgb), 0.1);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--theme-color);
    font-size: 16px;
}

.file-info {
    min-width: 0;
}

.file-name {
    font-size: 14px;
    color: #172b4d;
    font-weight: 500;
    word-break: break-word;
}

.file-size {
    font-size: 12px;
    color: #5e6c84;
    margin-top: 2px;
}

.file-actions {
    opacity: 0;
    transition: opacity 0.2s ease;
    display: flex;
    gap: 4px;
}

.attachment-item:hover .file-actions {
    opacity: 1;
}

.img_preview {
    border-radius: 6px;
    border: 1px solid #e1e5e9;
}

/* Activity Timeline */
.activity-timeline {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.activity-item {
    display: flex;
    gap: 12px;
    align-items: flex-start;
}

.activity-avatar {
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
    min-width: 0;
}

.activity-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.activity-type {
    font-size: 12px;
    font-weight: 600;
    color: var(--theme-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.activity-time {
    font-size: 11px;
    color: #5e6c84;
}

.activity-description {
    font-size: 13px;
    color: #172b4d;
    line-height: 1.4;
}

.activity-description b {
    font-weight: 600;
    color: var(--theme-color);
}

/* Comments Styles */
.comment-input-section {
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f4f5f7;
}

.comment-input-wrapper {
    position: relative;
}

.comment-form {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.comment-textarea {
    border: 1px solid #dfe1e6;
    border-radius: 8px;
    padding: 12px;
    font-size: 14px;
    line-height: 1.4;
    resize: vertical;
    min-height: 40px;
    transition: all 0.2s ease;
}

.comment-textarea:focus {
    border-color: var(--theme-color);
    box-shadow: 0 0 0 2px rgba(var(--theme-color-rgb), 0.1);
    outline: none;
}

.comment-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.comments-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.comment-item {
    display: flex;
    gap: 12px;
    align-items: flex-start;
}

.comment-content {
    flex: 1;
    min-width: 0;
}

.comment-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.comment-author {
    font-size: 13px;
    font-weight: 600;
    color: #172b4d;
}

.comment-time {
    font-size: 11px;
    color: #5e6c84;
}

.comment-text {
    font-size: 14px;
    color: #172b4d;
    line-height: 1.4;
    word-break: break-word;
}

.comment-actions {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.comment-item:hover .comment-actions {
    opacity: 1;
}

/* Empty States */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px 16px;
    text-align: center;
}

.empty-state i {
    font-size: 32px;
    color: #c1c7d0;
    margin-bottom: 8px;
}

.empty-state p {
    font-size: 13px;
    color: #5e6c84;
}

/* Responsive Design */
@media (max-width: 768px) {
    .task-detail-modern {
        padding: 16px;
    }

    .task-header {
        padding: 16px;
    }

    .task-title {
        font-size: 18px;
    }

    .detail-card {
        padding: 12px;
    }

    .section-content {
        padding: 16px;
    }

    .task-details-grid .row {
        gap: 8px;
    }

    .task-details-grid .col-md-6 {
        margin-bottom: 8px;
    }
}

/* Form Controls */
.form-control-sm {
    padding: 8px 12px;
    font-size: 13px;
    border-radius: 6px;
    border: 1px solid #dfe1e6;
    transition: all 0.2s ease;
}

.form-control-sm:focus {
    border-color: var(--theme-color);
    box-shadow: 0 0 0 2px rgba(var(--theme-color-rgb), 0.1);
    outline: none;
}

/* Button Styles */
.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary {
    background: var(--theme-color);
    border-color: var(--theme-color);
    color: white;
}

.btn-primary:hover {
    background: rgba(var(--theme-color-rgb), 0.9);
    border-color: rgba(var(--theme-color-rgb), 0.9);
    transform: translateY(-1px);
}

/* Modal Adjustments */
.modal-lg .modal-dialog {
    max-width: 900px;
}

.modal-body.task-detail-modern {
    max-height: 80vh;
    overflow-y: auto;
}

/* Scrollbar Styling */
.modal-body.task-detail-modern::-webkit-scrollbar {
    width: 6px;
}

.modal-body.task-detail-modern::-webkit-scrollbar-track {
    background: #f4f5f7;
    border-radius: 3px;
}

.modal-body.task-detail-modern::-webkit-scrollbar-thumb {
    background: #c1c7d0;
    border-radius: 3px;
}

.modal-body.task-detail-modern::-webkit-scrollbar-thumb:hover {
    background: #a5adba;
}

/* Header scroll effect */
.task-header.scrolled {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-bottom: 1px solid #e1e5e9;
}

/* Loading states */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn .ti-loader {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Focus states */
.form-check-input:focus {
    border-color: var(--theme-color);
    box-shadow: 0 0 0 2px rgba(var(--theme-color-rgb), 0.1);
}

.form-check-input:checked {
    background-color: var(--theme-color);
    border-color: var(--theme-color);
}

/* Hover effects */
.detail-card:hover .detail-icon {
    background: rgba(var(--theme-color-rgb), 0.15);
    transform: scale(1.05);
}

.task-detail-modern .user-avatar-text:hover {
    box-shadow: 0 2px 8px rgba(var(--theme-color-rgb), 0.3);
}

/* Task actions dropdown */
.task-actions .dropdown-menu {
    border: 1px solid #e1e5e9;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 8px 0;
}

.task-actions .dropdown-item {
    padding: 8px 16px;
    font-size: 13px;
    color: #172b4d;
    transition: all 0.2s ease;
}

.task-actions .dropdown-item:hover {
    background: #f4f5f7;
    color: #172b4d;
}

.task-actions .dropdown-item.text-danger:hover {
    background: #ffebe6;
    color: #bf2600;
}

/* Modern Task Header Redesign */
.task-header-modern {
    background: var(--bs-body-bg, #ffffff);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
    border: 1px solid var(--bs-border-color, #e2e8f0);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.task-header-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--theme-color) 0%, rgba(var(--theme-color-rgb), 0.6) 100%);
}

.task-header-content {
    position: relative;
    z-index: 1;
}

/* Task Meta Section */
.task-meta-section {
    margin-bottom: 20px;
}

.task-badges-row {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

/* Modern Priority Badge */
.priority-badge-modern {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    cursor: default;
}

.priority-badge-modern:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.priority-badge-modern i {
    font-size: 12px;
}

.priority-badge-modern.priority-critical {
    background: rgba(220, 38, 38, 0.1);
    color: #dc2626;
    border: 1px solid rgba(220, 38, 38, 0.3);
}

.priority-badge-modern.priority-high {
    background: rgba(217, 119, 6, 0.1);
    color: #d97706;
    border: 1px solid rgba(217, 119, 6, 0.3);
}

.priority-badge-modern.priority-medium {
    background: rgba(37, 99, 235, 0.1);
    color: #2563eb;
    border: 1px solid rgba(37, 99, 235, 0.3);
}

.priority-badge-modern.priority-low {
    background: rgba(5, 150, 105, 0.1);
    color: #059669;
    border: 1px solid rgba(5, 150, 105, 0.3);
}

/* Modern Status Badge */
.status-badge-modern {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 14px;
    background: var(--bs-secondary-bg, rgba(108, 117, 125, 0.1));
    color: var(--bs-body-color, #475569);
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid var(--bs-border-color, #cbd5e1);
    transition: all 0.3s ease;
}

.status-badge-modern:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-badge-modern i {
    font-size: 10px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Task ID Badge */
.task-id-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 10px;
    background: var(--bs-tertiary-bg, rgba(108, 117, 125, 0.05));
    color: var(--bs-secondary-color, #64748b);
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    border: 1px solid var(--bs-border-color, #e2e8f0);
    font-family: 'Monaco', 'Menlo', monospace;
}

.task-id-badge i {
    font-size: 10px;
}

/* Task Main Content */
.task-main-content {
    position: relative;
}

.task-title-row {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: 20px;
    margin-bottom: 16px;
}

.task-title-modern {
    font-size: 24px;
    font-weight: 700;
    color: var(--bs-body-color, #1e293b);
    line-height: 1.3;
    margin: 0;
    flex: 1;
    word-break: break-word;
}

/* Task Actions Modern */
.task-actions-modern {
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

.action-buttons-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-action-modern {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    border: 1px solid var(--bs-border-color, #e2e8f0);
    background: var(--bs-body-bg, #ffffff);
    color: var(--bs-secondary-color, #64748b);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.btn-action-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.btn-action-modern:hover::before {
    left: 100%;
}

.btn-action-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--theme-color);
    color: var(--theme-color);
}

.btn-action-modern.btn-edit:hover {
    background: rgba(var(--theme-color-rgb), 0.1);
    color: var(--theme-color);
    border-color: var(--theme-color);
}

.btn-action-modern.btn-delete:hover {
    background: rgba(220, 38, 38, 0.1);
    color: #dc2626;
    border-color: #dc2626;
}



/* Task Description Modern */
.task-description-modern {
    background: var(--bs-tertiary-bg, rgba(108, 117, 125, 0.05));
    border-radius: 12px;
    padding: 16px;
    border-left: 4px solid var(--theme-color);
    position: relative;
}

.task-description-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--theme-color) 0%, rgba(var(--theme-color-rgb), 0.3) 100%);
    border-radius: 0 2px 2px 0;
}

.task-description-modern p {
    margin: 0;
    font-size: 14px;
    line-height: 1.6;
    color: var(--bs-body-color, #475569);
}

/* Responsive Design for Modern Header */
@media (max-width: 768px) {
    .task-header-modern {
        padding: 20px;
    }

    .task-title-row {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }

    .task-title-modern {
        font-size: 20px;
    }

    .task-badges-row {
        gap: 8px;
    }

    .priority-badge-modern,
    .status-badge-modern {
        font-size: 11px;
        padding: 4px 10px;
    }

    .btn-action-modern {
        width: 36px;
        height: 36px;
        font-size: 14px;
    }
}

@media (max-width: 576px) {
    .task-header-modern {
        padding: 16px;
    }

    .task-title-modern {
        font-size: 18px;
    }

    .action-buttons-group {
        gap: 6px;
    }

    .btn-action-modern {
        width: 32px;
        height: 32px;
        font-size: 13px;
    }
}

/* Enhanced Interaction Effects */
.btn-action-modern.hover-effect {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
}

.priority-badge-modern.badge-hover,
.status-badge-modern.badge-hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Loading state for action buttons */
.btn-action-modern.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn-action-modern.loading i {
    animation: spin 1s linear infinite;
}

/* Success state animation */
.btn-action-modern.success {
    background: rgba(5, 150, 105, 0.1);
    color: #059669;
    border-color: #10b981;
}

.btn-action-modern.success i {
    animation: checkmark 0.6s ease-in-out;
}

@keyframes checkmark {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* Improved focus states for accessibility */
.btn-action-modern:focus {
    outline: 2px solid var(--theme-color);
    outline-offset: 2px;
}

.dropdown-item-modern:focus {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    outline: none;
}

/* Dark Mode Support - Uses Bootstrap CSS variables that automatically adapt */
[data-bs-theme="dark"] .task-header-modern,
.dark .task-header-modern {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

[data-bs-theme="dark"] .priority-badge-modern,
.dark .priority-badge-modern {
    background: rgba(var(--theme-color-rgb), 0.2);
    color: var(--theme-color);
    border-color: rgba(var(--theme-color-rgb), 0.4);
}

/* Theme Color Integration */
.task-header-modern:hover {
    box-shadow: 0 6px 25px rgba(var(--theme-color-rgb), 0.1);
}

.btn-action-modern:focus {
    outline: 2px solid var(--theme-color);
    outline-offset: 2px;
}

/* Delete button specific styling */
.btn-action-modern.btn-delete:focus {
    outline: 2px solid #dc2626;
    outline-offset: 2px;
}

/* Improved spacing */
.task-section + .task-section {
    margin-top: 16px;
}

/* Better mobile responsiveness */
@media (max-width: 576px) {
    .task-details-grid .col-md-6 {
        width: 100%;
        margin-bottom: 12px;
    }

    .detail-card {
        padding: 12px;
    }

    .detail-icon {
        width: 28px;
        height: 28px;
        font-size: 14px;
    }

    .section-header {
        padding: 12px 16px;
    }

    .section-content {
        padding: 16px;
    }

    .comment-item,
    .activity-item {
        gap: 8px;
    }

    .task-detail-modern .user-avatar-text {
        width: 28px;
        height: 28px;
        font-size: 11px;
    }
}

/* Animation for new items */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.checklist-item,
.attachment-item,
.comment-item {
    animation: slideIn 0.3s ease-out;
}

/* Better form styling */
.add-item-container .form-control-sm {
    border: 1px solid #c1c7d0;
}

.add-item-container .form-control-sm:focus {
    border-color: var(--theme-color);
    box-shadow: 0 0 0 2px rgba(var(--theme-color-rgb), 0.1);
}

/* Status indicators */
.priority-badge i {
    font-size: 10px;
}

.status-badge {
    font-weight: 500;
    letter-spacing: 0.3px;
}

/* Improved empty states */
.empty-state {
    background: #fafbfc;
    border: 2px dashed #c1c7d0;
    border-radius: 8px;
    margin: 8px 0;
}

.empty-state:hover {
    border-color: #a5adba;
    background: #f4f5f7;
}

/* Better file type icons */
.file-icon .ti-file-type-pdf { color: #ff5630; }
.file-icon .ti-file-type-doc { color: #2684ff; }
.file-icon .ti-file-type-xls { color: #36b37e; }
.file-icon .ti-photo { color: #ffab00; }
.file-icon .ti-file-zip { color: #6554c0; }

/* Progress bar enhancements */
.progress {
    background-color: #f4f5f7;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, var(--theme-color), rgba(var(--theme-color-rgb), 0.8));
    transition: width 0.3s ease;
}

/* Task title enhancements */
.task-title {
    word-break: break-word;
    line-height: 1.3;
}

.task-description {
    max-height: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}


/* setting-page-design-start */


.setting-menu-div .card .card-header:not(.border-0) h5:after,
.setting-menu-div .card .card-header:not(.border-0) .h5:after {
    left: -16px;
}

.setting-logo-box .logo-content {
    background-color: #f9f9f9;
    width: 100%;
    border-radius: 5px;
    position: relative;
    padding-top: 25%;
}

.setting-logo-box .logo-content img {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    object-fit: scale-down;
    padding: 10px 0;
}

.setting-logo-box .logo-content.dark-logo {
    background-color: #002333;
}

.setting-menu-div .note-editor.note-frame{
    margin: 0;
}
#seo-settings .logo-content {
    position: relative;
    background-color: #f9f9f9;
    width: 100%;
    border-radius: 5px;
    padding-top: 50%;
    overflow: hidden;
}
#seo-settings .logo-content .seo_image{
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}
#storage-settings .choices__inner {
    min-height: 42px;
    padding: 5.50px 7.5px 3.75px;
}
.custom-switch .btn-primary .switch-on{
    color: var(--bs-white);
}
.permission-inner {
    height: 177px;
    overflow-y: auto;
}
.permission-inner .badge{
    font-size: 12px;
}

/* setting-page-design-end */

/* Modern Comment System Styles */
.modern-comments-section {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid #f1f3f4;
}

.modern-comment-form {
    background: #fafbfc;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
    border: 1px solid #e8eaed;
    transition: all 0.2s ease;
}

.modern-comment-form:hover {
    border-color: #dadce0;
    box-shadow: 0 1px 6px rgba(32, 33, 36, 0.08);
}

.modern-comment-input {
    border: none;
    background: transparent;
    resize: none;
    font-size: 14px;
    line-height: 1.5;
    color: #202124;
    padding: 0;
    min-height: 60px;
    width: 100%;
}

.modern-comment-input:focus {
    outline: none;
    box-shadow: none;
}

.modern-comment-input::placeholder {
    color: #5f6368;
    font-weight: 400;
}

.comment-form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e8eaed;
}

.emoji-picker-btn {
    background: none;
    border: none;
    font-size: 20px;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.emoji-picker-btn:hover {
    background-color: #f1f3f4;
}

.comment-submit-actions {
    display: flex;
    gap: 8px;
}

.btn-modern {
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 13px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-modern-primary {
    background: #1a73e8;
    color: white;
}

.btn-modern-primary:hover {
    background: #1557b0;
    box-shadow: 0 1px 3px rgba(26, 115, 232, 0.4);
}

.btn-modern-secondary {
    background: transparent;
    color: #5f6368;
    border: 1px solid #dadce0;
}

.btn-modern-secondary:hover {
    background: #f8f9fa;
    border-color: #c4c7c5;
}

.modern-comment-item {
    padding: 20px 0;
    border-bottom: 1px solid #f1f3f4;
    position: relative;
}

.modern-comment-item:last-child {
    border-bottom: none;
}

.comment-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 16px;
    flex-shrink: 0;
}

.comment-main-content {
    flex: 1;
    margin-left: 16px;
}

.comment-header-modern {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.comment-author-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.comment-author-name {
    font-weight: 600;
    color: #202124;
    font-size: 14px;
}

.comment-timestamp {
    color: #5f6368;
    font-size: 12px;
}

.comment-menu-btn {
    background: none;
    border: none;
    color: #5f6368;
    padding: 4px;
    border-radius: 4px;
    cursor: pointer;
    opacity: 0;
    transition: all 0.2s ease;
}

.modern-comment-item:hover .comment-menu-btn {
    opacity: 1;
}

.comment-menu-btn:hover {
    background: #f1f3f4;
}

.comment-text-modern {
    color: #202124;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 12px;
    word-wrap: break-word;
}

.comment-reactions {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.reaction-item {
    background: #f1f3f4;
    border: 1px solid #e8eaed;
    border-radius: 16px;
    padding: 4px 8px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.reaction-item:hover {
    background: #e8eaed;
}

.reaction-item.user-reacted {
    background: #e8f0fe;
    border-color: #1a73e8;
    color: #1a73e8;
}

.comment-actions-modern {
    display: flex;
    align-items: center;
    gap: 16px;
}

.comment-action-btn {
    background: none;
    border: none;
    color: #5f6368;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.comment-action-btn:hover {
    background: #f1f3f4;
    color: #202124;
}

.reply-form-modern {
    margin-top: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e8eaed;
}

.reply-input-modern {
    border: none;
    background: transparent;
    resize: none;
    font-size: 13px;
    line-height: 1.4;
    color: #202124;
    padding: 0;
    min-height: 40px;
    width: 100%;
}

.reply-input-modern:focus {
    outline: none;
}

.reply-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 12px;
}

.replies-container {
    margin-top: 16px;
    margin-left: 56px;
    border-left: 2px solid #f1f3f4;
    padding-left: 16px;
}

.reply-item {
    padding: 12px 0;
    border-bottom: 1px solid #f8f9fa;
}

.reply-item:last-child {
    border-bottom: none;
}

.emoji-picker-container {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    background: white;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 12px;
    display: none;
    max-width: 280px;
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 4px;
    max-height: 200px;
    overflow-y: auto;
}

.emoji-item {
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 18px;
    text-align: center;
    transition: background-color 0.2s ease;
}

.emoji-item:hover {
    background: #f1f3f4;
}

.no-comments-state {
    text-align: center;
    padding: 40px 20px;
    color: #5f6368;
}

.no-comments-icon {
    font-size: 48px;
    color: #dadce0;
    margin-bottom: 16px;
}

.no-comments-text {
    font-size: 16px;
    margin-bottom: 8px;
}

.no-comments-subtext {
    font-size: 14px;
    color: #9aa0a6;
}

/* Animation for new comments */
@keyframes slideInComment {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.comment-slide-in {
    animation: slideInComment 0.3s ease-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .modern-comment-form {
        padding: 16px;
    }

    .replies-container {
        margin-left: 40px;
        padding-left: 12px;
    }

    .comment-form-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .comment-submit-actions {
        justify-content: flex-end;
    }
}

/* Time Tracker Styles */
.time-tracker-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

.timer-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.timer-label {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

.timer-value {
    font-size: 16px;
    font-weight: 600;
    color: #495057;
    font-family: 'Courier New', monospace;
}

.time-display {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #495057;
    background: #e9ecef;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
}

.timer-controls {
    display: flex;
    gap: 8px;
}

.timer-controls .btn {
    min-width: 120px;
    font-weight: 500;
}

.timer-status {
    text-align: center;
    padding: 8px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.timer-running {
    background: #d1ecf1 !important;
    border-color: #bee5eb !important;
    color: #0c5460 !important;
}

.timer-running .timer-value {
    color: #0c5460;
}

.timer-running .time-display {
    background: #17a2b8;
    color: white;
}

/* Responsive timer */
@media (max-width: 768px) {
    .time-tracker-container {
        padding: 16px;
    }

    .time-tracker-container .d-flex {
        flex-direction: column;
        gap: 16px;
    }

    .timer-controls {
        justify-content: center;
    }

    .timer-controls .btn {
        min-width: 100px;
    }
}

/* Project Progress Section Styles */
.project-progress-section {
    margin-bottom: 16px;
}

.project-progress-section .progress-label {
    font-size: 12px;
    font-weight: 500;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.project-progress-section .progress-percentage {
    font-size: 14px;
    font-weight: 600;
    color: var(--theme-color);
}

.project-progress-section .progress {
    background-color: #f1f5f9 !important;
    border-radius: 6px;
    overflow: hidden;
    height: 8px;
}

.project-progress-section .progress-bar {
    background-color: var(--theme-color) !important;
    border-radius: 6px;
    transition: width 0.6s ease;
    min-width: 2px; /* Ensure visibility even for small percentages */
}

/* Global Progress Bar Improvements */
.progress {
    background-color: #f1f5f9 !important;
    border-radius: 6px;
}

.progress-bar {
    background-color: var(--theme-color) !important;
    border-radius: 6px;
    transition: width 0.6s ease;
    min-width: 2px;
}

/* Team and Date Section */
.team-date-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
