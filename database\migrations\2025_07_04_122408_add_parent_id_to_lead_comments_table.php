<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddParentIdToLeadCommentsTable extends Migration
{
    public function up()
    {
        Schema::table('lead_comments', function (Blueprint $table) {
            $table->unsignedBigInteger('parent_id')->nullable()->after('lead_id');
            $table->foreign('parent_id')->references('id')->on('lead_comments')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::table('lead_comments', function (Blueprint $table) {
            $table->dropForeign(['parent_id']);
            $table->dropColumn('parent_id');
        });
    }
}