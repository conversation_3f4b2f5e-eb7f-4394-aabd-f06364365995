<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Plan;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plans', function (Blueprint $table) {
            if (!Schema::hasColumn('plans', 'automatish')) {
                $table->integer('automatish')->default(0)->after('chatgpt');
            }
        });

        // Update all existing plans to enable automatish by default
        $plans = Plan::all();
        foreach ($plans as $plan) {
            $plan->automatish = 1;
            $plan->save();
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plans', function (Blueprint $table) {
            if (Schema::hasColumn('plans', 'automatish')) {
                $table->dropColumn('automatish');
            }
        });
    }
};
