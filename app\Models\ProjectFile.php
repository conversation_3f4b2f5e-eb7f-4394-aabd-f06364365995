<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class ProjectFile extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'original_name',
        'file_path',
        'file_type',
        'mime_type',
        'file_size',
        'project_id',
        'folder_id',
        'description',
        'uploaded_by'
    ];

    protected $casts = [
        'file_size' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'formatted_size'
    ];

    /**
     * Get the project that owns the file
     */
    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the folder that contains the file
     */
    public function folder()
    {
        return $this->belongsTo(ProjectFolder::class, 'folder_id');
    }

    /**
     * Get the user who uploaded the file
     */
    public function uploader()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get file size in human readable format
     */
    public function getFormattedSizeAttribute()
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get file icon based on file type
     */
    public function getFileIconAttribute()
    {
        $extension = strtolower($this->file_type);
        
        $icons = [
            // Images
            'jpg' => 'ti-photo',
            'jpeg' => 'ti-photo',
            'png' => 'ti-photo',
            'gif' => 'ti-photo',
            'svg' => 'ti-photo',
            'webp' => 'ti-photo',
            
            // Documents
            'pdf' => 'ti-file-type-pdf',
            'doc' => 'ti-file-type-doc',
            'docx' => 'ti-file-type-doc',
            'xls' => 'ti-file-type-xls',
            'xlsx' => 'ti-file-type-xls',
            'ppt' => 'ti-file-type-ppt',
            'pptx' => 'ti-file-type-ppt',
            'txt' => 'ti-file-text',
            
            // Archives
            'zip' => 'ti-file-zip',
            'rar' => 'ti-file-zip',
            '7z' => 'ti-file-zip',
            
            // Code
            'html' => 'ti-file-code',
            'css' => 'ti-file-code',
            'js' => 'ti-file-code',
            'php' => 'ti-file-code',
            'py' => 'ti-file-code',
            
            // Video
            'mp4' => 'ti-video',
            'avi' => 'ti-video',
            'mov' => 'ti-video',
            'wmv' => 'ti-video',
            
            // Audio
            'mp3' => 'ti-music',
            'wav' => 'ti-music',
            'flac' => 'ti-music',
        ];
        
        return $icons[$extension] ?? 'ti-file';
    }

    /**
     * Get file URL
     */
    public function getUrlAttribute()
    {
        return Storage::url($this->file_path);
    }

    /**
     * Check if file is an image
     */
    public function getIsImageAttribute()
    {
        $imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'];
        return in_array(strtolower($this->file_type), $imageTypes);
    }

    /**
     * Check if user can access this file
     */
    public function canAccess($user)
    {
        // Check if user has access to the project
        return $this->project->users()->where('user_id', $user->id)->exists() || 
               $this->project->client_id == $user->id ||
               $user->type == 'super admin' ||
               $user->type == 'company';
    }

    /**
     * Delete file from storage when model is deleted
     */
    protected static function boot()
    {
        parent::boot();
        
        static::deleting(function ($file) {
            if (Storage::exists($file->file_path)) {
                Storage::delete($file->file_path);
            }
        });
    }
}
