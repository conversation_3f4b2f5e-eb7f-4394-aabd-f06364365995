<?php

/**
 * RetrieveAll.php
 * php version 7.2.0
 *
 * @category Trait
 * @package  Xendit\ApiOperations
 * <AUTHOR> <<EMAIL>>
 * @license  https://opensource.org/licenses/MIT MIT License
 * @link     https://api.xendit.co
 */

namespace App\Xendit\ApiOperations;

/**
 * Trait RetrieveAll
 *
 * @category Trait
 * @package  Xendit\ApiOperations
 * <AUTHOR> <<EMAIL>>
 * @license  https://opensource.org/licenses/MIT MIT License
 * @link     https://api.xendit.co
 */
trait RetrieveAll
{
    /**
     * Send request to get all object, e.g Invoice
     *
     * @return array
     */
    public static function retrieveAll($params = [])
    {
        $url = static::classUrl();
        return static::_request('GET', $url, $params);
    }
}
