@if(isset($projects) && !empty($projects) && count($projects) > 0)
    <div class="col-12">
        <div class="row">

            @foreach ($projects as $key => $project)
            <div class="col-xxl-3 col-md-4 col-sm-6 col-12 mb-4">
                <div class="modern-project-card project-card-clickable" data-project-url="{{ route('projects.show', $project) }}">
                    <!-- Card Header -->
                    <div class="card-header-modern">
                        <div class="project-category">
                            <span class="category-badge bg-{{ \App\Models\Project::$status_color[$project->status] }}">
                                {{ __(\App\Models\Project::$project_status[$project->status]) }}
                            </span>
                        </div>
                        <div class="card-actions">
                            <div class="dropdown">
                                <button class="action-dots" data-bs-toggle="dropdown" aria-expanded="false" onclick="event.stopPropagation();">
                                    <i class="ti ti-dots-vertical"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end shadow border-0">
                                    @can('create project')
                                        <li><a class="dropdown-item" data-ajax-popup="true" data-size="md"
                                                data-title="{{ __('Duplicate Project') }}"
                                                data-url="{{ route('project.copy', [$project->id]) }}">
                                                <i class="ti ti-copy me-2"></i> {{ __('Duplicate') }}</a></li>
                                    @endcan
                                    @can('edit project')
                                        <li><a class="dropdown-item" href="#!" data-size="lg"
                                                data-url="{{ route('projects.edit', $project->id) }}"
                                                data-ajax-popup="true">
                                                <i class="ti ti-pencil me-2"></i> {{ __('Edit') }}</a></li>
                                    @endcan
                                    @can('delete project')
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            {!! Form::open(['method' => 'DELETE', 'route' => ['projects.destroy', $project->id]]) !!}
                                            <a href="#!" class="dropdown-item text-danger bs-pass-para">
                                                <i class="ti ti-trash me-2"></i> {{ __('Delete') }}</a>
                                            {!! Form::close() !!}
                                        </li>
                                    @endcan
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Card Body -->
                    <div class="card-body-modern">
                        <!-- Project Title -->
                        <h3 class="project-title">{{ $project->project_name }}</h3>

                        <!-- Project Description -->
                        <p class="project-description">
                            {{ $project->description ? Str::limit($project->description, 80) : 'Here you will make a project description.' }}
                        </p>

                        <!-- Project Icon/Logo Section -->
                        <div class="project-brand">
                            <div class="brand-icon">
                                <div class="project-logo">
                                    <i class="ti ti-list-check"></i>
                                </div>
                            </div>
                            <div class="brand-info">
                                <span class="brand-name">
                                    <i class="ti ti-list-check"></i>
                                    {{ $project->tasks->count() }} {{ __('Total Tasks') }}
                                </span>
                                <span class="brand-url">{{ $project->countTask() }} {{ __('Completed') }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Card Footer -->
                    <div class="card-footer-modern">
                        <!-- Progress Section -->
                        @php
                            $lastTaskId = $last_task ? $last_task->id : null;
                            $progressData = $lastTaskId ? $project->project_progress($project, $lastTaskId) : ['percentage' => '0%', 'color' => 'secondary'];
                        @endphp
                        <div class="project-progress-section mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="progress-label">{{ __('Progress') }}</span>
                                <span class="progress-percentage fw-bold" style="color: var(--theme-color);">{{ $progressData['percentage'] }}</span>
                            </div>
                            <div class="progress" style="height: 8px; background-color: #f1f5f9; border-radius: 6px;">
                                <div class="progress-bar"
                                     role="progressbar"
                                     style="width: {{ $progressData['percentage'] }}; background-color: var(--theme-color); border-radius: 6px;"
                                     aria-valuenow="{{ str_replace('%', '', $progressData['percentage']) }}"
                                     aria-valuemin="0"
                                     aria-valuemax="100">
                                </div>
                            </div>
                        </div>

                        <!-- Team and Date Section -->
                        <div class="team-date-section">
                            <!-- Team Members -->
                            <div class="team-members">
                            @foreach ($project->users->take(3) as $user)
                                <div class="member-avatar" title="{{ $user->name }}">
                                    {{ strtoupper(substr($user->name, 0, 1)) }}
                                </div>
                            @endforeach
                            @if($project->users->count() > 3)
                                <div class="member-avatar more-members">
                                    +{{ $project->users->count() - 3 }}
                                </div>
                            @endif
                            </div>

                            <!-- Project Date -->
                            <div class="project-date">
                                <i class="ti ti-calendar"></i>
                                <span>{{ isset($project->end_date) ? \Carbon\Carbon::parse($project->end_date)->format('d M y') : \Carbon\Carbon::parse($project->start_date)->format('d M y') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>

@else
    <div class="col-xl-12 col-lg-12 col-sm-12">
        <div class="card">
            <div class="card-body">
                <h6 class="text-center mb-0">{{__('No Projects Found.')}}</h6>
            </div>
        </div>
    </div>
@endif
