<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLeadCommentReactionsTable extends Migration
{
    public function up()
    {
        Schema::create('lead_comment_reactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('comment_id');
            $table->unsignedBigInteger('user_id');
            $table->string('reaction'); // 'like', 'love', 'laugh', 'angry', etc.
            $table->timestamps();

            $table->foreign('comment_id')->references('id')->on('lead_comments')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->unique(['comment_id', 'user_id']); // One reaction per user per comment
        });
    }

    public function down()
    {
        Schema::dropIfExists('lead_comment_reactions');
    }
}