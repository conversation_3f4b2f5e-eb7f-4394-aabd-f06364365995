@extends('layouts.admin')

@section('page-title')
    {{ __('Company Details') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('system-admin.dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ route('system-admin.companies') }}">{{ __('Companies') }}</a></li>
    <li class="breadcrumb-item">{{ __('Details') }}</li>
@endsection

@section('action-button')
    <div class="float-end">
        <a href="{{ route('system-admin.companies.edit', $company->id) }}" data-bs-toggle="tooltip" title="{{ __('Edit') }}"
            class="btn btn-sm btn-primary">
            <i class="ti ti-pencil"></i>
        </a>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="row">
                <div class="col-sm-12 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="row align-items-center justify-content-between">
                                <div class="col-auto mb-3 mb-sm-0">
                                    <div class="d-flex align-items-center">
                                        <div class="theme-avtar bg-primary">
                                            <i class="ti ti-building"></i>
                                        </div>
                                        <div class="ms-3">
                                            <small class="text-muted">{{ __('Company Name') }}</small>
                                            <h6 class="m-0">{{ $company->name }}</h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="row align-items-center justify-content-between">
                                <div class="col-auto mb-3 mb-sm-0">
                                    <div class="d-flex align-items-center">
                                        <div class="theme-avtar bg-info">
                                            <i class="ti ti-mail"></i>
                                        </div>
                                        <div class="ms-3">
                                            <small class="text-muted">{{ __('Email') }}</small>
                                            <h6 class="m-0">{{ $company->email }}</h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="row align-items-center justify-content-between">
                                <div class="col-auto mb-3 mb-sm-0">
                                    <div class="d-flex align-items-center">
                                        <div class="theme-avtar bg-warning">
                                            <i class="ti ti-trophy"></i>
                                        </div>
                                        <div class="ms-3">
                                            <small class="text-muted">{{ __('Plan') }}</small>
                                            <h6 class="m-0">
                                                @if($company->plan)
                                                    {{ $company->plan->name ?? __('Default Plan') }}
                                                @else
                                                    {{ __('No Plan Assigned') }}
                                                @endif
                                            </h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="row align-items-center justify-content-between">
                                <div class="col-auto mb-3 mb-sm-0">
                                    <div class="d-flex align-items-center">
                                        <div class="theme-avtar bg-success">
                                            <i class="ti ti-calendar"></i>
                                        </div>
                                        <div class="ms-3">
                                            <small class="text-muted">{{ __('Created Date') }}</small>
                                            <h6 class="m-0">{{ \Auth::user()->dateFormat($company->created_at) }}</h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('Company Information') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ __('Company Name') }}</label>
                                <p class="text-muted">{{ $company->name }}</p>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ __('Email Address') }}</label>
                                <p class="text-muted">{{ $company->email }}</p>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ __('User Type') }}</label>
                                <p class="text-muted">
                                    <span class="badge bg-primary">{{ ucfirst($company->type) }}</span>
                                </p>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ __('Language') }}</label>
                                <p class="text-muted">{{ strtoupper($company->lang) }}</p>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ __('Plan') }}</label>
                                <p class="text-muted">
                                    @if($company->plan)
                                        <span class="badge bg-success">{{ $company->plan->name ?? __('Default Plan') }}</span>
                                    @else
                                        <span class="badge bg-secondary">{{ __('No Plan Assigned') }}</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ __('Email Verified') }}</label>
                                <p class="text-muted">
                                    @if($company->email_verified_at)
                                        <span class="badge bg-success">{{ __('Verified') }}</span>
                                        <small class="d-block">{{ \Auth::user()->dateFormat($company->email_verified_at) }}</small>
                                    @else
                                        <span class="badge bg-warning">{{ __('Not Verified') }}</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ __('Created Date') }}</label>
                                <p class="text-muted">{{ \Auth::user()->dateFormat($company->created_at) }}</p>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <label class="form-label">{{ __('Last Updated') }}</label>
                                <p class="text-muted">{{ \Auth::user()->dateFormat($company->updated_at) }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
