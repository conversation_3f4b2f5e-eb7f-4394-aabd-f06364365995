<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TaskComment extends Model
{
    protected $fillable = [
        'comment',
        'task_id',
        'user_id',
        'user_type',
        'created_by',
        'parent_id',
        'comment_reaction',
    ];

    protected $casts = [
        'comment_reaction' => 'array'
    ];

    public function user()
    {
        return $this->hasOne('App\Models\User', 'id', 'created_by');
    }

    // Add relationships for replies
    public function replies()
    {
        return $this->hasMany(TaskComment::class, 'parent_id')->orderBy('created_at', 'asc');
    }

    public function parent()
    {
        return $this->belongsTo(TaskComment::class, 'parent_id');
    }

    // Helper method to check if this is a reply
    public function isReply()
    {
        return !is_null($this->parent_id);
    }

    // Helper method to get reaction counts
    public function getReactionCounts()
    {
        if (!$this->comment_reaction) {
            return [];
        }

        $counts = [];
        foreach ($this->comment_reaction as $reaction) {
            $emoji = $reaction['reaction'];
            if (!isset($counts[$emoji])) {
                $counts[$emoji] = 0;
            }
            $counts[$emoji]++;
        }

        return $counts;
    }

    // Helper method to get user's reaction
    public function getUserReaction($username)
    {
        if (!$this->comment_reaction) {
            return null;
        }

        foreach ($this->comment_reaction as $reaction) {
            if ($reaction['username'] === $username) {
                return $reaction['reaction'];
            }
        }

        return null;
    }
}
