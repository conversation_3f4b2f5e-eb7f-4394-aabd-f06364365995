{{ Form::open(array('route' => ['form.field.store',$formbuilder->id], 'class'=>'needs-validation', 'novalidate')) }}
<div class="modal-body">
    <div class="row" id="frm_field_data">
        <div class="col-12 form-group">
            {{ Form::label('name', __('Question Name'),['class'=>'form-label']) }}<x-required></x-required>
            {{ Form::text('name[]', '', array('class' => 'form-control','required'=>'required', 'placeholder'=>__('Enter Question Name'))) }}
        </div>
        <div class="col-12 form-group">
            {{ Form::label('type', __('Type'),['class'=>'form-label']) }}<x-required></x-required>
            {{ Form::select('type[]', $types,null, array('class' => 'form-control select2 field-type-select','id'=>'choices-multiple1','required'=>'required')) }}
        </div>

        <div class="col-12 form-group">
            <div class="form-check form-switch">
                {{ Form::checkbox('required[]', 1, false, ['class' => 'form-check-input', 'id' => 'required-toggle']) }}
                {{ Form::label('required-toggle', __('Required Field'), ['class' => 'form-check-label']) }}
            </div>
        </div>

        {{-- Dynamic Options Input --}}
        <div class="col-12 form-group options-container" style="display:none;">
            {{ Form::label('options[]', __('Options (for checkbox/radio/select/multiselect)'), ['class' => 'form-label']) }}
            <div class="options-list">
                <div class="d-flex mb-2">
                    {{ Form::text('options[]', null, ['class' => 'form-control me-2', 'placeholder' => 'Option 1']) }}
                    <button type="button" class="btn btn-sm btn-success add-option">+</button>
                </div>
            </div>
        </div>

    </div>
</div>
<div class="modal-footer">
    <input type="button" value="{{__('Cancel')}}" class="btn  btn-secondary" data-bs-dismiss="modal">
    <input type="submit" value="{{__('Create')}}" class="btn  btn-primary">
</div>
{{Form::close()}}

<script>
// Handle field type change for form builder
$(document).on('change', '#commonModal .field-type-select', function() {
    const selected = $(this).val();
    const optionsContainer = $('#commonModal .options-container');

    if (selected === 'checkbox' || selected === 'radio' || selected === 'select' || selected === 'multiselect') {
        optionsContainer.show();
        console.log('Showing options container for form builder');
    } else {
        optionsContainer.hide();
        console.log('Hiding options container for form builder');
    }
});

// Handle add/remove option buttons for form builder
$(document).on('click', '#commonModal .add-option', function(e) {
    e.preventDefault();
    console.log('Add option clicked in form builder');
    const optionsList = $('#commonModal .options-list');
    const newOption = `
        <div class="d-flex mb-2">
            <input type="text" name="options[]" class="form-control me-2" placeholder="Option">
            <button type="button" class="btn btn-sm btn-danger remove-option">-</button>
        </div>
    `;
    optionsList.append(newOption);
});

$(document).on('click', '#commonModal .remove-option', function(e) {
    e.preventDefault();
    console.log('Remove option clicked in form builder');
    $(this).closest('.d-flex').remove();
});

// Initialize when modal is shown for form builder
$(document).on('shown.bs.modal', '#commonModal', function() {
    // Check if this is form builder modal by looking for field-type-select
    const typeSelect = $('#commonModal .field-type-select');
    if (typeSelect.length) {
        console.log('Form Builder modal shown, initializing form');
        // Trigger initial state
        typeSelect.trigger('change');
    }
});
</script>
