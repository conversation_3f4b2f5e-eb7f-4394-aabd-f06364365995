<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class SystemAdminRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create System Admin permissions
        $systemAdminPermissions = [
            [
                'name' => 'manage system admin dashboard',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'manage super admins',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'create super admin',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'edit super admin',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'delete super admin',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'manage module permissions',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'assign module permissions',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'view super admin activities',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'manage system modules',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'create system module',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'edit system module',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'delete system module',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'manage pricing plan',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'create pricing plan',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'edit pricing plan',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'delete pricing plan',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        // Insert permissions
        Permission::insert($systemAdminPermissions);

        // Create System Admin role
        $systemAdminRole = Role::create([
            'name' => 'system admin',
            'created_by' => 0,
        ]);

        // Assign all system admin permissions to the role
        $permissionNames = [
            'manage system admin dashboard',
            'manage super admins',
            'create super admin',
            'edit super admin',
            'delete super admin',
            'manage module permissions',
            'assign module permissions',
            'view super admin activities',
            'manage system modules',
            'create system module',
            'edit system module',
            'delete system module',
            // Also include some super admin permissions for system oversight
            'manage user',
            'create user',
            'edit user',
            'delete user',
            'manage role',
            'create role',
            'edit role',
            'delete role',
            'manage permission',
            'create permission',
            'edit permission',
            'delete permission',
            'manage system settings',
            'manage plan',
            'create plan',
            'edit plan',
            'manage order',
            'manage coupon',
            'create coupon',
            'edit coupon',
            'delete coupon',
            'manage pricing plan',
            'create pricing plan',
            'edit pricing plan',
            'delete pricing plan',
        ];

        $systemAdminRole->givePermissionTo($permissionNames);

        // Create default System Admin user
        $systemAdmin = User::create([
            'name' => 'System Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('<EMAIL>'),
            'type' => 'system admin',
            'lang' => 'en',
            'avatar' => '',
            'created_by' => 0,
            'email_verified_at' => now(),
        ]);

        $systemAdmin->assignRole($systemAdminRole);

        // Seed default modules
        $modules = [
            [
                'name' => 'User Management',
                'slug' => 'user-management',
                'description' => 'Manage users, roles and permissions',
                'is_active' => true,
                'sort_order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'CRM Module',
                'slug' => 'crm',
                'description' => 'Customer Relationship Management features',
                'is_active' => true,
                'sort_order' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'HRM Module',
                'slug' => 'hrm',
                'description' => 'Human Resource Management features',
                'is_active' => true,
                'sort_order' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Project Management',
                'slug' => 'project-management',
                'description' => 'Project and task management features',
                'is_active' => true,
                'sort_order' => 4,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Accounting Module',
                'slug' => 'accounting',
                'description' => 'Financial and accounting features',
                'is_active' => true,
                'sort_order' => 5,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'POS Module',
                'slug' => 'pos',
                'description' => 'Point of Sale features',
                'is_active' => true,
                'sort_order' => 6,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'System Settings',
                'slug' => 'system-settings',
                'description' => 'System configuration and settings',
                'is_active' => true,
                'sort_order' => 7,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Plan Management',
                'slug' => 'plan-management',
                'description' => 'Subscription plans and billing',
                'is_active' => true,
                'sort_order' => 8,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('system_admin_modules')->insert($modules);
    }
}
