<?php

namespace App\Observers;

use App\Models\PricingPlan;
use App\Models\User;

class PricingPlanObserver
{
    /**
     * Handle the PricingPlan "updated" event.
     */
    public function updated(PricingPlan $pricingPlan): void
    {
        // Check if module_permissions were changed
        if ($pricingPlan->wasChanged('module_permissions')) {
            $this->syncUsersModulePermissions($pricingPlan);
        }
    }

    /**
     * Sync module_permissions for all users assigned to this pricing plan
     */
    private function syncUsersModulePermissions(PricingPlan $pricingPlan)
    {
        try {
            // Get all users assigned to this pricing plan
            $users = User::where('plan', $pricingPlan->id)
                ->where('type', 'company')
                ->get();

            foreach ($users as $user) {
                // Update user's module_permissions field
                $user->module_permissions = $pricingPlan->module_permissions ?? [];
                $user->save();

                // Refresh Spatie permissions based on new module permissions
                $user->assignPricingPlanPermissions();

                // Sync to external modules if OMX Flow permissions changed
                if (isset($pricingPlan->module_permissions['omx_flow'])) {
                    $this->syncToOmxFlow($user, $pricingPlan->module_permissions['omx_flow']);
                }

                \Log::info('Observer: Updated user module permissions after pricing plan change', [
                    'user_id' => $user->id,
                    'user_email' => $user->email,
                    'pricing_plan_id' => $pricingPlan->id,
                    'pricing_plan_name' => $pricingPlan->name,
                    'new_permissions' => $pricingPlan->module_permissions
                ]);
            }

            \Log::info('Observer: Successfully updated module permissions for all users in pricing plan', [
                'pricing_plan_id' => $pricingPlan->id,
                'pricing_plan_name' => $pricingPlan->name,
                'users_updated' => $users->count()
            ]);

        } catch (\Exception $e) {
            \Log::error('Observer: Failed to update users module permissions after pricing plan change', [
                'pricing_plan_id' => $pricingPlan->id,
                'pricing_plan_name' => $pricingPlan->name,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Sync user to OMX Flow module
     */
    private function syncToOmxFlow($user, $omxFlowPermissions)
    {
        try {
            // Get enabled OMX Flow modules
            $modules = \App\Models\ModuleIntegration::enabled()->get();

            foreach ($modules as $module) {
                // Only sync to modules that match the OMX Flow module
                if ($module->name === 'omx_flow' || $module->name === 'omx-flow' || $module->name === 'OMX FLOW') {
                    $moduleIntegrationController = app(\App\Http\Controllers\ModuleIntegrationController::class);

                    // Prepare additional fields for company creation with default super admin email
                    $additionalFields = [
                        'company_name' => $user->name,
                        'company_description' => 'Company updated via pricing plan integration (Observer)',
                        'super_admin_email' => '<EMAIL>'
                    ];

                    // Send to external module with permissions and additional data
                    $result = $moduleIntegrationController->syncUserToModule($user, $module, $omxFlowPermissions, $additionalFields);

                    \Log::info('Observer: OMX Flow sync result for company after pricing plan update', [
                        'user_id' => $user->id,
                        'user_email' => $user->email,
                        'module_name' => $module->name,
                        'permissions' => $omxFlowPermissions,
                        'additional_fields' => $additionalFields,
                        'sync_successful' => $result
                    ]);
                }
            }
        } catch (\Exception $e) {
            \Log::error('Observer: Failed to sync company to OMX Flow after pricing plan update', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'permissions' => $omxFlowPermissions,
                'error' => $e->getMessage()
            ]);
        }
    }
}
