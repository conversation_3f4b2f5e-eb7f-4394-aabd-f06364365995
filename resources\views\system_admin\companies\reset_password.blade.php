<form method="post" action="{{ route('system-admin.companies.reset.update', $company->id) }}">
    @csrf
    <div class="modal-body">
        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    {{ Form::label('password', __('Password'), ['class' => 'form-label']) }}
                    {{ Form::password('password', ['class' => 'form-control', 'required' => 'required', 'placeholder' => __('Enter Password')]) }}
                    @error('password')
                        <span class="invalid-password" role="alert">
                            <strong class="text-danger">{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group">
                    {{ Form::label('password_confirmation', __('Confirm Password'), ['class' => 'form-label']) }}
                    {{ Form::password('password_confirmation', ['class' => 'form-control', 'required' => 'required', 'placeholder' => __('Enter Confirm Password')]) }}
                    @error('password_confirmation')
                        <span class="invalid-password" role="alert">
                            <strong class="text-danger">{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <input type="button" value="{{ __('Cancel') }}" class="btn btn-light" data-bs-dismiss="modal">
        <input type="submit" value="{{ __('Update') }}" class="btn btn-primary">
    </div>
</form>
