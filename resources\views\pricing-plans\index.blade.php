@extends('layouts.admin')

@section('page-title')
    {{ __('Pricing Plans') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('system-admin.dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('Pricing Plans') }}</li>
@endsection

@section('action-btn')
    <div class="float-start me-2">
        <form method="GET" action="{{ route('pricing-plans.index') }}">
            <select name="plan_type" class="form-select" onchange="this.form.submit()">
                <option value="">{{ __('Filter by Plan Type') }}</option>
                @foreach(\App\Models\PricingPlan::getPlanTypeOptions() as $key => $value)
                    <option value="{{ $key }}" {{ request('plan_type') == $key ? 'selected' : '' }}>{{ $value }}</option>
                @endforeach
            </select>
        </form>
    </div>
    <div class="float-end">
        @if (\Auth::user()->type == 'system admin' || (\Auth::user()->type == 'staff' && \Auth::user()->can('create pricing plans')))
            <a href="{{ route('pricing-plans.create') }}" class="btn btn-sm btn-primary">
                <i class="ti ti-plus"></i> {{ __('Create Plan') }}
            </a>
        @endif
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-header card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table" id="pc-dt-simple">
                            <thead>
                                <tr>
                            <th>{{ __('Name') }}</th>
                            <th>{{ __('Plan Type') }}</th>
                            <th>{{ __('Price') }}</th>
                            <th>{{ __('Duration') }}</th>
                            <th>{{ __('Max Users') }}</th>
                            <th>{{ __('Modules') }}</th>
                            <th>{{ __('Status') }}</th>
                            <th>{{ __('Action') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($pricingPlans as $plan)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div>
                                                    <h6 class="mb-0">{{ $plan->name }}</h6>
                                                    @if($plan->description)
                                                        <small class="text-muted">{{ Str::limit($plan->description, 50) }}</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ ucfirst(str_replace('_', ' ', $plan->plan_type)) }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">${{ number_format($plan->price, 2) }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ ucfirst($plan->duration) }}</span>
                                        </td>
                                        <td>
                                            <span class="text-dark">{{ $plan->max_users == -1 ? 'Unlimited' : $plan->max_users }}</span>
                                        </td>
                                        <td>
                                            @if($plan->module_permissions)
                                                @foreach(array_keys($plan->module_permissions) as $module)
                                                    <span class="badge bg-secondary me-1">{{ ucfirst($module) }}</span>
                                                @endforeach
                                            @else
                                                <span class="text-muted">{{ __('No modules') }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input status-toggle" type="checkbox" 
                                                       data-id="{{ $plan->id }}" 
                                                       {{ $plan->status === 'active' ? 'checked' : '' }}>
                                                <label class="form-check-label">
                                                    <span class="badge bg-{{ $plan->status === 'active' ? 'success' : 'danger' }}">
                                                        {{ ucfirst($plan->status) }}
                                                    </span>
                                                </label>
                                            </div>
                                        </td>
                                        <td>
                                            @if (\Auth::user()->type == 'system admin' || (\Auth::user()->type == 'staff' && \Auth::user()->can('view pricing plans')))
                                                <div class="action-btn bg-info ms-2">
                                                    <a href="{{ route('pricing-plans.show', $plan) }}"
                                                       class="mx-3 btn btn-sm align-items-center"
                                                       data-bs-toggle="tooltip" title="{{ __('View') }}">
                                                        <i class="ti ti-eye text-white"></i>
                                                    </a>
                                                </div>
                                            @endif
                                            @if (\Auth::user()->type == 'system admin' || (\Auth::user()->type == 'staff' && \Auth::user()->can('edit pricing plans')))
                                                <div class="action-btn bg-primary ms-2">
                                                    <a href="{{ route('pricing-plans.edit', $plan) }}"
                                                       class="mx-3 btn btn-sm align-items-center"
                                                       data-bs-toggle="tooltip" title="{{ __('Edit') }}">
                                                        <i class="ti ti-pencil text-white"></i>
                                                    </a>
                                                </div>
                                            @endif
                                            @if (\Auth::user()->type == 'system admin' || (\Auth::user()->type == 'staff' && \Auth::user()->can('delete pricing plans')))
                                                <div class="action-btn bg-danger ms-2">
                                                    <form method="POST" action="{{ route('pricing-plans.destroy', $plan) }}"
                                                          style="display: inline-block;">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit"
                                                                class="mx-3 btn btn-sm align-items-center show_confirm"
                                                                data-bs-toggle="tooltip" title="{{ __('Delete') }}">
                                                            <i class="ti ti-trash text-white"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
    <script>
        $(document).ready(function() {
            // Status toggle
            $('.status-toggle').on('change', function() {
                var planId = $(this).data('id');
                var isChecked = $(this).is(':checked');
                
                $.ajax({
                    url: '{{ route("pricing-plans.toggle-status", ":id") }}'.replace(':id', planId),
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            show_toastr('Success', response.message, 'success');
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        }
                    },
                    error: function(xhr) {
                        show_toastr('Error', 'Something went wrong!', 'error');
                        // Revert the toggle
                        $('.status-toggle[data-id="' + planId + '"]').prop('checked', !isChecked);
                    }
                });
            });

            // Delete confirmation
            $('.show_confirm').click(function(event) {
                var form = $(this).closest("form");
                event.preventDefault();
                
                Swal.fire({
                    title: 'Are you sure?',
                    text: "This action cannot be undone!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, delete it!'
                }).then((result) => {
                    if (result.isConfirmed) {
                        form.submit();
                    }
                });
            });
        });
    </script>
@endpush
