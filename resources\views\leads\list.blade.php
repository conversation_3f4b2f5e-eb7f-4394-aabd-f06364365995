@extends('layouts.admin')
@section('page-title')
    {{__('Manage Leads')}} @if($pipeline) - {{$pipeline->name}} @endif
@endsection

@push('css-page')
    <link rel="stylesheet" href="{{asset('css/summernote/summernote-bs4.css')}}">
    <style>
        .communication-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
        }

        .communication-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .communication-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .communication-btn.call { background-color: #28a745; }
        .communication-btn.sms { background-color: #17a2b8; }
        .communication-btn.email { background-color: #6f42c1; }
        .communication-btn.source { background-color: #fd7e14; }

        .communication-btn i {
            font-size: 14px;
        }

        .modal-body .communication-options-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .communication-option-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            border-radius: 8px;
            background-color: #f8f9fa;
            color: #333;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .communication-option-item:hover {
            background-color: #e9ecef;
            transform: translateX(5px);
        }

        .communication-option-item i {
            font-size: 20px;
            margin-right: 15px;
            width: 25px;
            text-align: center;
        }

        .communication-option-item span {
            font-size: 16px;
            font-weight: 500;
        }

        #whatsapp-option i { color: #25D366; }
        #default-email-option i { color: #007BFF; }
        #cloud-email-option i { color: #6f42c1; }
        .data-source-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
        }
        .source-internal {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }


    </style>
@endpush

@push('script-page')
    <script src="{{asset('css/summernote/summernote-bs4.js')}}"></script>
    <script>
        $(document).on('change', '#default_pipeline_id', function() {
            $('#change-pipeline').submit();
        });

        function openCommunicationModal(type) {
            const modal = new bootstrap.Modal(document.getElementById('communication-modal'));
            const title = document.getElementById('communication-modal-label');
            title.textContent = type === 'sms' ? 'SMS Options' : 'Email Options';
            modal.show();
        }

        function openSourcesModal(sources) {
            const sourcesList = document.getElementById('sources-list');
            sourcesList.innerHTML = ''; // Clear previous sources

            if (sources && sources.length > 0) {
                sources.forEach(source => {
                    const listItem = document.createElement('li');
                    listItem.className = 'list-group-item';
                    listItem.textContent = source.name;
                    sourcesList.appendChild(listItem);
                });
            } else {
                const listItem = document.createElement('li');
                listItem.className = 'list-group-item';
                listItem.textContent = '{{ __('No sources found for this lead.') }}';
                sourcesList.appendChild(listItem);
            }

            const modal = new bootstrap.Modal(document.getElementById('sources-modal'));
            modal.show();
        }












        // Global function to handle filter form submission using jQuery hide/show
        window.handleFilterSubmit = function(form) {
            var nameFilter = $(form).find('input[name="name"]').val().toLowerCase();
            var emailFilter = $(form).find('input[name="email"]').val().toLowerCase();
            var stageFilter = $(form).find('select[name="stage_id"]').val();

            // Check if any filters are applied
            var hasFilters = nameFilter || emailFilter || stageFilter;

            // Show/hide filter active badge
            if (hasFilters) {
                $('#filter-active-badge').removeClass('d-none');
            } else {
                $('#filter-active-badge').addClass('d-none');
            }

            var visibleRowsCount = 0;

            // Filter table rows
            $('#leads-tbody tr').each(function() {
                var row = $(this);
                var leadName = row.find('.lead-name a').text().toLowerCase();
                var leadEmail = row.find('.lead-email').text().toLowerCase();
                var leadStage = row.find('td:nth-child(5)').text().toLowerCase(); // Stage column

                var showRow = true;

                // Apply name filter
                if (nameFilter && leadName.indexOf(nameFilter) === -1) {
                    showRow = false;
                }

                // Apply email filter
                if (emailFilter && leadEmail.indexOf(emailFilter) === -1) {
                    showRow = false;
                }

                // Apply stage filter
                if (stageFilter) {
                    // Get the stage name from the stages dropdown to match
                    var selectedStageName = $('#lead-filter-form select[name="stage_id"] option:selected').text().toLowerCase();
                    if (selectedStageName && leadStage.indexOf(selectedStageName) === -1) {
                        showRow = false;
                    }
                }

                if (showRow) {
                    row.show();
                    visibleRowsCount++;
                } else {
                    row.hide();
                }
            });

            // Show message if no results found
            if (visibleRowsCount === 0 && hasFilters) {
                if ($('#no-results-row').length === 0) {
                    $('#leads-tbody').append('<tr id="no-results-row"><td colspan="9" class="text-center py-4">{{__("No leads found matching the filter criteria.")}}</td></tr>');
                }
            } else {
                $('#no-results-row').remove();
            }

            // Close the off-canvas
            var offcanvasElement = document.getElementById('filterOffcanvas');
            var offcanvas = bootstrap.Offcanvas.getInstance(offcanvasElement);
            if (offcanvas) {
                offcanvas.hide();
            }

            // Show success message
            show_toastr('Success', 'Filters applied successfully', 'success');

            return false; // Prevent default form submission
        };

        // Global function to clear filters
        window.clearFilters = function() {
            // Reset the filter form
            $('#lead-filter-form')[0].reset();

            // Reset select dropdown
            $('#lead-filter-form select').val('').trigger('change');

            // Hide filter active badge
            $('#filter-active-badge').addClass('d-none');

            // Show all table rows
            $('#leads-tbody tr').show();

            // Remove no results message
            $('#no-results-row').remove();

            // Show success message
            show_toastr('Success', 'Filters cleared successfully', 'success');
        };

        // Initialize off-canvas filter
        $(document).ready(function() {
            // Initialize select2 for the stage dropdown in off-canvas
            $('#filterOffcanvas .select').select2({
                dropdownParent: $('#filterOffcanvas'),
                placeholder: '{{__("Select Stage")}}',
                allowClear: true
            });

            // Handle off-canvas shown event
            $('#filterOffcanvas').on('shown.bs.offcanvas', function () {
                // Focus on first input when off-canvas is shown
                $(this).find('input[name="name"]').focus();
            });
        });
    </script>
@endpush

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item">{{__('Lead')}}</li>
@endsection
@section('action-btn')
    <div class="float-end">
    {{ Form::open(['route' => 'deals.change.pipeline', 'id' => 'change-pipeline', 'class' => 'btn btn-sm']) }}
        {{ Form::select('default_pipeline_id', $pipelines, $pipeline->id, ['class' => 'form-control select me-2', 'id' => 'default_pipeline_id', 'style' => 'min-width: 180px; display: inline-block;']) }}
        {{ Form::close() }}
        <a href="{{ route('leads.index') }}" data-bs-toggle="tooltip" title="{{__('Kanban View')}}" class="btn btn-sm bg-light-blue-subtitle me-1">
            <i class="ti ti-layout-grid"></i>
        </a>
        <a href="#" data-size="md"  data-bs-toggle="tooltip" title="{{__('Import')}}" data-url="{{ route('leads.import') }}" data-ajax-popup="true" data-title="{{__('Import Lead CSV file')}}" class="btn btn-sm bg-brown-subtitle me-1">
            <i class="ti ti-file-import"></i>
        </a>
        <a href="#" data-bs-toggle="offcanvas" data-bs-target="#filterOffcanvas" aria-controls="filterOffcanvas" data-bs-toggle="tooltip" title="{{__('Filter Leads')}}" class="btn btn-sm btn-primary me-1 position-relative" id="filter-btn">
            <i class="ti ti-filter"></i> {{__('Filter')}}
            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger d-none" id="filter-active-badge">
                <span class="visually-hidden">{{__('Filters Active')}}</span>
            </span>
        </a>
        <a href="{{route('leads.export')}}" data-bs-toggle="tooltip" title="{{__('Export')}}" class="btn btn-sm btn-secondary me-1">
            <i class="ti ti-file-export"></i>
        </a>
        <a href="#" id="module-btn" data-bs-toggle="tooltip" title="{{__('Module')}}" class="btn btn-sm btn-info me-1">
            <i class="ti ti-plug"></i> {{__('Module')}}
        </a>
        <a href="#" data-size="lg" data-url="{{ route('leads.create') }}" data-ajax-popup="true" data-bs-toggle="tooltip" title="{{__('Create New Lead')}}" data-title="{{__('Create Lead')}}" class="btn btn-sm btn-primary me-1">
            <i class="ti ti-plus"></i>
        </a>
    </div>
@endsection

@section('content')
    @if($pipeline)
        <div class="row">
            <div class="col-xl-12">
                <div class="card">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0">{{__('Leads List')}}</h5>
                            </div>

                        </div>
                    </div>
                    <div class="card-body table-border-style">
                        <div class="table-responsive">
                            <table class="table datatable" id="leads-table">
                                <thead>
                                <tr>
                                    <th>{{__('Source')}}</th>
                                    <th>{{__('Name')}}</th>
                                    <th>{{__('Email')}}</th>
                                    <th>{{__('Subject')}}</th>
                                    <th>{{__('Stage')}}</th>
                                    <th>{{__('Assign To')}}</th>
                                    <th>{{__('Follow-Up Date')}}</th>
                                    <th>{{__('Contact')}}</th>
                                    <th>{{__('Action')}}</th>
                                </tr>
                                </thead>
                                <tbody id="leads-tbody">
                                @if(count($leads) > 0)
                                    @foreach ($leads as $lead)
                                        @php
                                            $today = \Carbon\Carbon::today();
                                            $followUp = $lead->next_follow_up_date ? \Carbon\Carbon::parse($lead->next_follow_up_date) : null;
                                            $highlight = '';
                                            if ($followUp) {
                                                if ($followUp->isToday()) {
                                                    $highlight = 'badge bg-warning text-dark';
                                                } elseif ($followUp->isPast()) {
                                                    $highlight = 'badge bg-danger';
                                                }
                                            }
                                        @endphp
                                        <tr class="internal-lead" data-source="internal">
                                            <td>
                                                <span class="data-source-badge source-internal">
                                                    <i class="ti ti-database"></i> {{__('Internal')}}
                                                </span>
                                            </td>
                                            <td class="lead-name">
                                                <a href="{{ route('leads.show', $lead->id) }}" class="text-primary text-decoration-underline" style="cursor:pointer;">
                                                    {{ $lead->name }}
                                                </a>
                                            </td>
                                            <td class="lead-email">{{ $lead->email }}</td>
                                            <td>{{ $lead->subject }}</td>
                                            <td>{{  !empty($lead->stage)?$lead->stage->name:'-' }}</td>
                                            <td>
                                                @foreach($lead->users as $user)
                                                <div class="d-flex align-items-center">
                                                        <a href="#"
                                                        class="btn btn-sm me-1 p-0 d-flex align-items-center justify-content-center rounded-circle"
                                                        style="width: 30px; height: 30px; background-color: #f0f0f0; transition: all 0.3s ease;"
                                                        data-bs-toggle="tooltip"
                                                        data-bs-placement="top"
                                                        title="{{ $user->name }}">
                                                            <i class="ti ti-user" style="font-size: 16px; color: #333;"></i>
                                                        </a>
                                                    @endforeach
                                                </div>
                                            </td>
                                            <td>
                                                @if($lead->next_follow_up_date)
                                                    <span class="{{ $highlight }}">
                                                        {{ \Carbon\Carbon::parse($lead->next_follow_up_date)->format('d-m-Y') }}
                                                    </span>
                                                @else
                                                    -
                                                @endif
                                            </td>
                                            <td>
                                                <?php
                                                $sources = $lead->sources();
                                                ?>
                                                <div class="communication-buttons">
                                                    <button class="btn btn-sm d-flex align-items-center justify-content-center"
                                                        style="
                                                            background: linear-gradient(135deg, #6f42c1, #065f46);
                                                            border: none;
                                                            border-radius: 8px;
                                                            padding: 8px 12px;
                                                            transition: all 0.3s ease;
                                                            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                                        "
                                                        onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0,0,0,0.1)'"
                                                        data-bs-toggle="tooltip"
                                                        title="Call"
                                                        onclick="window.open('tel:{{ $lead->phone }}', '_self')">
                                                        <i class="fas fa-phone-alt text-white" style="font-size: 12px;"></i>
                                                    </button>

                                                    {{-- SMS Button --}}
                                                    <button class="btn btn-sm d-flex align-items-center justify-content-center"
                                                            style="
                                                                background: linear-gradient(135deg, #6f42c1, #065f46);
                                                                border: none;
                                                                border-radius: 8px;
                                                                padding: 8px 12px;
                                                                transition: all 0.3s ease;
                                                                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                                            "
                                                            onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                            onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0,0,0,0.1)'"
                                                            data-bs-toggle="tooltip"
                                                            title="SMS"
                                                            onclick="openCommunicationModal('sms')">
                                                        <i class="fas fa-comment-dots text-white" style="font-size: 16px;"></i>
                                                    </button>

                                                    {{-- Email Button --}}
                                                    <button class="btn btn-sm d-flex align-items-center justify-content-center"
                                                            style="
                                                                background: linear-gradient(135deg, #0d6efd, #0b5ed7);
                                                                border: none;
                                                                border-radius: 8px;
                                                                padding: 8px 12px;
                                                                transition: all 0.3s ease;
                                                                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                                            "
                                                            onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                            onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0,0,0,0.1)'"
                                                            data-bs-toggle="tooltip"
                                                            title="Email"
                                                            onclick="openCommunicationModal('email')">
                                                        <i class="fas fa-envelope text-white" style="font-size: 16px;"></i>
                                                    </button>

                                                    {{-- Sources Button --}}
                                                    <button class="btn btn-sm d-flex align-items-center justify-content-center"
                                                            style="
                                                                border: 1px solid #0d6efd;
                                                                background-color: transparent;
                                                                border-radius: 8px;
                                                                padding: 8px 12px;
                                                                transition: all 0.3s ease;
                                                            "
                                                            onmouseover="this.style.backgroundColor='#0d6efd'; this.querySelector('i').style.color='#fff'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                            onmouseout="this.style.backgroundColor='transparent'; this.querySelector('i').style.color='#0d6efd'; this.style.boxShadow='none'"
                                                            title="Activity"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#activityModal-{{ $lead->id }}">
                                                        <i class="fas fa-stream" style="font-size: 16px; color: #0d6efd;"></i>
                                                    </button>
                                                </div>
                                            </td>
                                            @if(Auth::user()->type != 'client')
                                                <td class="Action">
                                                    <span>
                                                    @can('view lead')
                                                            @if($lead->is_active)
                                                            <!-- <div class="action-btn">
                                                                <a href="{{ route('leads.show', $lead->id) }}"
                                                                class="mx-3 btn btn-sm d-flex align-items-center justify-content-center"
                                                                style="
                                                                        background: linear-gradient(135deg, #14532d, #065f46); 
                                                                        border: none;
                                                                        border-radius: 8px;
                                                                        padding: 8px 12px;
                                                                        transition: all 0.3s ease;
                                                                        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                                                "
                                                                onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                                onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0,0,0,0.1)'"
                                                                data-size="xl"
                                                                data-bs-toggle="tooltip"
                                                                title="{{ __('View') }}"
                                                                data-title="{{ __('Lead Detail') }}">
                                                                    <i class="ti ti-eye text-white" style="font-size: 16px;"></i>
                                                                </a>
                                                            </div> -->
                                                            @endif
                                                        @endcan
                                                        @can('edit lead')
                                                        <div class="action-btn" style="margin-left: 15px;">
                                                            <a href="#"
                                                            class="mx-3 btn btn-sm d-flex align-items-center justify-content-center"
                                                            style="
                                                                background: linear-gradient(135deg, #14532d, #065f46); 
                                                                border: none;
                                                                border-radius: 8px;
                                                                padding: 8px 12px;
                                                                transition: all 0.3s ease;
                                                                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                                            "
                                                            onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                            onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0,0,0,0.1)'"
                                                            data-url="{{ route('leads.edit', $lead->id) }}"
                                                            data-ajax-popup="true"
                                                            data-size="xl"
                                                            data-bs-toggle="tooltip"
                                                            title="{{ __('Edit') }}"
                                                            data-title="{{ __('Lead Edit') }}">
                                                                <i class="ti ti-pencil text-white" style="font-size: 16px;"></i>
                                                            </a>
                                                        </div>
                                                        @endcan
                                                        @can('delete lead')
                                                        <div class="action-btn" style="margin-left: 15px;">
                                                            {!! Form::open(['method' => 'DELETE', 'route' => ['leads.destroy', $lead->id], 'id' => 'delete-form-' . $lead->id]) !!}
                                                                <a href="#"
                                                                onclick="event.preventDefault(); document.getElementById('delete-form-{{ $lead->id }}').submit();"
                                                                class="mx-3 btn btn-sm d-flex align-items-center justify-content-center"
                                                                style="
                                                                    border: 1px solid #dc3545;
                                                                    background-color: transparent;
                                                                    border-radius: 8px;
                                                                    padding: 8px 12px;
                                                                    transition: all 0.3s ease;
                                                                "
                                                                onmouseover="this.style.backgroundColor='#dc3545'; this.querySelector('i').style.color='#fff'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                                onmouseout="this.style.backgroundColor='transparent'; this.querySelector('i').style.color='#14532d'; this.style.boxShadow='none'"
                                                                data-bs-toggle="tooltip"
                                                                title="{{ __('Delete') }}">
                                                                    <i class="ti ti-trash" style="font-size: 16px; color: #14532d;"></i>
                                                                </a>
                                                            {!! Form::close() !!}
                                                        </div>
                                                        @endcan
                                                    </span>
                                                </td>
                                            @endif
                                        </tr>
                                        <!-- Activity Modal for this lead -->
                                        <div class="modal fade" id="activityModal-{{ $lead->id }}" tabindex="-1" aria-labelledby="activityModalLabel-{{ $lead->id }}" aria-hidden="true">
                                            <div class="modal-dialog modal-lg modal-dialog-centered">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="activityModalLabel-{{ $lead->id }}">{{ __('Activity') }}</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="row leads-scroll">
                                                            <ul class="event-cards list-group list-group-flush mt-3 w-100">
                                                                @if (!$lead->activities->isEmpty())
                                                                    @foreach ($lead->activities as $activity)
                                                                        <li class="list-group-item card mb-3">
                                                                            <div class="row align-items-center justify-content-between">
                                                                                <div class="col-auto mb-3 mb-sm-0">
                                                                                    <div class="d-flex align-items-center">
                                                                                        <div class="theme-avtar bg-primary badge">
                                                                                            <i class="ti {{ $activity->logIcon() }}"></i>
                                                                                        </div>
                                                                                        <div class="ms-3">
                                                                                            <span class="text-dark text-sm">{{ __($activity->log_type) }}</span>
                                                                                            <h6 class="m-0">{!! $activity->getLeadRemark() !!}</h6>
                                                                                            <small class="text-muted">{{ $activity->created_at->diffForHumans() }}</small>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </li>
                                                                    @endforeach
                                                                @else
                                                                    <li class="text-center py-4">No activity found yet.</li>
                                                                @endif
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <tr class="font-style no-data-row">
                                        <td colspan="7" class="text-center">{{ __('No data available in table') }}</td>
                                    </tr>
                                @endif

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Communication Modal -->
    <div class="modal fade" id="communication-modal" tabindex="-1" aria-labelledby="communication-modal-label" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="communication-modal-label">Communication Options</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="communication-options-list">
                        <a href="#" class="communication-option-item" id="whatsapp-option">
                            <i class="fab fa-whatsapp"></i>
                            <span>WhatsApp</span>
                        </a>
                        <a href="#" class="communication-option-item" id="default-email-option">
                            <i class="fas fa-envelope-open-text"></i>
                            <span>Default Email App</span>
                        </a>
                        <a href="#" class="communication-option-item" id="cloud-email-option">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <span>Cloud Email Service</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sources Modal -->
    <div class="modal fade" id="sources-modal" tabindex="-1" aria-labelledby="sources-modal-label" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="sources-modal-label">{{ __('Lead Sources') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="list-group" id="sources-list">
                        <!-- Sources will be dynamically inserted here -->
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Off-canvas for List View -->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="filterOffcanvas" aria-labelledby="filterOffcanvasLabel">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="filterOffcanvasLabel">{{__('Filter Leads')}}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            {{ Form::open(['id' => 'lead-filter-form', 'onsubmit' => 'return handleFilterSubmit(this)']) }}

            <div class="mb-3">
                {{ Form::label('name', __('Name'), ['class' => 'form-label']) }}
                {{ Form::text('name', null, ['class' => 'form-control', 'placeholder' => __('Enter lead name')]) }}
            </div>

            <div class="mb-3">
                {{ Form::label('email', __('Email'), ['class' => 'form-label']) }}
                {{ Form::email('email', null, ['class' => 'form-control', 'placeholder' => __('Enter email address')]) }}
            </div>

            <div class="mb-3">
                {{ Form::label('stage_id', __('Stage'), ['class' => 'form-label']) }}
                {{ Form::select('stage_id', $stages, null, ['class' => 'form-control select', 'placeholder' => __('Select Stage')]) }}
            </div>

            <div class="d-grid gap-2">
                <input type="submit" value="{{ __('Apply Filter') }}" class="btn btn-primary">
                <input type="button" value="{{ __('Clear Filters') }}" class="btn btn-secondary" onclick="clearFilters()">
            </div>

            {{ Form::close() }}
        </div>
    </div>

@endsection