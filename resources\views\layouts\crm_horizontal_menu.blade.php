<div class="crm-horizontal-menu-wrapper mb-4">
    <nav class="crm-horizontal-menu navbar navbar-expand-lg navbar-light bg-white shadow-sm rounded py-2 px-3">
        <div class="container-fluid p-0">
            <ul class="navbar-nav flex-row flex-wrap w-100 justify-content-between justify-content-md-start gap-2 gap-md-3">
                <li class="nav-item">
                    <a class="nav-link fw-semibold px-3 py-2 @if (Request::route()->getName() == 'pipelines.index') active text-primary @else text-dark @endif" href="{{ route('pipelines.index') }}">
                        <i class="ti ti-git-branch me-1"></i> {{ __('Pipeline') }}
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link fw-semibold px-3 py-2 @if (Request::route()->getName() == 'lead_stages.index') active text-primary @else text-dark @endif" href="{{ route('lead_stages.index') }}">
                        <i class="ti ti-list-numbers me-1"></i> {{ __('Lead Stages') }}
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link fw-semibold px-3 py-2 @if (Request::route()->getName() == 'stages.index') active text-primary @else text-dark @endif" href="{{ route('stages.index') }}">
                        <i class="ti ti-list-details me-1"></i> {{ __('Deal Stages') }}
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link fw-semibold px-3 py-2 @if (Request::route()->getName() == 'sources.index') active text-primary @else text-dark @endif" href="{{ route('sources.index') }}">
                        <i class="ti ti-source-code me-1"></i> {{ __('Sources') }}
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link fw-semibold px-3 py-2 @if (Request::route()->getName() == 'labels.index') active text-primary @else text-dark @endif" href="{{ route('labels.index') }}">
                        <i class="ti ti-tag me-1"></i> {{ __('Labels') }}
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link fw-semibold px-3 py-2 @if (Request::route()->getName() == 'crmCustomField.index') active text-primary @else text-dark @endif" href="{{ route('crmCustomField.index') }}">
                        <i class="ti ti-settings me-1"></i> {{ __('Custom Fields') }}
                    </a>
                </li>
            </ul>
        </div>
    </nav>
</div>

<style>
.crm-horizontal-menu-wrapper {
    width: 100%;
}
.crm-horizontal-menu .nav-link {
    border-radius: 0.5rem;
    transition: background 0.2s, color 0.2s;
    font-size: 1rem;
    display: flex;
    align-items: center;
}
.crm-horizontal-menu .nav-link.active, .crm-horizontal-menu .nav-link:hover {
    background: #f0f4fa;
    color: #0d6efd !important;
}
@media (max-width: 767.98px) {
    .crm-horizontal-menu .navbar-nav {
        flex-direction: column !important;
        gap: 0.5rem !important;
    }
    .crm-horizontal-menu .nav-link {
        width: 100%;
        justify-content: flex-start;
    }
}
</style> 