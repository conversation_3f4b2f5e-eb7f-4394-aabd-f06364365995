@extends('layouts.admin')

@section('page-title')
    {{ __('Lead Report') }}
@endsection

@section('content')
<style>
    .pdf-container {
        background: white;
        padding: 30px;
        margin: 20px auto;
        max-width: 1200px;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    .pdf-header {
        text-align: center;
        margin-bottom: 30px;
        border-bottom: 2px solid #007bff;
        padding-bottom: 20px;
    }
    .pdf-title {
        font-size: 28px;
        font-weight: bold;
        color: #333;
        margin-bottom: 10px;
    }
    .pdf-subtitle {
        font-size: 16px;
        color: #666;
        margin-bottom: 5px;
    }
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    .stat-card {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border-left: 4px solid #007bff;
    }
    .stat-title {
        font-size: 14px;
        color: #666;
        margin-bottom: 5px;
    }
    .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #333;
    }
    .chart-section {
        margin-bottom: 30px;
        page-break-inside: avoid;
    }
    .chart-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 15px;
        color: #333;
    }
    .data-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 15px;
    }
    .data-table th,
    .data-table td {
        padding: 8px 12px;
        border: 1px solid #ddd;
        text-align: left;
    }
    .data-table th {
        background-color: #f8f9fa;
        font-weight: bold;
    }
    @media print {
        .pdf-container {
            box-shadow: none;
            margin: 0;
            padding: 20px;
        }
    }
</style>

<div class="pdf-container">
    <div class="pdf-header">
        <div class="pdf-title">{{ __('Lead Report') }}</div>
        <div class="pdf-subtitle">{{ __('Generated on') }}: {{ date('F j, Y \a\t g:i A') }}</div>
        <div class="pdf-subtitle">{{ __('Report Period') }}: {{ $filter['startDateRange'] ?? 'All Time' }} - {{ $filter['endDateRange'] ?? 'Current' }}</div>
    </div>

    <!-- Summary Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-title">{{ __('Total Leads This Week') }}</div>
            <div class="stat-value">{{ array_sum($devicearray['data']) }}</div>
        </div>
        <div class="stat-card">
            <div class="stat-title">{{ __('Lead Sources') }}</div>
            <div class="stat-value">{{ count($leadsourceName) }}</div>
        </div>
        <div class="stat-card">
            <div class="stat-title">{{ __('Active Pipelines') }}</div>
            <div class="stat-value">{{ count($leadpipelineName) }}</div>
        </div>
        <div class="stat-card">
            <div class="stat-title">{{ __('Team Members') }}</div>
            <div class="stat-value">{{ count($leaduserName) }}</div>
        </div>
    </div>

    <!-- Weekly Lead Distribution -->
    <div class="chart-section">
        <div class="chart-title">{{ __('Weekly Lead Distribution') }}</div>
        <table class="data-table">
            <thead>
                <tr>
                    <th>{{ __('Date') }}</th>
                    <th>{{ __('Leads Count') }}</th>
                </tr>
            </thead>
            <tbody>
                @foreach($devicearray['label'] as $index => $date)
                <tr>
                    <td>{{ date('M j, Y', strtotime($date)) }}</td>
                    <td>{{ $devicearray['data'][$index] ?? 0 }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Lead Sources Analysis -->
    <div class="chart-section">
        <div class="chart-title">{{ __('Lead Sources Analysis') }}</div>
        <table class="data-table">
            <thead>
                <tr>
                    <th>{{ __('Source') }}</th>
                    <th>{{ __('Leads Count') }}</th>
                    <th>{{ __('Percentage') }}</th>
                </tr>
            </thead>
            <tbody>
                @php $totalSourceLeads = array_sum($leadsourceeData); @endphp
                @foreach($leadsourceName as $index => $source)
                <tr>
                    <td>{{ $source }}</td>
                    <td>{{ $leadsourceeData[$index] ?? 0 }}</td>
                    <td>{{ $totalSourceLeads > 0 ? round(($leadsourceeData[$index] ?? 0) / $totalSourceLeads * 100, 1) : 0 }}%</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Team Performance -->
    <div class="chart-section">
        <div class="chart-title">{{ __('Team Performance') }}</div>
        <table class="data-table">
            <thead>
                <tr>
                    <th>{{ __('Team Member') }}</th>
                    <th>{{ __('Leads Handled') }}</th>
                    <th>{{ __('Performance') }}</th>
                </tr>
            </thead>
            <tbody>
                @php $totalUserLeads = array_sum($leadusereData); @endphp
                @foreach($leaduserName as $index => $user)
                <tr>
                    <td>{{ $user }}</td>
                    <td>{{ $leadusereData[$index] ?? 0 }}</td>
                    <td>
                        @php 
                            $percentage = $totalUserLeads > 0 ? round(($leadusereData[$index] ?? 0) / $totalUserLeads * 100, 1) : 0;
                        @endphp
                        {{ $percentage }}%
                        @if($percentage >= 25)
                            <span style="color: #28a745;">{{ __('Excellent') }}</span>
                        @elseif($percentage >= 15)
                            <span style="color: #ffc107;">{{ __('Good') }}</span>
                        @else
                            <span style="color: #dc3545;">{{ __('Needs Improvement') }}</span>
                        @endif
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Pipeline Distribution -->
    <div class="chart-section">
        <div class="chart-title">{{ __('Pipeline Distribution') }}</div>
        <table class="data-table">
            <thead>
                <tr>
                    <th>{{ __('Pipeline') }}</th>
                    <th>{{ __('Leads Count') }}</th>
                    <th>{{ __('Distribution') }}</th>
                </tr>
            </thead>
            <tbody>
                @php $totalPipelineLeads = array_sum($leadpipelineeData); @endphp
                @foreach($leadpipelineName as $index => $pipeline)
                <tr>
                    <td>{{ $pipeline }}</td>
                    <td>{{ $leadpipelineeData[$index] ?? 0 }}</td>
                    <td>{{ $totalPipelineLeads > 0 ? round(($leadpipelineeData[$index] ?? 0) / $totalPipelineLeads * 100, 1) : 0 }}%</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Monthly Trend -->
    <div class="chart-section">
        <div class="chart-title">{{ __('Monthly Lead Trend') }}</div>
        <table class="data-table">
            <thead>
                <tr>
                    <th>{{ __('Month') }}</th>
                    <th>{{ __('Leads Count') }}</th>
                    <th>{{ __('Growth') }}</th>
                </tr>
            </thead>
            <tbody>
                @foreach($labels as $index => $month)
                <tr>
                    <td>{{ $month }}</td>
                    <td>{{ $data[$index] ?? 0 }}</td>
                    <td>
                        @if($index > 0)
                            @php 
                                $current = $data[$index] ?? 0;
                                $previous = $data[$index - 1] ?? 0;
                                $growth = $previous > 0 ? round(($current - $previous) / $previous * 100, 1) : 0;
                            @endphp
                            @if($growth > 0)
                                <span style="color: #28a745;">+{{ $growth }}%</span>
                            @elseif($growth < 0)
                                <span style="color: #dc3545;">{{ $growth }}%</span>
                            @else
                                <span style="color: #6c757d;">0%</span>
                            @endif
                        @else
                            <span style="color: #6c757d;">-</span>
                        @endif
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div style="margin-top: 40px; text-align: center; color: #666; font-size: 12px;">
        {{ __('Report generated by') }} {{ config('app.name') }} {{ __('on') }} {{ date('Y-m-d H:i:s') }}
    </div>
</div>

<!-- Hidden filename input for PDF generation -->
<input type="hidden" id="filename" value="Lead_Report_{{ date('Y-m-d_H-i-s') }}">

<script src="{{ asset('js/jquery.min.js') }}"></script>
<script type="text/javascript" src="{{ asset('js/html2pdf.bundle.min.js') }}"></script>
<script>
    function closeScript() {
        setTimeout(function () {
            window.open(window.location, '_self').close();
        }, 1000);
    }

    $(window).on('load', function () {
        var element = document.getElementsByClassName('pdf-container')[0];
        var filename = $('#filename').val();

        var opt = {
            margin: [0.5, 0, 0.5, 0],
            filename: filename,
            image: {type: 'jpeg', quality: 1},
            html2canvas: {scale: 2, dpi: 72, letterRendering: true},
            jsPDF: {unit: 'in', format: 'A4'}
        };

        html2pdf().set(opt).from(element).save().then(closeScript);
    });
</script>

@endsection
