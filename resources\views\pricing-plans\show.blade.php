@extends('layouts.admin')

@section('page-title')
    {{ __('Pricing Plan Details') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('system-admin.dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ route('pricing-plans.index') }}">{{ __('Pricing Plans') }}</a></li>
    <li class="breadcrumb-item">{{ $pricingPlan->name }}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        <a href="{{ route('pricing-plans.edit', $pricingPlan) }}" class="btn btn-sm btn-primary">
            <i class="ti ti-pencil"></i> {{ __('Edit Plan') }}
        </a>
        <a href="{{ route('pricing-plans.index') }}" class="btn btn-sm btn-secondary">
            <i class="ti ti-arrow-left"></i> {{ __('Back to List') }}
        </a>
    </div>
@endsection

@section('content')
    <div class="row">
        <!-- Plan Overview -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('Plan Overview') }}</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <h3 class="text-primary">{{ $pricingPlan->name }}</h3>
                        @if($pricingPlan->is_featured)
                            <span class="badge bg-warning mb-2">
                                <i class="ti ti-star"></i> {{ __('Featured') }}
                            </span>
                        @endif
                        <div class="price-display">
                            <h2 class="text-success">${{ number_format($pricingPlan->price, 2) }}</h2>
                            <small class="text-muted">{{ ucfirst($pricingPlan->duration) }}</small>
                        </div>
                        <span class="badge bg-{{ $pricingPlan->status === 'active' ? 'success' : 'danger' }}">
                            {{ ucfirst($pricingPlan->status) }}
                        </span>
                    </div>

                    @if($pricingPlan->description)
                        <div class="mb-3">
                            <h6>{{ __('Description') }}</h6>
                            <p class="text-muted">{{ $pricingPlan->description }}</p>
                        </div>
                    @endif

                    <div class="plan-limits">
                        <h6>{{ __('Plan Limits') }}</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="ti ti-users text-primary me-2"></i>
                                <strong>{{ __('Users:') }}</strong> 
                                {{ $pricingPlan->max_users == -1 ? __('Unlimited') : number_format($pricingPlan->max_users) }}
                            </li>
                            <li class="mb-2">
                                <i class="ti ti-user-check text-primary me-2"></i>
                                <strong>{{ __('Customers:') }}</strong> 
                                {{ $pricingPlan->max_customers == -1 ? __('Unlimited') : number_format($pricingPlan->max_customers) }}
                            </li>
                            <li class="mb-2">
                                <i class="ti ti-building-store text-primary me-2"></i>
                                <strong>{{ __('Vendors:') }}</strong> 
                                {{ $pricingPlan->max_vendors == -1 ? __('Unlimited') : number_format($pricingPlan->max_vendors) }}
                            </li>
                            <li class="mb-2">
                                <i class="ti ti-briefcase text-primary me-2"></i>
                                <strong>{{ __('Clients:') }}</strong> 
                                {{ $pricingPlan->max_clients == -1 ? __('Unlimited') : number_format($pricingPlan->max_clients) }}
                            </li>
                            <li class="mb-2">
                                <i class="ti ti-database text-primary me-2"></i>
                                <strong>{{ __('Storage:') }}</strong> 
                                {{ $pricingPlan->storage_limit == -1 ? __('Unlimited') : number_format($pricingPlan->storage_limit, 1) . ' GB' }}
                            </li>
                        </ul>
                    </div>

                    <div class="plan-meta mt-3">
                        <small class="text-muted">
                            <strong>{{ __('Created:') }}</strong> {{ $pricingPlan->created_at->format('M d, Y') }}<br>
                            <strong>{{ __('Updated:') }}</strong> {{ $pricingPlan->updated_at->format('M d, Y') }}<br>
                            <strong>{{ __('Sort Order:') }}</strong> {{ $pricingPlan->sort_order }}
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Module Permissions -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('Module Permissions') }}</h5>
                </div>
                <div class="card-body">
                    @if($pricingPlan->module_permissions && count($pricingPlan->module_permissions) > 0)
                        @foreach($pricingPlan->module_permissions as $moduleKey => $permissions)
                            @php
                                $moduleData = $availableModules[$moduleKey] ?? null;
                            @endphp
                            
                            @if($moduleData)
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">
                                            <i class="ti ti-check-circle text-success me-2"></i>
                                            {{ $moduleData['name'] }}
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            @foreach($permissions as $permission)
                                                <div class="col-md-4 mb-2">
                                                    <div class="d-flex align-items-center">
                                                        <i class="ti ti-check text-success me-2"></i>
                                                        <span>{{ ucfirst($permission) }}</span>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <i class="ti ti-info-circle text-muted" style="font-size: 3rem;"></i>
                            <h5 class="text-muted mt-2">{{ __('No Module Permissions') }}</h5>
                            <p class="text-muted">{{ __('This plan does not have any module permissions assigned.') }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Available Modules (Not Included) -->
            @php
                $includedModules = array_keys($pricingPlan->module_permissions ?? []);
                $availableModuleKeys = array_keys($availableModules);
                $notIncludedModules = array_diff($availableModuleKeys, $includedModules);
            @endphp

            @if(count($notIncludedModules) > 0)
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>{{ __('Modules Not Included') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach($notIncludedModules as $moduleKey)
                                @php
                                    $moduleData = $availableModules[$moduleKey];
                                @endphp
                                <div class="col-md-6 mb-2">
                                    <div class="d-flex align-items-center">
                                        <i class="ti ti-x text-danger me-2"></i>
                                        <span class="text-muted">{{ $moduleData['name'] }}</span>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
@endsection

@push('style-page')
    <style>
        .price-display h2 {
            font-size: 2.5rem;
            font-weight: bold;
        }
        
        .plan-limits ul li {
            padding: 0.25rem 0;
        }
        
        .card-header.bg-light {
            background-color: #f8f9fa !important;
        }
    </style>
@endpush
