<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ModuleIntegration extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'base_url',
        'sync_endpoint',
        'sso_endpoint',
        'api_token',
        'enabled'
    ];

    protected $casts = [
        'enabled' => 'boolean'
    ];

    /**
     * Get the full sync URL
     */
    public function getSyncUrlAttribute()
    {
        return rtrim($this->base_url, '/') . '/' . ltrim($this->sync_endpoint, '/');
    }

    /**
     * Get the full SSO URL
     */
    public function getSsoUrlAttribute()
    {
        return rtrim($this->base_url, '/') . '/' . ltrim($this->sso_endpoint, '/');
    }

    /**
     * Scope to get only enabled modules
     */
    public function scopeEnabled($query)
    {
        return $query->where('enabled', true);
    }
}
