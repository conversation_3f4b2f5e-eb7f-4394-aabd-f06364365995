document.addEventListener('DOMContentLoaded', function () {
    const form = document.getElementById('finalBookingForm');
    if (!form) return;

    form.addEventListener('submit', function (e) {
        e.preventDefault();

        const name = document.getElementById('booking_name').value;
        const email = document.getElementById('booking_email').value;
        const phone = document.getElementById('booking_phone').value;
        const event = document.getElementById('booking_event').textContent;
        const date = document.getElementById('selected_date').value;
        const time = document.getElementById('selected_time').value;
        const location = document.getElementById('booking_location').textContent;
        const duration = document.getElementById('booking_duration').textContent;

        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        fetch('/bookings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            },
            body: JSON.stringify({
                name,
                email,
                phone,
                event,
                date,
                time,
                location,
                duration
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ Booking submitted successfully!');
                document.getElementById('bookingFormContainer').classList.add('d-none');
                form.reset();
            } else {
                alert('❌ Booking failed. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('⚠️ An unexpected error occurred.');
        });
    });
});
