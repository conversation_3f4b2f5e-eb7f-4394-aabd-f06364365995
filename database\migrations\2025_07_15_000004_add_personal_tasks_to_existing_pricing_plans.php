<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\PricingPlan;

class AddPersonalTasksToExistingPricingPlans extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Get all existing pricing plans
        $pricingPlans = PricingPlan::all();
        
        // Personal tasks permissions
        $personalTasksPermissions = [
            'manage personal task',
            'create personal task',
            'edit personal task',
            'delete personal task',
            'view personal task',
            'create personal task comment',
            'edit personal task comment',
            'delete personal task comment',
            'create personal task file',
            'delete personal task file',
            'create personal task checklist',
            'edit personal task checklist',
            'delete personal task checklist',
            'manage personal task time tracking',
        ];
        
        foreach ($pricingPlans as $plan) {
            $modulePermissions = $plan->module_permissions ?? [];
            
            // Add personal tasks module to existing plans
            $modulePermissions['personal_tasks'] = $personalTasksPermissions;
            
            $plan->update([
                'module_permissions' => $modulePermissions
            ]);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Get all existing pricing plans
        $pricingPlans = PricingPlan::all();
        
        foreach ($pricingPlans as $plan) {
            $modulePermissions = $plan->module_permissions ?? [];
            
            // Remove personal tasks module from existing plans
            unset($modulePermissions['personal_tasks']);
            
            $plan->update([
                'module_permissions' => $modulePermissions
            ]);
        }
    }
}
