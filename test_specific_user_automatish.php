<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\PricingPlan;
use App\Models\Plan;
use App\Models\ModuleIntegration;
use Illuminate\Support\Facades\Gate;

// Initialize Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Testing Specific User: <EMAIL> ===\n\n";

try {
    // Find the specific user
    $user = User::where('email', '<EMAIL>')->first();
    
    if (!$user) {
        echo "❌ User <EMAIL> not found\n";
        exit;
    }

    echo "✅ Found user: {$user->name} ({$user->email})\n";
    echo "   - User ID: {$user->id}\n";
    echo "   - User Type: {$user->type}\n";
    echo "   - Plan ID: {$user->plan}\n";
    echo "   - Created By: {$user->created_by}\n\n";

    // Test 1: Check user's pricing plan
    echo "1. Checking user's pricing plan...\n";
    $pricingPlan = $user->pricingPlan;
    if ($pricingPlan) {
        echo "✅ User has NEW pricing plan: {$pricingPlan->name} (ID: {$pricingPlan->id})\n";
        echo "   - Module permissions: " . json_encode($pricingPlan->module_permissions, JSON_PRETTY_PRINT) . "\n";
        
        $hasAutomatishInPlan = isset($pricingPlan->module_permissions['automatish']);
        echo "   - Has automatish module: " . ($hasAutomatishInPlan ? 'YES' : 'NO') . "\n";
        
        if ($hasAutomatishInPlan) {
            $automatishPermissions = $pricingPlan->module_permissions['automatish'];
            echo "   - Automatish permissions: " . json_encode($automatishPermissions) . "\n";
            $hasAccessPermission = in_array('access automatish', $automatishPermissions);
            echo "   - Has 'access automatish' permission: " . ($hasAccessPermission ? 'YES' : 'NO') . "\n";
        }
    } else {
        echo "❌ User does NOT have a NEW pricing plan\n";
    }
    echo "\n";

    // Test 2: Check old plan system
    echo "2. Checking old plan system...\n";
    $oldPlan = $user->currentPlan;
    if ($oldPlan) {
        echo "✅ User has OLD plan: {$oldPlan->name} (ID: {$oldPlan->id})\n";
        echo "   - Automatish enabled: " . ($oldPlan->automatish == 1 ? 'YES' : 'NO') . "\n";
    } else {
        echo "❌ User does NOT have an OLD plan\n";
    }
    echo "\n";

    // Test 3: Check module permissions column
    echo "3. Checking user's module_permissions column...\n";
    if (!empty($user->module_permissions)) {
        echo "✅ User has module_permissions: " . json_encode($user->module_permissions, JSON_PRETTY_PRINT) . "\n";
        $hasAutomatishInColumn = isset($user->module_permissions['automatish']);
        echo "   - Has automatish in module_permissions: " . ($hasAutomatishInColumn ? 'YES' : 'NO') . "\n";
    } else {
        echo "❌ User does NOT have module_permissions set\n";
    }
    echo "\n";

    // Test 4: Test hasModulePermission method
    echo "4. Testing hasModulePermission method...\n";
    $hasModulePermission = $user->hasModulePermission('automatish', 'access automatish');
    echo "   - hasModulePermission('automatish', 'access automatish'): " . ($hasModulePermission ? 'YES' : 'NO') . "\n\n";

    // Test 5: Test Gate permission
    echo "5. Testing Gate permission...\n";
    $hasGateAccess = Gate::forUser($user)->check('access automatish');
    echo "   - Gate::check('access automatish'): " . ($hasGateAccess ? 'YES' : 'NO') . "\n\n";

    // Test 6: Check sidebar conditions
    echo "6. Testing sidebar conditions...\n";
    
    // Check if user type qualifies for company section
    $isCompanySection = $user->type != 'super admin' && $user->type != 'system admin' && $user->type != 'staff';
    echo "   - Qualifies for company section (not super admin/system admin/staff): " . ($isCompanySection ? 'YES' : 'NO') . "\n";
    
    // Check if user type qualifies for super admin section
    $isSuperAdminSection = $user->type == 'super admin';
    echo "   - Qualifies for super admin section: " . ($isSuperAdminSection ? 'YES' : 'NO') . "\n";
    
    // Check module integration
    $automatishModule = ModuleIntegration::where('name', 'Automatish')->where('enabled', true)->first();
    echo "   - Automatish module enabled: " . ($automatishModule ? 'YES' : 'NO') . "\n";
    echo "   - SSO endpoint configured: " . ($automatishModule && $automatishModule->sso_endpoint ? 'YES' : 'NO') . "\n";
    
    // Overall sidebar visibility
    $shouldShowInSidebar = $hasGateAccess && $automatishModule && $automatishModule->sso_endpoint;
    echo "   - Should show in sidebar: " . ($shouldShowInSidebar ? 'YES' : 'NO') . "\n\n";

    // Test 7: Summary and explanation
    echo "7. Summary and explanation...\n";
    if ($hasGateAccess) {
        echo "✅ User CAN access automatish because:\n";
        
        if ($user->type === 'system admin') {
            echo "   - User is a SYSTEM ADMIN (always has access)\n";
        } elseif ($user->type === 'super admin' && !empty($user->module_permissions)) {
            echo "   - User is a SUPER ADMIN with module_permissions set\n";
        } elseif ($pricingPlan && isset($pricingPlan->module_permissions['automatish'])) {
            echo "   - User has automatish in their PRICING PLAN\n";
        } elseif ($oldPlan && $oldPlan->automatish == 1) {
            echo "   - User has automatish enabled in their OLD PLAN\n";
        } else {
            echo "   - UNKNOWN REASON (this shouldn't happen)\n";
        }
    } else {
        echo "❌ User CANNOT access automatish because:\n";
        echo "   - User does not have automatish permissions in any system\n";
    }

} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Test Complete ===\n";
