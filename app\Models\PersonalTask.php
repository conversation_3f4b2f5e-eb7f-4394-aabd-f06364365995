<?php

namespace App\Models;

use App\Models\User;
use App\Models\TaskStage;
use Illuminate\Database\Eloquent\Model;

class PersonalTask extends Model
{
    protected $fillable = [
        'name',
        'description',
        'estimated_hrs',
        'start_date',
        'end_date',
        'priority',
        'priority_color',
        'assign_to',
        'stage_id',
        'order',
        'created_by',
        'is_favourite',
        'is_complete',
        'marked_at',
        'progress',
        'status',
    ];

    public static $priority = [
        'critical' => 'Critical',
        'high' => 'High',
        'medium' => 'Medium',
        'low' => 'Low',
    ];

    public static $priority_color = [
        'critical' => 'danger',
        'high' => 'warning',
        'medium' => 'primary',
        'low' => 'info',
    ];

    /**
     * Get the users assigned to this task
     */
    public function users()
    {
        if (empty($this->assign_to)) {
            return collect();
        }
        return User::whereIn('id', explode(',', $this->assign_to))->get();
    }

    /**
     * Get the task stage
     */
    public function stage()
    {
        return $this->hasOne('App\Models\TaskStage', 'id', 'stage_id');
    }

    /**
     * Get the task creator
     */
    public function createdBy()
    {
        return $this->hasOne('App\Models\User', 'id', 'created_by');
    }

    /**
     * Get task comments
     */
    public function comments()
    {
        return $this->hasMany('App\Models\PersonalTaskComment', 'task_id', 'id')->orderBy('id', 'DESC');
    }

    /**
     * Get task files
     */
    public function taskFiles()
    {
        return $this->hasMany('App\Models\PersonalTaskFile', 'task_id', 'id')->orderBy('id', 'DESC');
    }

    /**
     * Get task checklists
     */
    public function taskCheckList()
    {
        return $this->hasMany('App\Models\PersonalTaskChecklist', 'task_id', 'id')->orderBy('id', 'DESC');
    }

    /**
     * Get task time tracking records
     */
    public function timeTracking()
    {
        return $this->hasMany('App\Models\PersonalTaskTimeTracking', 'task_id', 'id')->orderBy('id', 'DESC');
    }

    /**
     * Check if user is assigned to this task
     */
    public function isAssignedTo($userId)
    {
        if (empty($this->assign_to)) {
            return false;
        }
        return in_array($userId, explode(',', $this->assign_to));
    }

    /**
     * Get all users for assignment dropdown
     */
    public static function getUsers()
    {
        $data = [];
        $users = User::where('created_by', \Auth::user()->creatorId())
                    ->where('type', '!=', 'client')
                    ->get();
        
        foreach ($users as $user) {
            $data[$user->id]['id'] = $user->id;
            $data[$user->id]['name'] = $user->name;
            $data[$user->id]['avatar'] = $user->avatar;
        }
        
        return $data;
    }

    /**
     * Get task progress percentage
     */
    public function getProgressPercentage()
    {
        $totalChecklist = $this->taskCheckList()->count();
        if ($totalChecklist == 0) {
            return $this->progress;
        }
        
        $completedChecklist = $this->taskCheckList()->where('status', 1)->count();
        return round(($completedChecklist / $totalChecklist) * 100);
    }

    /**
     * Get total time spent on task
     */
    public function getTotalTimeSpent()
    {
        return $this->timeTracking()->sum('total_time');
    }
}
