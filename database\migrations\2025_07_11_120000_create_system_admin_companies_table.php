<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create system admin companies table
        Schema::create('system_admin_companies', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->text('address')->nullable();
            $table->string('logo')->nullable();
            $table->boolean('is_active')->default(true);
            $table->unsignedBigInteger('created_by'); // The system admin who created this company
            $table->timestamps();

            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
        });

        // Add company_id to users table for system admin staff
        Schema::table('users', function (Blueprint $table) {
            $table->unsignedBigInteger('system_admin_company_id')->nullable()->after('type');
            $table->foreign('system_admin_company_id')->references('id')->on('system_admin_companies')->onDelete('set null');
        });

        // Create a default company for existing system admins
        $this->createDefaultCompaniesForExistingSystemAdmins();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['system_admin_company_id']);
            $table->dropColumn('system_admin_company_id');
        });

        Schema::dropIfExists('system_admin_companies');
    }

    /**
     * Create default companies for existing system admins
     */
    private function createDefaultCompaniesForExistingSystemAdmins()
    {
        $systemAdmins = User::where('type', 'system admin')->get();

        foreach ($systemAdmins as $systemAdmin) {
            // Create a default company for each system admin
            $company = \DB::table('system_admin_companies')->insertGetId([
                'name' => $systemAdmin->company_name ?? ($systemAdmin->name . ' Company'),
                'description' => $systemAdmin->company_description ?? 'Default company for ' . $systemAdmin->name,
                'email' => $systemAdmin->email,
                'is_active' => true,
                'created_by' => $systemAdmin->id,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // Update the system admin to be associated with their company
            $systemAdmin->update(['system_admin_company_id' => $company]);

            // Update any existing super admins created by this system admin to be part of the same company
            User::where('type', 'super admin')
                ->where('created_by', $systemAdmin->id)
                ->update(['system_admin_company_id' => $company]);
        }
    }
};
