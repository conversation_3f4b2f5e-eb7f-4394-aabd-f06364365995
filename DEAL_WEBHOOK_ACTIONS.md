# Deal Webhook Actions Documentation

This document outlines the new deal-related webhook actions that have been added to the CRM system, following the same pattern as the existing CRM and booking webhook implementations.

## New Deal Webhook Actions

The following new webhook actions have been added to `app/Constants/CrmWebhookActions.php`:

### 1. Deal Assignment
- **Action**: `crm.deal_assigned`
- **Triggers**: When a deal is assigned to a user
- **Data**: Deal information + assigned user ID + previous user ID (if any)

### 2. Deal Deadline Approaching
- **Action**: `crm.deal_deadline_approaching`
- **Triggers**: When a deal's deadline is approaching (configurable days)
- **Data**: Deal information + days until deadline + deadline date

### 3. Deal Inactive for X Days
- **Action**: `crm.deal_inactive_for_days`
- **Triggers**: When a deal hasn't been updated for a specified number of days
- **Data**: Deal information + number of inactive days + last activity date

### 4. Deal Notes Added
- **Action**: `crm.deal_notes_added`
- **Triggers**: When notes are added or updated on a deal
- **Data**: Deal information + note content + note ID

### 5. Deal Documents Uploaded
- **Action**: `crm.deal_documents_uploaded`
- **Triggers**: When documents/files are uploaded to a deal
- **Data**: Deal information + document details (ID, name, type)

## Implementation Details

### Constants Added
```php
// In app/Constants/CrmWebhookActions.php
const DEAL_ASSIGNED = 'crm.deal_assigned';
const DEAL_DEADLINE_APPROACHING = 'crm.deal_deadline_approaching';
const DEAL_INACTIVE_FOR_DAYS = 'crm.deal_inactive_for_days';
const DEAL_NOTES_ADDED = 'crm.deal_notes_added';
const DEAL_DOCUMENTS_UPLOADED = 'crm.deal_documents_uploaded';
```

### Dispatcher Methods Added
```php
// In app/Services/CrmWebhookDispatcher.php

// Deal assignment
public function dispatchDealAssigned($deal, $assignedUserId, $previousUserId = null, $additionalData = [])

// Deadline approaching
public function dispatchDealDeadlineApproaching($deal, $daysUntilDeadline, $additionalData = [])

// Inactive deals
public function dispatchDealInactiveForDays($deal, $inactiveDays, $additionalData = [])

// Notes added
public function dispatchDealNotesAdded($deal, $note, $additionalData = [])

// Documents uploaded
public function dispatchDealDocumentsUploaded($deal, $document, $additionalData = [])
```

## Usage Examples

### 1. Deal Assignment (Already Integrated)
```php
// In DealController@userUpdate
$webhookDispatcher = new CrmWebhookDispatcher();
$webhookDispatcher->dispatchDealAssigned($deal, $userId);
```

### 2. Deal Update with Change Tracking (Already Integrated)
```php
// In DealController@update
if (!empty($changes)) {
    $webhookDispatcher = new CrmWebhookDispatcher();
    $webhookDispatcher->dispatchDealUpdated($deal, $changes);
    
    // Check if stage changed
    if (isset($changes['stage_id'])) {
        $webhookDispatcher->dispatchDealStageChanged(
            $deal, 
            $changes['stage_id']['old'], 
            $changes['stage_id']['new']
        );
    }
}
```

### 3. Notes Added (Already Integrated)
```php
// In DealController@noteStore
if ($oldNotes !== $request->notes) {
    $webhookDispatcher = new CrmWebhookDispatcher();
    $webhookDispatcher->dispatchDealNotesAdded($deal, $request->notes);
}
```

### 4. Document Upload (Already Integrated)
```php
// In DealController@fileUpload
$webhookDispatcher = new CrmWebhookDispatcher();
$webhookDispatcher->dispatchDealDocumentsUploaded($deal, $file);
```

### 5. Deadline Approaching (Requires Scheduled Task)
```php
// Example usage in a scheduled command or service
$dealsWithApproachingDeadlines = Deal::where('deadline', '<=', now()->addDays(3))
    ->where('deadline', '>', now())
    ->get();

foreach ($dealsWithApproachingDeadlines as $deal) {
    $daysUntilDeadline = now()->diffInDays($deal->deadline);
    $webhookDispatcher = new CrmWebhookDispatcher();
    $webhookDispatcher->dispatchDealDeadlineApproaching($deal, $daysUntilDeadline);
}
```

### 6. Inactive Deals (Requires Scheduled Task)
```php
// Example usage in a scheduled command or service
$inactiveDeals = Deal::where('updated_at', '<=', now()->subDays(7))
    ->where('status', 'Active')
    ->get();

foreach ($inactiveDeals as $deal) {
    $inactiveDays = now()->diffInDays($deal->updated_at);
    $webhookDispatcher = new CrmWebhookDispatcher();
    $webhookDispatcher->dispatchDealInactiveForDays($deal, $inactiveDays);
}
```

## Integration Status

### ✅ Fully Integrated
- **Deal Created**: Integrated in `DealController@store`
- **Deal Updated**: Enhanced in `DealController@update` with change tracking
- **Deal Deleted**: Integrated in `DealController@destroy`
- **Deal Stage Changed**: Integrated in `DealController@order` (drag & drop)
- **Deal Status Changed**: Integrated in `DealController@changeStatus`
- **Deal Assignment**: Integrated in `DealController@userUpdate`
- **Deal Unassignment**: Integrated in `DealController@userDestroy`
- **Deal Won/Lost**: Integrated in `DealController@changeStatus` (automatic based on status)
- **Deal Notes Added**: Integrated in `DealController@noteStore`
- **Deal Documents Uploaded**: Integrated in `DealController@fileUpload`

### ⏳ Requires Scheduled Task
- **Deal Deadline Approaching**: Available via command/service
- **Deal Inactive for X Days**: Available via command/service

## Automated Webhook Checking

### Laravel Command
A Laravel command has been created to check for deals that need webhook triggers:

```bash
# Check with default settings (3 days for deadlines, 7 days for inactivity)
php artisan deals:check-webhooks

# Custom settings
php artisan deals:check-webhooks --deadline-days=5 --inactive-days=10

# Dry run (shows what would happen without sending webhooks)
php artisan deals:check-webhooks --dry-run
```

### Service Class
A service class `DealWebhookService` provides programmatic access:

```php
use App\Services\DealWebhookService;

$service = new DealWebhookService();

// Check approaching deadlines (3 days)
$deadlineResults = $service->checkApproachingDeadlines(3);

// Check inactive deals (7 days)
$inactiveResults = $service->checkInactiveDeals(7);

// Check both
$allResults = $service->checkAll(3, 7);

// Get deals without sending webhooks
$upcomingDeadlines = $service->getDealsWithApproachingDeadlines(3);
$inactiveDeals = $service->getInactiveDeals(7);
```

### Scheduling
Add to `app/Console/Kernel.php`:

```php
protected function schedule(Schedule $schedule)
{
    // Check daily at 9 AM
    $schedule->command('deals:check-webhooks')
             ->dailyAt('09:00')
             ->withoutOverlapping();

    // Or check multiple times per day
    $schedule->command('deals:check-webhooks --deadline-days=1')
             ->twiceDaily(9, 15); // 9 AM and 3 PM
}
```

## Next Steps

1. **Configure Scheduler**: Add the command to Laravel's task scheduler in `app/Console/Kernel.php`
2. **Add Configuration**: Add settings for deadline warning days and inactivity thresholds
3. **Testing**: Test all webhook integrations with external modules
4. **Monitor Logs**: Check Laravel logs for webhook dispatch results

## Error Handling

All webhook dispatches are wrapped in try-catch blocks to prevent failures from affecting normal CRM operations:

```php
try {
    $webhookDispatcher = new CrmWebhookDispatcher();
    $webhookDispatcher->dispatchDealAssigned($deal, $userId);
} catch (\Exception $e) {
    \Log::error('Error dispatching deal assignment webhook: ' . $e->getMessage());
}
```

## Webhook Data Structure

Each webhook includes:
- **Standard deal data**: ID, name, price, stage, pipeline, etc.
- **Action-specific data**: Varies by webhook type
- **User context**: Current user information
- **Timestamp**: When the action occurred

The webhook system automatically handles:
- Module integration lookup
- Payload formatting
- HTTP delivery
- Error logging
- Retry logic (if configured)
