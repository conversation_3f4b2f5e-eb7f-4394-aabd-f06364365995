<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PersonalTaskChecklist extends Model
{
    protected $fillable = [
        'name',
        'task_id',
        'user_type',
        'created_by',
        'status',
    ];

    /**
     * Get the task that owns the checklist
     */
    public function task()
    {
        return $this->belongsTo('App\Models\PersonalTask', 'task_id');
    }

    /**
     * Get the user that created the checklist
     */
    public function user()
    {
        return $this->belongsTo('App\Models\User', 'created_by');
    }
}
