
<div class="modal-body">
    <div class="row">
        <div class="col-6">
            <div class="form-group">
                <b class="text-sm">{{ __('Title')}} :</b>
                <p class="m-0 p-0 text-sm">{{$bug->title}}</p>
            </div>
        </div>
        <div class="col-6">
            <div class="form-group">
                <b class="text-sm">{{ __('Priority')}} :</b>
                <p class="m-0 p-0 text-sm">{{ucfirst($bug->priority)}}</p>
            </div>
        </div>

        <div class="col-6 ">
            <div class="form-group">
                <b class="text-sm">{{ __('Created Date')}} :</b>
                <p class="m-0 p-0 text-sm">{{$bug->created_at}}</p>
            </div>
        </div>
        <div class="col-6">
            <div class="form-group">
                <b class="text-sm">{{ __('Assign to')}} :</b>
                <p class="m-0 p-0 text-sm">{{(!empty($bug->assignTo)?$bug->assignTo->name:'')}}</p>
            </div>
        </div>
        <div class="col-12">
            <div class="form-group">
                <b class="text-sm">{{ __('Description')}} :</b>
                <p class="m-0 p-0 text-sm">{{$bug->description}}</p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <ul class="nav nav-tabs" id="myTab" role="tablist">
                <li class="nav-item mb-2">
                    <a class="btn btn-outline-primary btn-sm ml-1 active show" data-bs-toggle="tab"
                       href="#profile" role="tab" aria-selected="false">{{__('Comments')}}</a>
                </li>
                <li class="nav-item mb-2">
                    <a class="btn btn-outline-primary btn-sm ml-1" id="contact-tab" data-bs-toggle="tab" href="#contact" role="tab" aria-controls="contact" aria-selected="false">{{__('Files')}}</a>
                </li>
            </ul>

            <div class="tab-content pt-4" id="myTabContent">
                <div class="tab-pane fade active show" id="profile" role="tabpanel" aria-labelledby="profile-tab">
                    {{-- Comments Section --}}
                    <div id="comments" class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5>{{__('Comments')}} <span class="badge bg-secondary ms-2">{{ $bug->comments->count() }}</span></h5>
                                    <button type="button" class="btn btn-sm btn-primary" id="toggleBugCommentForm">
                                        <i class="ti ti-plus"></i> {{__('Add Comment')}}
                                    </button>
                                </div>
                                <div class="card-body">
                                    {{-- Inline Comment Form (Hidden by default) --}}
                                    <div id="bugCommentForm" class="comment-form-section mb-4" style="display: none;">
                                        <div class="d-flex gap-3">
                                            <div class="flex-shrink-0">
                                                <div class="theme-avtar bg-primary">
                                                    <span>{{substr(\Auth::user()->name, 0, 1)}}</span>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1">
                                                <form method="post" action="{{route('bug.comment.store',[$bug->project_id,$bug->id])}}" id="inlineBugCommentForm">
                                                    @csrf
                                                    <div class="form-group mb-3">
                                                        <textarea class="form-control" name="comment" id="bugCommentTextarea" rows="3" placeholder="{{__('Write your comment...')}}" required></textarea>
                                                    </div>
                                                    <div class="d-flex gap-2">
                                                        <button type="submit" class="btn btn-sm btn-primary" id="submitBugComment">
                                                            <i class="ti ti-send"></i> {{__('Add Comment')}}
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-secondary" id="cancelBugComment">
                                                            {{__('Cancel')}}
                                                        </button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>

                                    {{-- Comments List --}}
                                    <div id="bugCommentsList">
                                        @if(!empty($bug->comments) && count($bug->comments) > 0)
                                            @foreach($bug->comments->sortByDesc('created_at') as $comment)
                                                <div class="comment-item border-bottom pb-3 mb-3">
                                                    <div class="d-flex">
                                                        <div class="flex-shrink-0">
                                                            <div class="theme-avtar bg-primary">
                                                                <span>{{substr($comment->user->name ?? 'U', 0, 1)}}</span>
                                                            </div>
                                                        </div>
                                                        <div class="flex-grow-1 ms-3">
                                                            <div class="d-flex justify-content-between align-items-start">
                                                                <div>
                                                                    <h6 class="mb-1">{{$comment->user->name ?? __('Unknown User')}}</h6>
                                                                    <small class="text-muted">{{$comment->created_at->diffForHumans()}}</small>
                                                                </div>
                                                                <div class="dropdown">
                                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                                        <i class="ti ti-dots-vertical"></i>
                                                                    </button>
                                                                    <ul class="dropdown-menu">
                                                                        <li>
                                                                            <a class="dropdown-item text-danger delete-comment" href="#" data-url="{{ route('bug.comment.destroy', $comment->id) }}">
                                                                                <i class="ti ti-trash"></i> {{__('Delete')}}
                                                                            </a>
                                                                        </li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                            <div class="comment-text mt-2">
                                                                <p class="mb-0">{{$comment->comment}}</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        @else
                                            <div class="text-center py-4" id="noBugCommentsMessage">
                                                <i class="ti ti-message-circle text-muted" style="font-size: 3rem;"></i>
                                                <p class="text-muted mt-2 mb-0">{{__('No comments yet')}}</p>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="contact" role="tabpanel" aria-labelledby="contact-tab">
                    <div class="form-group m-0">
                        <form method="post" id="form-file" enctype="multipart/form-data" data-url="{{ route('bug.comment.file.store',$bug->id) }}">
                            @csrf
                            <div class="row">
                                <div class="col-6">
                                <div class="choose-file form-group">
                                    <label for="file" class="form-label">
                                        <div>{{__('file here')}}</div>
                                        <input type="file" class="form-control" name="file" id="file" data-filename="file_update">
                                    </label>
                                    <p class="file_update"></p>
                                </div>
                                    <span class="invalid-feedback" id="file-error" role="alert"></span>
                                </div>
                                <div class="col-4">
                                    <div class="btn-group  ml-2 mt-4 d-none d-sm-inline-block">
                                        <button type="submit" class="btn btn-primary btn-sm ml-1 text-white">{{ __('Upload')}}</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <div class="row mt-3" id="comments-file">
                            @foreach($bug->bugFiles as $file)
                                <div class="col-8 mb-2 file-{{$file->id}}">
                                    <h5 class="mt-0 mb-1 font-weight-bold text-sm"> {{$file->name}}</h5>
                                    <p class="m-0 text-xs">{{$file->file_size}}</p>
                                </div>
                                <div class="col-4 mb-2 file-{{$file->id}}">
                                    <div class="comment-trash" style="float: right">
                                        <a download href="{{asset(Storage::url('bugs/'.$file->file))}}" class="btn btn-sm btn-primary me-1">
                                            <i class="ti ti-download"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm red btn-danger delete-comment-file m-0 px-2" data-id="{{$file->id}}" data-url="{{route('bug.comment.file.destroy',[$file->id])}}">
                                            <i class="ti ti-trash"></i>
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('script-page')
    <script>
        $(document).ready(function () {
            // Initialize tooltips
            $('[data-bs-toggle="tooltip"]').tooltip();

            // Toggle bug comment form
            $('#toggleBugCommentForm').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const form = $('#bugCommentForm');
                const button = $(this);

                if (form.is(':visible')) {
                    form.slideUp(300);
                    button.html('<i class="ti ti-plus"></i> {{__("Add Comment")}}');
                } else {
                    form.slideDown(300);
                    button.html('<i class="ti ti-minus"></i> {{__("Cancel")}}');
                    $('#bugCommentTextarea').focus();
                }
            });

            // Cancel bug comment
            $('#cancelBugComment').on('click', function(e) {
                e.preventDefault();
                $('#bugCommentForm').slideUp(300);
                $('#toggleBugCommentForm').html('<i class="ti ti-plus"></i> {{__("Add Comment")}}');
                $('#bugCommentTextarea').val('');
            });

            // Submit bug comment form
            $('#inlineBugCommentForm').on('submit', function(e) {
                e.preventDefault();

                const form = $(this);
                const textarea = $('#bugCommentTextarea');
                const comment = textarea.val().trim();
                const submitBtn = $('#submitBugComment');

                if (!comment) {
                    alert('{{__("Please enter a comment")}}');
                    return;
                }

                // Show loading state
                submitBtn.prop('disabled', true).html('<i class="ti ti-loader ti-spin"></i> {{__("Adding...")}}');

                $.ajax({
                    url: form.attr('action'),
                    method: 'POST',
                    data: form.serialize(),
                    success: function(response) {
                        // Reset form
                        textarea.val('');
                        $('#bugCommentForm').slideUp(300);
                        $('#toggleBugCommentForm').html('<i class="ti ti-plus"></i> {{__("Add Comment")}}');

                        // Show success message
                        if (typeof show_toastr === 'function') {
                            show_toastr('{{__("Success")}}', '{{__("Comment added successfully")}}', 'success');
                        }

                        // Reload to show new comment
                        setTimeout(function() {
                            location.reload();
                        }, 500);
                    },
                    error: function(xhr, status, error) {
                        console.error('Error adding comment:', error);
                        alert('{{__("Error adding comment. Please try again.")}}');
                    },
                    complete: function() {
                        // Reset button state
                        submitBtn.prop('disabled', false).html('<i class="ti ti-send"></i> {{__("Add Comment")}}');
                    }
                });
            });

            // Handle delete comment
            $(document).on('click', '.delete-comment', function(e) {
                e.preventDefault();

                if (confirm('{{__("Are you sure you want to delete this comment?")}}')) {
                    const url = $(this).data('url');
                    const commentItem = $(this).closest('.comment-item');

                    $.ajax({
                        url: url,
                        method: 'DELETE',
                        data: {
                            _token: $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            commentItem.fadeOut(300, function() {
                                $(this).remove();
                                // Update comment count
                                const currentCount = parseInt($('.badge').text()) || 0;
                                $('.badge').text(Math.max(0, currentCount - 1));

                                // Show no comments message if no comments left
                                if ($('.comment-item').length === 0) {
                                    $('#bugCommentsList').html('<div class="text-center py-4" id="noBugCommentsMessage"><i class="ti ti-message-circle text-muted" style="font-size: 3rem;"></i><p class="text-muted mt-2 mb-0">{{__("No comments yet")}}</p></div>');
                                }
                            });
                        },
                        error: function(xhr, status, error) {
                            console.error('Error deleting comment:', error);
                            alert('{{__("Error deleting comment")}}');
                        }
                    });
                }
            });
        });
    </script>
@endpush

