<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chart_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('chart_id'); // e.g., 'leads_this_week'
            $table->string('chart_type'); // e.g., 'bar', 'line', 'pie', etc.
            $table->json('settings')->nullable(); // For future customization
            $table->timestamps();

            // Ensure one setting per user per chart
            $table->unique(['user_id', 'chart_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chart_settings');
    }
};
