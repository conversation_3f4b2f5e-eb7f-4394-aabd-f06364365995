<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lead_files', function (Blueprint $table) {
            // Add new columns to match project files structure
            if (!Schema::hasColumn('lead_files', 'original_name')) {
                $table->string('original_name')->nullable()->after('file_name');
            }
            if (!Schema::hasColumn('lead_files', 'file_type')) {
                $table->string('file_type')->nullable()->after('file_path');
            }
            if (!Schema::hasColumn('lead_files', 'mime_type')) {
                $table->string('mime_type')->nullable()->after('file_type');
            }
            if (!Schema::hasColumn('lead_files', 'file_size')) {
                $table->unsignedBigInteger('file_size')->nullable()->after('mime_type');
            }
            if (!Schema::hasColumn('lead_files', 'uploaded_by')) {
                $table->unsignedBigInteger('uploaded_by')->nullable()->after('file_size');
            }
        });

        // Add foreign key constraint separately to avoid issues
        if (!Schema::hasColumn('lead_files', 'uploaded_by')) {
            Schema::table('lead_files', function (Blueprint $table) {
                $table->foreign('uploaded_by')->references('id')->on('users')->onDelete('set null');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lead_files', function (Blueprint $table) {
            // Drop foreign key first
            if (Schema::hasColumn('lead_files', 'uploaded_by')) {
                $table->dropForeign(['uploaded_by']);
            }

            // Drop columns
            $columns = ['original_name', 'file_type', 'mime_type', 'file_size', 'uploaded_by'];
            foreach ($columns as $column) {
                if (Schema::hasColumn('lead_files', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
