@if($companies->hasPages())
<div class="d-flex justify-content-between align-items-center mt-4 flex-wrap gap-3">
    <div class="pagination-info">
        <span class="text-muted small">
            {{ __('Showing') }} 
            <strong>{{ $companies->firstItem() }}</strong> 
            {{ __('to') }} 
            <strong>{{ $companies->lastItem() }}</strong> 
            {{ __('of') }} 
            <strong>{{ $companies->total() }}</strong> 
            {{ __('results') }}
        </span>
    </div>
    <div class="pagination-wrapper">
        <nav aria-label="{{ __('Pagination Navigation') }}" role="navigation">
            <ul class="pagination pagination-sm justify-content-center mb-0">
                {{-- Previous Page Link --}}
                @if ($companies->onFirstPage())
                    <li class="page-item disabled" aria-disabled="true" aria-label="@lang('pagination.previous')">
                        <span class="page-link" aria-hidden="true">
                            <i class="ti ti-chevron-left"></i>
                        </span>
                    </li>
                @else
                    <li class="page-item">
                        <a class="page-link pagination-link" href="#" data-page="{{ $companies->currentPage() - 1 }}" rel="prev" aria-label="@lang('pagination.previous')">
                            <i class="ti ti-chevron-left"></i>
                        </a>
                    </li>
                @endif

                {{-- Pagination Elements --}}
                @php
                    $start = max($companies->currentPage() - 2, 1);
                    $end = min($start + 4, $companies->lastPage());
                    $start = max($end - 4, 1);
                @endphp

                @if($start > 1)
                    <li class="page-item">
                        <a class="page-link pagination-link" href="#" data-page="1">1</a>
                    </li>
                    @if($start > 2)
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    @endif
                @endif

                @for ($page = $start; $page <= $end; $page++)
                    @if ($page == $companies->currentPage())
                        <li class="page-item active" aria-current="page">
                            <span class="page-link">{{ $page }}</span>
                        </li>
                    @else
                        <li class="page-item">
                            <a class="page-link pagination-link" href="#" data-page="{{ $page }}">{{ $page }}</a>
                        </li>
                    @endif
                @endfor

                @if($end < $companies->lastPage())
                    @if($end < $companies->lastPage() - 1)
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    @endif
                    <li class="page-item">
                        <a class="page-link pagination-link" href="#" data-page="{{ $companies->lastPage() }}">{{ $companies->lastPage() }}</a>
                    </li>
                @endif

                {{-- Next Page Link --}}
                @if ($companies->hasMorePages())
                    <li class="page-item">
                        <a class="page-link pagination-link" href="#" data-page="{{ $companies->currentPage() + 1 }}" rel="next" aria-label="@lang('pagination.next')">
                            <i class="ti ti-chevron-right"></i>
                        </a>
                    </li>
                @else
                    <li class="page-item disabled" aria-disabled="true" aria-label="@lang('pagination.next')">
                        <span class="page-link" aria-hidden="true">
                            <i class="ti ti-chevron-right"></i>
                        </span>
                    </li>
                @endif
            </ul>
        </nav>
    </div>
</div>
@endif
