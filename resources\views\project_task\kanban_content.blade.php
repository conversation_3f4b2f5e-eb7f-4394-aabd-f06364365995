<div class="row kanban-wrapper horizontal-scroll-cards" data-containers='{{ json_encode($stageClass) }}' data-plugin="dragula">
    @foreach ($stages as $stage)
        @php($tasks = $stage->tasks)
        <div class="col">
            <div class="crm-sales-card mb-4">
                <div class="card-header d-flex align-items-center justify-content-between gap-3">
                    <h4 class="mb-0">{{ $stage->name }}</h4>
                    @can('create project task')
                        <a href="#" data-size="lg"
                            data-url="{{ route('projects.tasks.create', [$project->id, $stage->id]) }}"
                            data-ajax-popup="true" data-bs-toggle="tooltip"
                            title="{{ __('Add Task in ') . $stage->name }}" class="btn btn-sm btn-light-primary">
                            <i class="ti ti-plus"></i>
                        </a>
                    @endcan
                </div>
                <div class="sales-item-wrp kanban-box" id="task-list-{{ $stage->id }}" data-status="{{ $stage->id }}">
                    @foreach ($tasks as $taskDetail)
                        <div class="sales-item draggable-item" id="{{ $taskDetail->id }}">
                            <div class="sales-item-top border-bottom">
                                <div class="d-flex align-items-center">
                                    <h5 class="mb-0 flex-1">
                                        <a href="#" class="dashboard-link"
                                        data-url="{{ route('projects.tasks.show', [$project->id, $taskDetail->id]) }}"
                                        data-ajax-popup="true" data-size="lg"
                                        data-bs-original-title="{{ $taskDetail->name }}">{{ $taskDetail->name }}</a>
                                    </h5>
                                    <div class="btn-group card-option">
                                        <button type="button" class="btn p-0 border-0"
                                            data-bs-toggle="dropdown" aria-haspopup="true"
                                            aria-expanded="false">
                                            <i class="ti ti-dots-vertical"></i>
                                        </button>
                                        <div class="dropdown-menu dropdown-menu-end">
                                            @can('view project task')
                                                <a href="#" class="dropdown-item dashboard-link"
                                                    data-url="{{ route('projects.tasks.show', [$project->id, $taskDetail->id]) }}"
                                                    data-ajax-popup="true" data-size="lg"
                                                    data-bs-original-title="{{ $taskDetail->name }}">
                                                    <i class="ti ti-eye"></i>
                                                    <span>{{ __('View') }}</span>
                                                </a>
                                            @endcan
                                            @can('edit project task')
                                                <a href="#" class="dropdown-item"
                                                    data-url="{{ route('projects.tasks.edit', [$project->id, $taskDetail->id]) }}"
                                                    data-ajax-popup="true" data-size="lg"
                                                    data-bs-original-title="{{ __('Edit Task') }}">
                                                    <i class="ti ti-pencil"></i>
                                                    <span>{{ __('Edit') }}</span>
                                                </a>
                                            @endcan
                                            @can('delete project task')
                                                <a href="#" class="dropdown-item text-danger bs-pass-para"
                                                    data-confirm="{{ __('Are You Sure?') }}"
                                                    data-text="{{ __('This action can not be undone. Do you want to continue?') }}"
                                                    data-confirm-yes="delete-form-{{ $taskDetail->id }}">
                                                    <i class="ti ti-trash"></i>
                                                    <span>{{ __('Delete') }}</span>
                                                </a>
                                                {!! Form::open(['method' => 'DELETE', 'route' => ['projects.tasks.destroy', $project->id, $taskDetail->id], 'id' => 'delete-form-' . $taskDetail->id]) !!}
                                                {!! Form::close() !!}
                                            @endcan
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="sales-item-body">
                                <div class="d-flex align-items-center justify-content-between mb-3">
                                    <div class="d-flex align-items-center gap-2">
                                        @if($taskDetail->priority)
                                            <span class="badge bg-{{ \App\Models\ProjectTask::$priority_color[$taskDetail->priority] }}">
                                                {{ __(\App\Models\ProjectTask::$priority[$taskDetail->priority]) }}
                                            </span>
                                        @endif
                                        @if($taskDetail->milestone)
                                            <span class="badge bg-secondary">
                                                {{ $taskDetail->milestone->title }}
                                            </span>
                                        @endif
                                    </div>
                                    <small class="text-muted">
                                        {{ \App\Models\Utility::getDateFormated($taskDetail->end_date) }}
                                    </small>
                                </div>
                                
                                @if($taskDetail->description)
                                    <p class="text-muted small mb-3">{{ Str::limit($taskDetail->description, 100) }}</p>
                                @endif
                                
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center gap-2">
                                        @if($taskDetail->users->count() > 0)
                                            <div class="d-flex align-items-center">
                                                @foreach($taskDetail->users->take(3) as $user)
                                                    <div class="user-avatar-text bg-primary text-white" 
                                                         style="width: 24px; height: 24px; font-size: 10px; margin-left: -4px;"
                                                         data-bs-toggle="tooltip" title="{{ $user->name }}">
                                                        {{ substr($user->name, 0, 1) }}
                                                    </div>
                                                @endforeach
                                                @if($taskDetail->users->count() > 3)
                                                    <small class="text-muted ms-2">+{{ $taskDetail->users->count() - 3 }}</small>
                                                @endif
                                            </div>
                                        @endif
                                    </div>
                                    <div class="d-flex align-items-center gap-2 text-muted small">
                                        @if($taskDetail->taskFiles->count() > 0)
                                            <span><i class="ti ti-paperclip"></i> {{ $taskDetail->taskFiles->count() }}</span>
                                        @endif
                                        @if($taskDetail->taskComments->count() > 0)
                                            <span><i class="ti ti-message-circle"></i> {{ $taskDetail->taskComments->count() }}</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    @endforeach
</div>

<script>
    // Initialize dragula for task reordering
    function initializeDragula() {
        if (typeof dragula !== 'undefined') {
            var containers = [];
            $('.kanban-box').each(function() {
                containers.push(this);
            });
            
            if (containers.length > 0) {
                dragula(containers).on('drop', function(el, target, source, sibling) {
                    var task_id = $(el).attr('id');
                    var stage_id = $(target).attr('data-status');
                    var order = [];
                    
                    $(target).find('.draggable-item').each(function() {
                        order.push($(this).attr('id'));
                    });
                    
                    $.ajax({
                        url: '{{ route('tasks.update.order', $project->id) }}',
                        type: 'PATCH',
                        data: {
                            task_id: task_id,
                            stage_id: stage_id,
                            order: order,
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(data) {
                            if (data.success) {
                                show_toastr('success', data.message, 'success');
                            }
                        }
                    });
                });
            }
        }
    }
    
    // Initialize on load
    $(document).ready(function() {
        initializeDragula();
    });
</script>
