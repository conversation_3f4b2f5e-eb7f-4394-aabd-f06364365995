<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PersonalTaskTimeTracking extends Model
{
    protected $fillable = [
        'task_id',
        'user_id',
        'start_time',
        'end_time',
        'is_running',
        'total_time',
        'notes',
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'is_running' => 'boolean',
    ];

    /**
     * Get the task that owns the time tracking
     */
    public function task()
    {
        return $this->belongsTo('App\Models\PersonalTask', 'task_id');
    }

    /**
     * Get the user that owns the time tracking
     */
    public function user()
    {
        return $this->belongsTo('App\Models\User', 'user_id');
    }

    /**
     * Get formatted time duration
     */
    public function getFormattedDuration()
    {
        $hours = floor($this->total_time / 3600);
        $minutes = floor(($this->total_time % 3600) / 60);
        $seconds = $this->total_time % 60;
        
        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }
}
