<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('calendar_events', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->datetime('start_date');
            $table->datetime('end_date');
            $table->integer('duration')->nullable(); // in minutes
            $table->integer('booking_per_slot')->default(1);
            $table->integer('minimum_notice')->default(0); // in minutes
            $table->text('description')->nullable();
            $table->enum('location', ['in_person', 'zoom', 'skype', 'meet', 'others'])->nullable();
            $table->string('meet_link')->nullable();
            $table->text('physical_address')->nullable();
            $table->boolean('require_name')->default(true);
            $table->boolean('require_email')->default(true);
            $table->boolean('require_phone')->default(false);
            $table->json('weekly_availability')->nullable();
            $table->unsignedBigInteger('created_by');
            $table->timestamps();
            
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('calendar_events');
    }
};