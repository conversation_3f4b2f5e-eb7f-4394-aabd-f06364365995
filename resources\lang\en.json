{"\tPaid Amount": "\t<PERSON><PERSON>", " & allocated total ": " & allocated total ", " (Format h:m:s i.e 00:35:20 means 35 Minutes and 20 Sec)": " (Format h:m:s i.e 00:35:20 means 35 Minutes and 20 Sec)", " --- Select Account ---": " --- Select Account ---", " 1 to 15 Days": " 1 to 15 Days", " 16 to 30 Days": " 16 to 30 Days", " 31 to 45 Days": " 31 to 45 Days", " > 45 Days": " > 45 Days", " Add POS": " Add POS", " added to cart successfully!": " added to cart successfully!", " All In One Business ERP With Project, Account, HRM, CRM": " All In One Business ERP With Project, Account, HRM, CRM", " and earn ": " and earn ", " Days": " Days", " Estimated Hours": " Estimated Hours", " Format": " Format", " from task": " from task", " Generate With AI": " Generate With AI", " Hours": " Hours", " hrs in other tasks": " hrs in other tasks", " Manually Payment": " Manually Payment", " No Data Available.!": " No Data Available.!", " No Emails Available.!": " No Emails Available.!", " Overview": " Overview", " Payment successfully added.": " Payment successfully added.", " per paid signup!": " per paid signup!", " Please confirm your password before continuing.": " Please confirm your password before continuing.", " Product not select in warehouse": " Product not select in warehouse", " Purchase Amount": " Purchase Amount", " quantity add in purchase": " quantity add in purchase", " quantity purchase in bill": " quantity purchase in bill", " quantity sold in": " quantity sold in", " quantity sold in invoice": " quantity sold in invoice", " quantity sold in pos": " quantity sold in pos", " Remove": " Remove", " Revenue": " Revenue", " Strictly Cookie Title": " Strictly Cookie Title", " Sub Total": " Sub Total", " to": " to", " We help": " We help", "$ 10.000,00": "$ 10.000,00", "'s Detail": "'s Detail", "'s Expenses": "'s Expenses", "'s Form Field": "'s Form Field", "'s Response": "'s Response", "'s Tasks": "'s Tasks", "'s Timesheet": "'s Timesheet", "1,000+ customers": "1,000+ customers", "1-15 DAYS": "1-15 DAYS", "16-30 DAYS": "16-30 DAYS", "31-45 DAYS": "31-45 DAYS", "70% Special Offer": "70% Special Offer", "> 45 DAYS": "> 45 DAYS", "`Cancle Request": "`Cancle Request", "A new verification link has been sent to the email address you provided during registration.": "A new verification link has been sent to the email address you provided during registration.", "Aamarpay": "<PERSON><PERSON><PERSON><PERSON>", "AamarPay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Aamarpay Mode": "Aamarpay Mode", "Absent": "Absent", "Access Token": "Access Token", "Account": "Account", "Account Amount": "Account Amount", "Account Balance": "Account <PERSON><PERSON>", "Account Code": "Account Code", "Account Drilldown": "Account <PERSON><PERSON><PERSON>", "Account Drilldown Report": "Account Drilldown Report", "Account Holder Name": "Account Holder Name", "Account Name": "Account Name", "Account Number": "Account Number", "Account Statement": "Account Statement", "Account Statement Summary": "Account Statement Summary", "Account successfully created.": "Account successfully created.", "Account successfully deleted.": "Account successfully deleted.", "Account successfully updated.": "Account successfully updated.", "Account Type": "Account Type", "Accounting ": "Accounting ", "Accounting Setup": "Accounting Setup", "Accounting System ": "Accounting System ", "Action": "Action", "Active": "Active", "Active Job ": "Active Job ", "Active Jobs": "Active Jobs", "Active Training": "Active Training", "Active Training ": "Active Training ", "Active User": "Active User", "Active Users": "Active Users", "Activity": "Activity", "Activity Log": "Activity Log", "Activity Log of this project": "Activity Log of this project", "Add": "Add", "Add a comment...": "Add a comment...", "Add a Notes...": "Add a Notes...", "Add Account": "Add Account", "Add Accounts": "Add Accounts", "Add attachment": "Add attachment", "Add Call": "Add Call", "Add Credit Note": "Add Credit Note", "Add Debit Note": "Add Debit Note", "Add Field": "Add Field", "Add item": "Add item", "Add Item": "Add Item", "Add Labels": "Add Labels", "Add Member": "Add Member", "Add Message": "Add Message", "Add new Discussion": "Add new Discussion", "Add new Products": "Add new Products", "Add Notes": "Add Notes", "Add Payment": "Add Payment", "Add Product": "Add Product", "Add signature": "Add signature", "Add Skills": "Add Skills", "Add some products to cart!": "Add some products to cart!", "Add Source": "Add Source", "Add Task": "Add Task", "Add Task in ": "Add Task in ", "Add Task on Timesheet": "Add Task on Timesheet", "Add Time successfully.": "Add Time successfully.", "Add to Job OnBoard": "Add to Job OnBoard", "Add User": "Add User", "Added": "Added", "Added By": "Added By", "Added you": "Added you", "Additional Details": "Additional Details", "Address": "Address", "Admin Hub": "<PERSON><PERSON>", "After coupon apply": "After coupon apply", "after tax & discount": "after tax & discount", "Age": "Age", "Aging Details": "Aging Details", "Aging Summary": "Aging Summary", "AI Creativity": "AI Creativity", "All": "All", "All Branch": "All Branch", "All Categories": "All Categories", "All Department": "All Department", "All Employee": "All Employee", "All In One Business ERP With Project, Account, HRM, CRM": "All In One Business ERP With Project, Account, HRM, CRM", "All items here cannot be deleted.": "All items here cannot be deleted.", "All Project": "All Project", "All Users": "All Users", "Allocated hours on task": "Allocated hours on task", "allocated total ": "allocated total ", "Allowance": "Allowance", "Allowance  successfully created.": "Allowance  successfully created.", "Allowance Option": "Allowance Option", "Allowance Options": "Allowance Options", "Allowance Options*": "Allowance Options*", "Allowance successfully deleted.": "Allowance successfully deleted.", "Allowance successfully updated.": "Allowance successfully updated.", "AllowanceOption  successfully created.": "AllowanceOption  successfully created.", "AllowanceOption successfully deleted.": "AllowanceOption successfully deleted.", "AllowanceOption successfully updated.": "AllowanceOption successfully updated.", "Allownace Option": "Allownace Option", "Already convert to Invoice": "Already convert to Invoice", "Already convert to POS": "Already convert to POS", "Already Converted To Deal": "Already Converted To Deal", "Already have an account?": "Already have an account?", "Always looking for better ways to do things, innovate": "Always looking for better ways to do things, innovate", "Amount": "Amount", "Amount Due": "Amount Due", "Amount successfully transfer updated.": "Amount successfully transfer updated.", "Amount successfully transfer.": "Amount successfully transfer.", "Amount transfer successfully deleted.": "Amount transfer successfully deleted.", "and help people achieve their goals": "and help people achieve their goals", "and the ": "and the ", "Announcement": "Announcement", "Announcement Description": "Announcement Description", "Announcement End Date": "Announcement End Date", "Announcement List": "Announcement List", "Announcement start Date": "Announcement start Date", "Announcement successfully created, Webhook call failed.": "Announcement successfully created, Webhook call failed.", "Announcement successfully created.": "Announcement successfully created.", "Announcement successfully deleted.": "Announcement successfully deleted.", "Announcement successfully updated.": "Announcement successfully updated.", "Announcement Title": "Announcement Title", "Answer": "Answer", "API Key": "API Key", "Api key is wrong": "Api key is wrong", "App ID": "App ID", "App Name": "App Name", "App Secret": "App Secret", "App Url": "App Url", "Applicant Name": "Applicant Name", "Applicant Notes": "Applicant Notes", "Application successfully converted to employee.": "Application successfully converted to employee.", "Application URL": "Application URL", "Application URL to log into the app.": "Application URL to log into the app.", "Applied at": "Applied at", "Applied For": "Applied For", "Applied On": "Applied On", "Apply": "Apply", "apply": "apply", "Apply for this job": "Apply for this job", "Apply now": "Apply now", "Appplied On": "Appplied On", "Appraisal": "Appraisal", "Appraisal Date": "Appraisal Date", "Appraisal Detail": "Appraisal Detail", "Appraisal successfully created.": "Appraisal successfully created.", "Appraisal successfully deleted.": "Appraisal successfully deleted.", "Appraisal successfully updated.": "Appraisal successfully updated.", "Approval": "Approval", "Approve": "Approve", "Approved Leave Detail": "Approved Leave Detail", "Approved Leaves": "Approved Leaves", "Apr-Jun": "Apr-Jun", "April": "April", "Archive": "Archive", "Archive Application": "Archive Application", "are required in": "are required in", "Are You Sure?": "Are You Sure?", "Are you sure?": "Are you sure?", "As": "As", "Asigned": "Asigned", "Assets": "Assets", "Assets successfully created.": "Assets successfully created.", "Assets successfully deleted.": "Assets successfully deleted.", "Assets successfully updated.": "Assets successfully updated.", "Assign Account related Permission to Roles": "Assign Account related Permission to Roles", "Assign CRM related Permission to Roles": "Assign CRM related Permission to Roles", "Assign Employee": "Assign Em<PERSON>loyee", "Assign General Permission to Roles": "Assign General Permission to Roles", "Assign HRM related Permission to Roles": "Assign HRM related Permission to Roles", "Assign Permission to Roles": "Assign Permission to Roles", "Assign POS related Permission to Roles": "Assign POS related Permission to Roles", "Assign Project related Permission to Roles": "Assign Project related Permission to Roles", "Assign To": "Assign To", "Assign to": "Assign to", "Assign User": "Assign User", "Assigned Client": "Assigned Client", "Assigned Tasks": "Assigned Tasks", "Assigned To": "Assigned To", "Assigned to": "Assigned to", "Assignee": "Assignee", "Attachment": "Attachment", "Attachment that uploaded in this project": "Attachment that uploaded in this project", "Attachments": "Attachments", "Attachments ewrwr": "Attachments ewrwr", "Attendance": "Attendance", "Attendance Report of": "Attendance Report of", "Attendance successfully deleted.": "Attendance successfully deleted.", "Attendance Summary": "Attendance Summary", "August": "August", "Auth Token": "<PERSON><PERSON>", "AuthorizeNet": "AuthorizeNet", "Authorizenet Invoice Payment": "Authorizenet Invoice Payment", "Authorizenet Mode": "Authorizenet Mode", "Authorizenet Plan Payment": "Authorizenet Plan Payment", "AuthorizetNet": "AuthorizetNet", "Auto Generate": "Auto Generate", "Availabe currencies: XOF, CDF, USD, KMF, GNF": "Availabe currencies: XOF, CDF, USD, KMF, GNF", "Available Credits": "Available Credits", "Available Debit": "Available Debit", "Avatar": "Avatar", "Average Price": "Average Price", "Average Sales": "Average Sales", "Awaiting payment": "Awaiting payment", "Award": "Award", "Award Date": "Award Date", "Award Email": "Award Email", "Award Name": "Award Name", "Award successfully created, Webhook call failed.": "Award successfully created, Webhook call failed.", "Award successfully created.": "Award successfully created.", "Award successfully deleted.": "Award successfully deleted.", "Award successfully updated.": "Award successfully updated.", "Award Type": "Award Type", "AwardType  successfully created.": "AwardType  successfully created.", "AwardType successfully deleted.": "AwardType successfully deleted.", "AwardType successfully updated.": "AwardType successfully updated.", "AWS S3": "AWS S3", "Back": "Back", "Back to": "Back to", "Balance": "Balance", "Balance Due": "Balance Due", "Balance Sheet": "Balance Sheet", "Bank": "Bank", "Bank Account": "Bank Account", "Bank Account Detail": "Bank Account Detail", "Bank Address": "Bank Address", "Bank Balance Transfer": "Bank Balance Transfer", "Bank Branch": "Bank Branch", "Bank Details": "Bank Details", "Bank Holder Name": "Bank Holder Name", "Bank Identifier Code": "Bank Identifier Code", "Bank Name": "Bank Name", "Bank Transfer": "Bank Transfer", "Banking": "Banking", "Banner": "Banner", "Barcode": "Barcode", "Barcode Format": "Barcode Format", "Barcode Setting": "Barcode Setting", "Barcode Type": "Barcode Type", "Basic details": "Basic details", "Basic Details": "Basic Details", "Basic Info": "Basic Info", "Basic Information": "Basic Information", "Basic Salary": "Basic Salary", "Below users are assigned in your project.": "Below users are assigned in your project.", "Benefit": "Benefit", "Benefit Key": "Benefit Key", "Benefit Secret Key": "Benefit Secret Key", "Bill": "Bill", "BILL": "BILL", "Bill :": "Bill :", "Bill : ": "Bill : ", "Bill Create": "<PERSON>", "Bill Date": "<PERSON>", "Bill Detail": "<PERSON>", "Bill duplicate successfully.": "Bill duplicate successfully.", "Bill Edit": "<PERSON>", "Bill Generated": "<PERSON>", "Bill Logo": "<PERSON>", "Bill Name": "<PERSON>", "Bill Not Found.": "<PERSON> Not Found.", "Bill Number": "<PERSON>", "Bill Prefix": "<PERSON>", "Bill Print Setting": "Bill Print Setting", "Bill product successfully deleted.": "Bill product successfully deleted.", "Bill Setting updated successfully": "<PERSON> updated successfully", "Bill successfully created, Webhook call failed.": "Bill successfully created, Webhook call failed.", "Bill successfully created.": "<PERSON> successfully created.", "Bill successfully deleted.": "<PERSON> successfully deleted.", "Bill successfully sent.": "<PERSON> successfully sent.", "Bill successfully updated.": "Bill successfully updated.", "Bill Summary": "<PERSON>", "Bill Template": "<PERSON>", "Bill to": "Bill to", "Bill To": "Bill <PERSON>", "Bill To:": "Bill To:", "Bill Url": "<PERSON>", "Billed Amount": "Billed Amount", "Billed To": "Billed To", "Billed To :": "Billed To :", "Billing Address": "Billing Address", "Billing City": "Billing City", "Billing Country": "Billing Country", "Billing Info": "Billing Info", "Billing Name": "Billing Name", "Billing Phone": "Billing Phone", "Billing State": "Billing State", "Billing Zip": "Billing Zip", "Bills": "Bills", "Bills Monthly Statistics": "Bills Monthly Statistics", "Bills Weekly Statistics": "Bills Weekly Statistics", "Biometric Attendance": "Biometric Attendance", "Biometric Attendance Settings": "Biometric Attendance Settings", "Biometric setting successfully saved.": "Biometric setting successfully saved.", "Branch": "Branch", "Branch  successfully created.": "Branch  successfully created.", "Branch & department field required.": "Branch & department field required.", "Branch Location": "Branch Location", "Branch Name": "Branch Name", "Branch successfully deleted.": "Branch successfully deleted.", "Branch successfully updated.": "Branch successfully updated.", "Brand Settings": "Brand Settings", "Browser Language": "Browser Language", "Browser Name": "Browser Name", "Budget": "Budget", "Budget Create": "Budget Create", "Budget Edit": "Budget Edit", "Budget Name": "Budget Name", "Budget Not Found.": "Budget Not Found.", "Budget Period": "Budget Period", "Budget plan successfully created, Webhook call failed.": "Budget plan successfully created, Webhook call failed.", "Budget Plan successfully created.": "Budget Plan successfully created.", "Budget Plan successfully deleted.": "Budget Plan successfully deleted.", "Budget Plan successfully updated.": "Budget Plan successfully updated.", "Budget Planner": "Budget Planner", "Budget Vs Actual": "Budget Vs Actual", "Budget Year": "Budget Year", "Bug": "Bug", "Bug comment successfully created.": "Bug comment successfully created.", "Bug Id": "Bug Id", "Bug Report": "Bug Report", "Bug Status": "Bug Status", "Bug status successfully created.": "Bug status successfully created.", "Bug status successfully deleted.": "Bug status successfully deleted.", "Bug status successfully updated.": "Bug status successfully updated.", "Bug Status Title": "Bug Status Title", "Bug successfully created.": "<PERSON><PERSON> successfully created.", "Bug successfully deleted.": "<PERSON><PERSON> successfully deleted.", "Bug successfully moved.": "<PERSON><PERSON> successfully moved.", "Bug Title": "Bug Title", "Bugs": "Bugs", "Bulk Attendance": "Bulk Attendance", "Bulk Payment": "Bulk Payment", "businesses grow": "businesses grow", "button": "button", "Buy Now": "Buy Now", "Buy Now ": "Buy Now ", "Buy Now Link": "Buy Now Link", "Buy Plan": "Buy Plan", "Cache Clear": "<PERSON><PERSON>", "Cache Settings": "<PERSON><PERSON>", "Calendar": "Calendar", "Calendar View": "Calendar View", "Calender View": "Calender View", "Call Result": "Call Result", "Call successfully created!": "Call successfully created!", "Call successfully deleted!": "Call successfully deleted!", "Call successfully updated!": "Call successfully updated!", "Call Type": "Call Type", "Calls": "Calls", "Cancel": "Cancel", "Canceled": "Canceled", "Cancle Request": "Cancle Request", "Candidate Detail": "Candidate Detail", "Candidate Status": "Candidate Status", "Candidate succefully added in job board.": "Candidate succefully added in job board.", "Card Number": "Card Number", "Card View": "Card View", "Career": "Career", "Cart cannot be empty!.": "Cart cannot be empty!.", "Cart is empty!": "Cart is empty!", "Cart updated successfully!": "Cart updated successfully!", "Cash Flow": "Cash Flow", "Cash Payment": "Cash Payment", "Cashflow": "Cashflow", "Cashfree": "Cashfree", "Cashfree Key": "Cashfree Key", "Cashfree Secret Key": "Cashfree Secret Key", "Category": "Category", "Category Code": "Category Code", "Category Color": "Category Color", "Category half": "Category half", "Category Key": "Category Key", "Category Name": "Category Name", "Category successfully created.": "Category successfully created.", "Category successfully deleted.": "Category successfully deleted.", "Category successfully updated.": "Category successfully updated.", "Category Type": "Category Type", "Category year": "Category year", "Change Password": "Change Password", "Channel": "Channel", "Chart Of Account": "Chart Of Account", "Chart of Account": "Chart of Account", "Chart of account type successfully created.": "Chart of account type successfully created.", "Chart of account type successfully deleted.": "Chart of account type successfully deleted.", "Chart of account type successfully updated.": "Chart of account type successfully updated.", "Chart of Accounts": "Chart of Accounts", "Chat GPT": "Chat GPT", "Chat GPT key": "Chat GPT key", "Chat GPT Model Name": "Chat GPT Model Name", "Chat GPT Settings": "Chat GPT Settings", "ChatGPT": "ChatGPT", "ChatGPT Setting successfully saved.": "ChatGPT Setting successfully saved.", "Check out our repository": "Check out our repository", "Checklist": "Checklist", "Checklist Added Successfully!": "Checklist Added Successfully!", "Checklist Deleted Successfully!": "Checklist Deleted Successfully!", "Checklist Name": "Checklist Name", "Checklist Updated Successfully!": "Checklist Updated Successfully!", "Choose a file…": "Choose a file…", "Choose File": "Choose <PERSON>", "Choose file here": "Choose file here", "CinetPay": "CinetPay", "Cinetpay": "Cinetpay", "CinetPay API Key": "CinetPay API Key", "CinetPay Site ID": "CinetPay Site ID", "City": "City", "Clear": "Clear", "Click here to add new client": "Click here to add new client", "Click here to add new company": "Click here to add new company", "Click here to add new user": "Click here to add new user", "Click to change status": "Click to change status", "Click to copy": "Click to copy", "Click to copy iframe link": "Click to copy iframe link", "Click to copy link": "Click to copy link", "Click to Mark Billable": "Click to <PERSON>", "Click to Mark Non-Billable": "Click to Mark Non-Billable", "Click To Paid": "<PERSON>lick To <PERSON>id", "Click to Upgrade Plan": "Click to Upgrade Plan", "Client": "Client", "Client ": "Client ", "Client Deleted Successfully!": "Client Deleted Successfully!", "Client Email": "Client Email", "Client ID": "Client ID", "Client Name": "Client Name", "Client Password": "Client Password", "Client Signature": "Client Signature", "Client successfully added.": "Client successfully added.", "Client successfully created.": "Client successfully created.", "Client successfully deleted!": "Client successfully deleted!", "Client Updated Successfully!": "Client Updated Successfully!", "Clients": "Clients", "Clients successfully updated!": "Clients successfully updated!", "CLOCK IN": "CLOCK IN", "Clock In": "Clock In", "CLOCK OUT": "CLOCK OUT", "Clock Out": "Clock Out", "Close": "Close", "Closed": "Closed", "Closing Balance": "Closing Balance", "Code": "Code", "Code: ": "Code: ", "Coingate": "Coingate", "CoinGate": "CoinGate", "CoinGate Auth Token": "CoinGate Auth Token", "CoinGate Mode": "CoinGate Mode", "Collapse": "Collapse", "Color": "Color", "Color Input": "Color Input", "Comma": "Comma", "Comment": "Comment", "Comment Added Successfully!": "Comment Added Successfully!", "Comment added successfully, Webhook call failed.": "Comment added successfully, Webhook call failed.", "Comment added successfully.": "Comment added successfully.", "Comment Deleted Successfully!": "Comment Deleted Successfully!", "Comment successfully deleted!": "Comment successfully deleted!", "Comments": "Comments", "comments successfully created!": "comments successfully created!", "Commission": "Commission", "Commission  successfully created.": "Commission  successfully created.", "Commission (%)": "Commission (%)", "Commission Amount": "Commission Amount", "Commission Percentage (%)": "Commission Percentage (%)", "Commission successfully deleted.": "Commission successfully deleted.", "Commission successfully updated.": "Commission successfully updated.", "Companies": "Companies", "Company": "Company", "Company Date Of Joining": "Company Date Of Joining", "Company Detail": "Company Detail", "Company End Time": "Company End Time", "Company Info": "Company Info", "Company login disable successfully.": "Company login disable successfully.", "Company login enable successfully.": "Company login enable successfully.", "Company Name": "Company Name", "Company Name *": "Company Name *", "Company policy": "Company policy", "Company Policy": "Company Policy", "Company policy created successfully, Webhook call failed.": "Company policy created successfully, Webhook call failed.", "Company Policy Name": "Company Policy Name", "Company policy successfully created.": "Company policy successfully created.", "Company policy successfully deleted.": "Company policy successfully deleted.", "Company policy successfully updated.": "Company policy successfully updated.", "Company Registration Number": "Company Registration Number", "Company Settings": "Company Settings", "Company Signature": "Company Signature", "Company Start Time": "Company Start Time", "Company successfully created.": "Company successfully created.", "Company Successfully deleted": "Company Successfully deleted", "Competencies": "Competencies", "Competencies  successfully created.": "Competencies  successfully created.", "Competencies  successfully deleted.": "Competencies  successfully deleted.", "Competencies  successfully updated.": "Competencies  successfully updated.", "Complain": "<PERSON><PERSON><PERSON>", "Complaint  successfully created.": "<PERSON><PERSON><PERSON><PERSON>  successfully created.", "Complaint Against": "<PERSON><PERSON><PERSON><PERSON> Against", "Complaint Date": "Complaint Date", "Complaint From": "<PERSON><PERSON><PERSON><PERSON> From", "Complaint Name": "Complaint Name", "Complaint successfully deleted.": "<PERSON><PERSON><PERSON><PERSON> successfully deleted.", "Complaint successfully updated.": "<PERSON><PERSON><PERSON><PERSON> successfully updated.", "Complaint Title": "Complaint Title", "Complaints": "<PERSON><PERSON><PERSON><PERSON>", "Complete": "Complete", "Completed": "Completed", "Completed Milestone": "Completed Milestone", "Completed:": "Completed:", "Completion": "Completion", "Confirm Password": "Confirm Password", "Connected": "Connected", "Connecting...": "Connecting...", "Contact": "Contact", "Contact Number": "Contact Number", "Contact Us Description": "Contact Us Description", "Contact Us URL": "Contact Us URL", "Contacts": "Contacts", "Contract": "Contract", "contract": "contract", "Contract Attachments": "Contract Attachments", "Contract Description": "Contract Description", "Contract Description ": "Contract Description ", "Contract description successfully saved!": "Contract description successfully saved!", "Contract Detail": "Contract Detail", "Contract End Date": "Contract End Date", "contract file successfully deleted.": "contract file successfully deleted.", "Contract Name": "Contract Name", "Contract Price": "Contract Price", "Contract Priority": "Contract Priority", "Contract Signed successfully": "Contract Signed successfully", "Contract Start Date": "Contract Start Date", "Contract Subject": "Contract Subject", "Contract successfully created!": "Contract successfully created!", "Contract successfully created, Webhook call failed.": "Contract successfully created, Webhook call failed.", "Contract successfully created.": "Contract successfully created.", "Contract successfully deleted.": "Contract successfully deleted.", "Contract successfully updated.": "Contract successfully updated.", "Contract Title": "Contract Title", "Contract Type": "Contract Type", "Contract Type  :": "Contract Type  :", "Contract Type successfully created.": "Contract Type successfully created.", "Contract Type successfully deleted.": "Contract Type successfully deleted.", "Contract Type successfully updated.": "Contract Type successfully updated.", "Contract Value": "Contract Value", "Contract Value   :": "Contract Value   :", "Convert into Lead Setting": "Convert into Lead Setting", "Convert Invoice": "Convert Invoice", "Convert To Employee": "Convert To Employee", "Convert to Employee": "Convert to Employee", "Convert to Invoice": "Convert to Invoice", "Convert to POS": "Convert to POS", "Cookie Description": "<PERSON><PERSON>", "Cookie Settings": "<PERSON><PERSON>", "Cookie Title": "<PERSON>ie Title", "Copy": "Copy", "Copy Contract": "Copy Contract", "Copy Invoice": "Copy Invoice", "Copy Link Setting Save Successfully!": "Copy Link Setting Save Successfully!", "Copy Project": "Copy Project", "Copy Selected Text": "Copy Selected Text", "Copy Text": "Copy Text", "Copy To": "Copy To", "Copylink": "Copylink", "Cost": "Cost", "Country": "Country", "Country Code": "Country Code", "Coupon": "Coupon", "Coupon code has applied successfully.": "Coupon code has applied successfully.", "Coupon code is required": "Coupon code is required", "Coupon code required.": "Coupon code required.", "Coupon Details": "Coupon Details", "Coupon Discount": "Coupon Discount", "Coupon successfully created.": "Coupon successfully created.", "Coupon successfully deleted.": "Coupon successfully deleted.", "Coupon successfully updated.": "Coupon successfully updated.", "Cover Letter": "Cover Letter", "Create": "Create", "Create  New Meeting": "Create  New Meeting", "Create account": "Create account", "Create account here.": "Create account here.", "Create Allowance": "Create Allowance", "Create allowance option": "Create allowance option", "Create allowance option here.": "Create allowance option here.", "Create award type": "Create award type", "Create award type here.": "Create award type here.", "Create bank account": "Create bank account", "Create bank account here.": "Create bank account here.", "Create Bank-Transfer": "Create Bank-Transfer", "Create Bill": "Create Bill", "Create bill": "Create bill", "Create bill here.": "Create bill here.", "Create branch": "Create branch", "Create branch here.": "Create branch here.", "Create Budget Planner": "Create Budget Planner", "Create Bug Stage": "Create Bug Stage", "Create bug status": "Create bug status", "Create bug status here.": "Create bug status here.", "Create candidate": "Create candidate", "Create candidate here.": "Create candidate here.", "Create category": "Create category", "Create Category": "Create Category", "Create category here.": "Create category here.", "Create category here. ": "Create category here. ", "Create client": "Create client", "Create Client": "Create Client", "Create client here.": "Create client here.", "Create Commission": "Create Commission", "Create Company": "Create Company", "Create complaint against": "Create complaint against", "Create complaint against here.": "Create complaint against here.", "Create complaint from": "Create complaint from", "Create complaint from here.": "Create complaint from here.", "Create contract type": "Create contract type", "Create contract type here.": "Create contract type here.", "Create customer": "Create customer", "Create Customer": "Create Customer", "Create customer here.": "Create customer here.", "Create Deal": "Create Deal", "Create Deal Stage": "Create Deal Stage", "Create deduction option": "Create deduction option", "Create deduction option here.": "Create deduction option here.", "Create department": "Create department", "Create department here.": "Create department here.", "Create designation": "Create designation", "Create designation here.": "Create designation here.", "Create Discover": "Create Discover", "Create Email": "Create Email", "Create employee": "Create employee", "Create Employee": "Create Employee", "Create employee here.": "Create employee here.", "Create Estimate": "Create Estimate", "Create Expense": "Create Expense", "Create FAQ": "Create FAQ", "Create Feature": "Create Feature", "Create Feature Block": "Create Feature Block", "Create goal type": "Create goal type", "Create goal type here.": "Create goal type here.", "Create Interview Schedule": "Create Interview Schedule", "Create interviewer": "Create interviewer", "Create interviewer here.": "Create interviewer here.", "Create Invoice": "Create Invoice", "Create invoice": "Create invoice", "Create invoice here.": "Create invoice here.", "Create Job": "Create Job", "Create job": "Create job", "Create job category": "Create job category", "Create job category here.": "Create job category here.", "Create job here.": "Create job here.", "Create Labels": "Create Labels", "Create Language": "Create Language", "Create Lead": "Create Lead", "Create Lead Stage": "Create Lead Stage", "Create Leave": "Create Leave", "Create leave type": "Create leave type", "Create leave type here.": "Create leave type here.", "Create Loan": "Create Loan", "Create loan option": "Create loan option", "Create loan option here.": "Create loan option here.", "Create Milestone": "Create Milestone", "Create milestone": "Create milestone", "Create milestone here.": "Create milestone here.", "Create New Account": "Create New Account", "Create New Allowance Option": "Create New Allowance Option", "Create New Announcement": "Create New Announcement", "Create New Appraisal": "Create New Appraisal", "Create New Assets": "Create New Assets", "Create New Award": "Create New Award", "Create New Award Type": "Create New Award Type", "Create New Bank Account": "Create New Bank Account", "Create New Branch": "Create New Branch", "Create New Bug": "Create New Bug", "Create New Category": "Create New Category", "Create New Client": "Create New Client", "create New Client": "create New Client", "Create New Company Policy": "Create New Company Policy", "Create New Competencies": "Create New Competencies", "Create New Complaint": "<PERSON><PERSON> <PERSON>laint", "Create New Contract": "Create New Contract", "Create New Contract Type": "Create New Contract Type", "Create New Coupon": "Create New Coupon", "Create New Credit Note": "Create New Credit Note", "Create New Custom Field": "Create New Custom Field", "Create New Custom Question": "Create New Custom Question", "Create New Deal": "Create New Deal", "Create new Deal Call": "Create new Deal Call", "Create new Deal Email": "Create new Deal Email", "Create New Debit Note": "Create New Debit Note", "Create New Deduction Option": "Create New Deduction Option", "Create New Department": "Create New Department", "Create New Designation": "Create New Designation", "Create New Document": "Create New Document", "Create New Employee": "Create New Employee", "Create New Event": "Create New Event", "Create new Expense": "Create new Expense", "Create New Field": "Create New Field", "Create New Form": "Create New Form", "Create New Goal": "Create New Goal", "Create New Goal Tracking": "Create New Goal Tracking", "Create New Goal Type": "Create New Goal Type", "Create New Holiday": "Create New Holiday", "Create New Indicator": "Create New Indicator", "Create New Interview Schedule": "Create New Interview Schedule", "Create New IP": "Create New IP", "Create New Job": "Create New Job", "Create New Job Application": "Create New Job Application", "Create New Job Category": "Create New Job Category", "Create New Job OnBoard": "Create New Job OnBoard", "Create New Job Stage": "Create New Job Stage", "Create New Journal": "Create New Journal", "Create New Language": "Create New Language", "Create New Lead": "Create New Lead", "Create new Lead Call": "Create new Lead Call", "Create new Lead Email": "Create new Lead Email", "Create New Leave Type": "Create New Leave Type", "Create New Loan Option": "Create New Loan Option", "Create New Meeting": "Create New Meeting", "Create new milestone": "Create new milestone", "Create New Payment": "Create New Payment", "Create New Payslip Type": "Create New Payslip Type", "Create New Performance Type": "Create New Performance Type", "Create New Permission": "Create New Permission", "Create New Pipeline": "Create New Pipeline", "Create New Plan": "Create New Plan", "Create New Product & Service": "Create New Product & Service", "Create New Project": "Create New Project", "Create New Promotion": "Create New Promotion", "Create New Resignation": "Create New Resignation", "Create New Revenue": "Create New Revenue", "Create New Role": "Create New Role", "Create New Sources": "Create New Sources", "Create new Task": "Create new Task", "Create New Termination": "Create New Termination", "Create New Termination Type": "Create New Termination Type", "Create New Trainer": "Create New Trainer", "Create New Training": "Create New Training", "Create New Training Type": "Create New Training Type", "Create New Transfer": "Create New Transfer", "Create New Trip": "Create New Trip", "Create New Type": "Create New Type", "Create New Unit": "Create New Unit", "Create New Vendor": "Create <PERSON>or", "Create New Warning": "Create New Warning", "Create New Webhook": "Create New Webhook", "Create Other Payment": "Create Other Payment", "Create Overtime": "Create Overtime", "Create Page": "Create Page", "Create payslip type": "Create payslip type", "Create payslip type here.": "Create payslip type here.", "Create pipeline": "Create pipeline", "Create pipeline here.": "Create pipeline here.", "Create product": "Create product", "Create product here.": "Create product here.", "Create Project": "Create Project", "Create project": "Create project", "Create project here.": "Create project here.", "Create Project Stage": "Create Project Stage", "Create Project Task Stage": "Create Project Task Stage", "Create Proposal": "Create Proposal", "Create Purchase": "Create Purchase", "Create Role": "Create Role", "Create role": "Create role", "Create role here.": "Create role here.", "Create salary type": "Create salary type", "Create salary type here.": "Create salary type here.", "Create Saturation Deduction": "Create Saturation Deduction", "Create ScreenShot": "Create ScreenShot", "Create source": "Create source", "Create source here.": "Create source here.", "Create stage": "Create stage", "Create stage here.": "Create stage here.", "Create Support": "Create Support", "Create task": "Create task", "Create task here.": "Create task here.", "Create tax": "Create tax", "Create tax here. ": "Create tax here. ", "Create Tax Rate": "Create Tax Rate", "Create termination type": "Create termination type", "Create termination type here.": "Create termination type here.", "Create Testimonial": "Create Testimonial", "Create Timesheet": "Create Timesheet", "Create trainer": "Create trainer", "Create trainer here.": "Create trainer here.", "Create training type": "Create training type", "Create training type here.": "Create training type here.", "Create unit": "Create unit", "Create unit here. ": "Create unit here. ", "Create user": "Create user", "Create User": "Create User", "Create user here.": "Create user here.", "Create vender": "Create vender", "Create vender here.": "Create vender here.", "Create vendor": "Create vendor", "Create vendor here.": "Create vendor here.", "Create warehouse": "Create warehouse", "Create Warehouse": "Create Warehouse", "Create warehouse here.": "Create warehouse here.", "Create Warehouse Transfer": "Create Warehouse Transfer", "Create warning by": "Create warning by", "Create warning by here.": "Create warning by here.", "Create warning to": "Create warning to", "Create warning to here.": "Create warning to here.", "Created": "Created", "Created At": "Created At", "created by": "created by", "Created By": "Created By", "created By": "created By", "Created Date": "Created Date", "Created new bug": "Created new bug", "Created on ": "Created on ", "Credit": "Credit", "Credit / Debit Card": "Credit / Debit Card", "Credit Note": "Credit Note", "Credit Note successfully created.": "Credit Note successfully created.", "Credit Note successfully deleted.": "Credit Note successfully deleted.", "Credit Note successfully updated.": "Credit Note successfully updated.", "Credit Note Summary": "Credit Note Summary", "Critical": "Critical", "CRM": "CRM", "CRM System": "CRM System", "CRM System Setup": "CRM System Setup", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "Currency *": "Currency *", "Currency Settings": "<PERSON><PERSON><PERSON><PERSON>", "Currency Symbol": "Currency Symbol", "Currency Symbol & Name": "Currency Symbol & Name", "Currency Symbol *": "Currency Symbol *", "Currency Symbol Position": "Currency Symbol Position", "Currency Symbol Space": "Currency Symbol Space", "Current": "Current", "Current Balance": "Current Balance", "Current cache size": "Current cache size", "Current Password": "Current Password", "Current Quantity": "Current Quantity", "Current Year": "Current Year", "Current year": "Current year", "Custom Field": "Custom Field", "Custom Field Name": "Custom Field Name", "Custom Field successfully created!": "Custom Field successfully created!", "Custom Field successfully deleted!": "Custom Field successfully deleted!", "Custom Field successfully updated!": "Custom Field successfully updated!", "Custom Page": "Custom Page", "Custom Question": "Custom Question", "Custom-Question": "Custom-Question", "Customer": "Customer", "Customer Balance": "Customer Balance", "Customer Email": "Customer <PERSON><PERSON>", "Customer Id": "Customer Id", "Customer Info": "Customer Info", "Customer Name": "Customer Name", "Customer Not Found.": "Customer Not Found.", "Customer Prefix": "Customer Prefix", "Customer successfully created.": "Customer successfully created.", "Customer successfully deleted.": "Customer successfully deleted.", "Customer successfully updated.": "Customer successfully updated.", "Customers": "Customers", "CV / Resume": "CV / Resume", "CVV": "CVV", "Daily": "Daily", "Daily Pos": "Daily Pos", "Daily Pos Report": "Daily Pos Report", "Daily Purchase": "Daily Purchase", "Daily Purchase Report": "Daily Purchase Report", "Daily Report": "Daily Report", "Dark Layout": "Dark Layout", "Dashboard": "Dashboard", "Data has been imported.": "Data has been imported.", "Data submit successfully.": "Data submit successfully.", "Date": "Date", "Date Format": "Date Format", "Date Of Birth": "Date Of Birth", "Date of Birth": "Date of Birth", "Date of Creation": "Date of Creation", "Date of Issuance": "Date of Issuance", "Date Of Joining": "Date Of Joining", "Date of POS": "Date of POS", "Day": "Day", "Day Left": "Day Left", "Days": "Days", "Days / Year": "Days / Year", "Days Of Week": "Days Of Week", "Days Per Year": "Days Per Year", "Deal": "Deal", "Deal : ": "Deal : ", "Deal Name": "Deal Name", "Deal Per Month": "Deal Per Month", "Deal Pipeline": "Deal Pipeline", "Deal Price": "Deal Price", "Deal Report": "Deal Report", "Deal Stage": "Deal Stage", "Deal Stage successfully created!": "Deal Stage successfully created!", "Deal Stage successfully deleted!": "Deal Stage successfully deleted!", "Deal Stage successfully updated!": "Deal Stage successfully updated!", "Deal Stages": "Deal Stages", "Deal Status": "Deal Status", "Deal successfully created!": "Deal successfully created!", "Deal successfully created, Webhook call failed.": "Deal successfully created, Webhook call failed.", "Deal successfully deleted!": "Deal successfully deleted!", "Deal successfully moved.": "<PERSON> successfully moved.", "Deal successfully updated!": "Deal successfully updated!", "Deal Task": "Deal Task", "Deals": "Deals", "Deals: ": "Deals: ", "Debit": "Debit", "Debit and Credit must be Equal.": "Debit and Credit must be Equal.", "Debit Note": "Debit Note", "Debit Note successfully created.": "Debit Note successfully created.", "Debit Note successfully deleted.": "Debit Note successfully deleted.", "Debit Note successfully updated.": "Debit Note successfully updated.", "Debit Note Summary": "Debit Note Summary", "December": "December", "Decimal Number Format": "Decimal Number Format", "Decimal Separator": "Decimal Separator", "Deduction": "Deduction", "Deduction Option": "Deduction Option", "Deduction Options": "Deduction Options", "Deduction Options*": "Deduction Options*", "DeductionOption  successfully created.": "DeductionOption  successfully created.", "DeductionOption successfully deleted.": "DeductionOption successfully deleted.", "DeductionOption successfully updated.": "DeductionOption successfully updated.", "Default Language": "Default Language", "Delete": "Delete", "Delete Conversation": "Delete Conversation", "Delete Customer": "Delete Customer", "Department": "Department", "Department  successfully created.": "Department  successfully created.", "Department successfully deleted.": "Department successfully deleted.", "Department successfully updated.": "Department successfully updated.", "Desciption": "Desciption", "description": "description", "Description": "Description", "Designation": "Designation", "Designation  successfully created.": "Designation  successfully created.", "Designation  successfully updated.": "Designation  successfully updated.", "Designation successfully deleted.": "Designation successfully deleted.", "Detail": "Detail", "Details": "Details", "Device": "<PERSON><PERSON>", "Device Type": "Device Type", "Disable": "Disable", "Disable User": "Disable User", "Disable Users": "Disable Users", "Disabled": "Disabled", "Discount": "Discount", "Discount (%)": "Discount (%)", "Discount:": "Discount:", "Discover": "Discover", "DISCOVER": "DISCOVER", "Discover List": "Discover List", "Discussion": "Discussion", "Display On Dashboard": "Display On Dashboard", "Display Shipping in Proposal / Invoice / Bill": "Display Shipping in Proposal / Invoice / Bill", "div": "div", "DOB": "DOB", "DOC": "DOC", "Document": "Document", "Document Detail": "Document Detail", "Document Setup": "Document Setup", "Document successfully deleted.": "Document successfully deleted.", "Document successfully uploaded.": "Document successfully uploaded.", "Document Type": "Document Type", "Document type successfully created.": "Document type successfully created.", "Document type successfully deleted.": "Document type successfully deleted.", "Document type successfully updated.": "Document type successfully updated.", "document_id": "document_id", "Don't have an account?": "Don't have an account?", "Done Task": "Done Task", "Done Tasks": "Done Tasks", "Done Training": "Done Training", "Done Training ": "Done Training ", "Dot": "Dot", "Double Entry": "Double Entry", "Download": "Download", "Download cookie accepted data": "Download cookie accepted data", "Download sample customer CSV file": "Download sample customer CSV file", "Download sample Deal CSV file": "Download sample Deal CSV file", "Download sample employee attendance CSV file": "Download sample employee attendance CSV file", "Download sample employee CSV file": "Download sample employee CSV file", "Download sample Lead CSV file": "Download sample Lead CSV file", "Download sample product CSV file": "Download sample product CSV file", "Download sample vendor CSV file": "Download sample vendor CSV file", "Due": "Due", "Due Amount": "Due Amount", "Due Date": "Due Date", "Due Date:": "Due Date:", "Due Today": "Due Today", "Duplicate": "Duplicate", "Duplicate Bill": "Duplicate Bill", "Duplicate Invoice": "Duplicate Invoice", "Duplicate Project": "Duplicate Project", "Duplicate Proposal": "Duplicate Proposal", "Duration": "Duration", "E-Mail": "E-Mail", "E-Mail Address": "E-Mail Address", "E-Mail has been not sent due to SMTP configuration": "E-Mail has been not sent due to SMTP configuration", "Early leave": "Early leave", "Early Leaving": "Early Leaving", "Earning": "Earning", "Easebuzz": "Easebuzz", "Easebuzz Enviroment Name": "Easebuzz Enviroment Name", "Edit": "Edit", "Edit ": "Edit ", "Edit Account": "Edit Account", "Edit Allowance": "Edit Allowance", "Edit Announcement": "Edit Announcement", "Edit Appraisal": "Edit Appraisal", "Edit Assets": "Edit Assets", "Edit Attendance": "Edit Attendance", "Edit Award": "Edit Award", "Edit Award Type": "Edit Award Type", "Edit Bank Account": "Edit Bank Account", "Edit Branch": "Edit Branch", "Edit Budget Planner": "Edit Budget Planner", "Edit Bug": "Edit Bug", "Edit Bug Status": "Edit Bug Status", "Edit Call": "Edit Call", "Edit Category": "Edit Category", "Edit Client": "Edit Client", "Edit Commission": "Edit Commission", "Edit Company": "Edit Company", "Edit Company Policy": "Edit Company Policy", "Edit Competencies": "Edit Competencies", "Edit Complaint": "<PERSON>", "Edit Contract": "Edit Contract", "Edit Contract Type": "Edit Contract Type", "Edit Coupon": "Edit Coupon", "Edit Credit Note": "Edit Credit Note", "Edit Custom Field": "Edit Custom Field", "Edit Custom Question": "Edit Custom Question", "Edit Customer": "Edit Customer", "Edit Deal": "Edit Deal", "Edit Deal Stages": "Edit Deal Stages", "Edit Debit Note": "Edit Debit Note", "Edit Deduction Option": "Edit Deduction Option", "Edit Department": "Edit Department", "Edit Designation": "Edit Designation", "Edit Discover": "Edit Discover", "Edit Document": "Edit Document", "Edit Document Type": "Edit Document Type", "Edit email notification settings": "Edit email notification settings", "Edit Employee": "Edit Employee", "Edit Employee salary": "Edit Employee salary", "Edit Estimation": "Edit Estimation", "Edit Estimation Product": "Edit Estimation Product", "Edit Event": "Edit Event", "Edit FAQ": "Edit FAQ", "Edit Feature": "Edit Feature", "Edit Feature Block": "Edit Feature Block", "Edit Form Field": "Edit Form Field", "Edit Goal": "Edit Goal", "Edit Goal Tracking": "Edit Goal Tracking", "Edit Goal Type": "Edit Goal Type", "Edit Holiday": "Edit Holiday", "Edit Indicator": "Edit Indicator", "Edit Interview Schedule": "Edit Interview Schedule", "Edit IP": "Edit IP", "Edit Job": "Edit Job", "Edit Job Category": "Edit Job Category", "Edit Job Stage": "Edit Job Stage", "Edit Journal": "Edit Journal", "Edit Labels": "Edit Labels", "Edit Lead": "Edit Lead", "Edit Lead Stages": "Edit Lead Stages", "Edit Leave": "Edit Leave", "Edit Leave Type": "Edit Leave Type", "Edit Loan": "<PERSON>an", "Edit Loan Option": "Edit <PERSON><PERSON>", "Edit Meeting": "Edit Meeting", "Edit Milestone": "<PERSON>", "Edit Other Payment": "Edit Other Payment", "Edit OverTime": "Edit OverTime", "Edit Page": "Edit Page", "Edit Payment": "Edit Payment", "Edit Payslip Type": "Edit Payslip Type", "Edit Performance Type": "Edit Performance Type", "Edit Pipeline": "<PERSON>", "Edit Plan": "Edit Plan", "Edit Product": "Edit Product", "Edit Project": "Edit Project", "Edit project Expense": "Edit project Expense", "Edit Project Stages": "Edit Project Stages", "Edit Promotion": "Edit Promotion", "Edit Resignation": "Edit Resignation", "Edit Revenue": "Edit Revenue", "Edit Saturation Deduction": "Edit Saturation Deduction", "Edit Screenshot": "Edit Screenshot", "Edit Source": "Edit Source", "Edit Support": "Edit Support", "Edit Task": "Edit Task", "Edit Tax Rate": "Edit Tax Rate", "Edit Termination": "Edit Termination", "Edit Testimonial": "Edit Testimonial", "Edit Timesheet": "Edit Timesheet", "Edit Trainer": "<PERSON> Trainer", "Edit Training": "Edit Training", "Edit Training Type": "Edit Training Type", "Edit Transfer": "Edit Transfer", "Edit Trip": "Edit Trip", "Edit Unit": "Edit Unit", "Edit User": "Edit User", "Edit Vendor": "<PERSON>", "Edit Warehouse": "Edit Warehouse", "Edit Warning": "Edit Warning", "Edit your brand details": "Edit your brand details", "Edit your company details": "Edit your company details", "Edit your currency details": "Edit your currency details", "Edit your Slack settings": "Edit your Slack settings", "Edit your system details": "Edit your system details", "Edit your Telegram settings": "Edit your Telegram settings", "Edit your Time Tracker settings": "Edit your Time Tracker settings", "Edit your Twilio settings": "Edit your Twilio settings", "Edit your Zoom settings": "Edit your Zoom settings", "Email": "Email", "Email already exist in our record.!": "Email already exist in our record.!", "Email Message": "Email Message", "Email Notification": "Email Notification", "Email Notification Settings": "Email Notification Settings", "Email send Successfully": "Email send Successfully", "Email Send successfully!": "Email Send successfully!", "Email Settings": "<PERSON><PERSON>s", "Email SMTP settings does not configure so please contact to your site admin.": "Email SMTP settings does not configure so please contact to your site admin.", "Email SMTP settings does not configured so please contact to your site admin.": "Email SMTP settings does not configured so please contact to your site admin.", "Email successfully created!": "Email successfully created!", "Email Template": "<PERSON>ail Te<PERSON>late", "Email Template Detail successfully updated.": "Email Template Detail successfully updated.", "Email Template successfully created.": "Email Template successfully created.", "Email Template successfully updated.": "Email Template successfully updated.", "Email Templates": "Email Templates", "Email Verification": "Email Verification", "Emails": "Emails", "Employee": "Employee", "employee": "employee", "Employee ": "Employee ", "Employee  successfully created.": "Employee  successfully created.", "Employee are not allow multiple time clock in & clock for every day.": "Employee are not allow multiple time clock in & clock for every day.", "Employee Attendance Already Created.": "Employee Attendance Already Created.", "Employee attendance successfully created.": "Employee attendance successfully created.", "Employee attendance successfully updated.": "Employee attendance successfully updated.", "Employee Code": "Employee Code", "Employee Detail": "Employee Detail", "Employee Details": "Employee Details", "Employee Email": "Employee Email", "Employee ID": "Employee ID", "Employee Id": "Employee Id", "Employee late": "Employee late", "Employee Name": "Employee Name", "Employee not found.": "Employee not found.", "Employee Not Found.": "Employee Not Found.", "Employee payroll successfully updated.": "Employee payroll successfully updated.", "Employee Payslip": "Employee Payslip", "Employee Profile": "Employee Profile", "Employee Salary": "Employee Salary", "Employee Salary Pay Slip": "Employee Salary Pay Slip", "Employee Set Salary": "Employee Set Salary", "Employee Setup": "Employee Setup", "Employee Signature": "Employee Signature", "Employee Successfully Clock In.": "Employee Successfully Clock In.", "Employee successfully clock Out.": "Employee successfully clock Out.", "Employee successfully created.": "Employee successfully created.", "Employee successfully Sync.": "Employee successfully Sync.", "EmployeeId": "EmployeeId", "Employees Asset Setup ": "Employees As<PERSON>up ", "Empty Cart": "Empty Cart", "Enable": "Enable", "Enable cookie": "Enable cookie", "Enable Landing Page": "Enable Landing Page", "Enable logging": "Enable logging", "Enable RTL": "Enable RTL", "Enable Sign-Up Page": "Enable Sign-Up Page", "Enable/Disable": "Enable/Disable", "Enable:": "Enable:", "Enabled": "Enabled", "End": "End", "End Date": "End Date", "End Date   :": "End Date   :", "End Date : ": "End Date : ", "End Month": "End Month", "End Time": "End Time", "Enter account holder name": "Enter account holder name", "Enter Account Number": "Enter Account Number", "Enter account number": "Enter account number", "Enter Address": "Enter Address", "Enter Allowance option Name": "Enter Allowance option Name", "Enter Amount": "Enter Amount", "Enter Announcement Title": "Enter Announcement Title", "Enter Answer": "Enter Answer", "Enter Award Type Name": "Enter Award Type Name", "Enter Bank Address": "Enter Bank Address", "Enter Bank Holder Name": "Enter Bank Holder Name", "Enter bank identifier code": "Enter bank identifier code", "Enter Bank Name": "Enter Bank Name", "Enter bank name": "Enter bank name", "Enter Benefit Key": "Enter Benefit Key", "Enter Benefit Secret key": "Enter Benefit Secret key", "Enter Bill Prifix": "Enter <PERSON>", "Enter Billing Address": "Enter Billing Address", "Enter Billing City": "Enter Billing City", "Enter Billing Country": "Enter Billing Country", "Enter Billing Name": "Enter Billing Name", "Enter Billing Phone": "Enter Billing Phone", "Enter Billing State": "Enter Billing State", "Enter Billing Zip": "Enter Billing Zip", "Enter branch location": "Enter branch location", "Enter Branch Name": "Enter Branch Name", "Enter Bug Status Title": "Enter Bug Status Title", "Enter Cashfree Key": "Enter Cashfree Key", "Enter Cashfree Secret key": "Enter Cashfree Secret key", "Enter Category Name": "Enter Category Name", "Enter category title": "Enter category title", "Enter Channel": "Enter Channel", "Enter Chat GPT API Key": "Enter Chat GPT API Key", "Enter Chat GPT Modal Name": "Enter Chat GPT Modal Name", "Enter City": "Enter City", "Enter Client Email": "Enter Client Email", "Enter Client Name": "Enter Client Name", "Enter client Name": "Enter client Name", "Enter Client Password": "Enter Client Password", "Enter Code": "Enter Code", "Enter Comment": "Enter Comment", "Enter Commission Percentage": "Enter Commission Percentage", "Enter Company Address": "Enter Company Address", "Enter Company City": "Enter Company City", "Enter Company Country": "Enter Company Country", "Enter Company Email": "Enter Company Email", "Enter Company Name": "Enter Company Name", "Enter Company Password": "Enter Company Password", "Enter Company Registration Number": "Enter Company Registration Number", "Enter Company State": "Enter Company State", "Enter Company Telephone": "Enter Company Telephone", "Enter Company Zip": "Enter Company Zip", "Enter Competencies Name": "Enter Competencies Name", "Enter Complaint Title": "<PERSON><PERSON> Complaint Title", "Enter Confirm Password": "Enter Confirm Password", "Enter Contact": "Enter Contact", "Enter Contract Type Name": "Enter Contract Type Name", "Enter Contract Value": "Enter Contract Value", "Enter Cost": "<PERSON>ter Cost", "Enter Country": "Enter Country", "Enter Coupon Code": "Enter Coupon Code", "Enter Cover Latter": "Enter Cover Latter", "Enter Currency": "<PERSON><PERSON>", "Enter Currency Symbol": "Enter Currency Symbol", "Enter Current Password": "Enter Current Password", "Enter Custom Field Name": "Enter Custom Field Name", "Enter Customer Prefix": "Enter Customer Prefix", "Enter Date of Birth": "Enter Date of Birth", "Enter Days / Year": "Enter Days / Year", "Enter Deal Name": "Enter Deal Name", "Enter Deal Stage Name": "Enter Deal Stage Name", "Enter Deduction Option Name": "Enter Deduction Option Name", "Enter Department Name": "Enter Department Name", "Enter Description": "Enter Description", "Enter Designation": "Enter Designation", "Enter Designation Name": "Enter Designation Name", "Enter Discount": "Enter Discount", "Enter Document Name": "Enter Document Name", "Enter Duration in minutes": "Enter Duration in minutes", "Enter email": "Enter email", "Enter Email": "<PERSON><PERSON>", "Enter employee address": "Enter employee address", "Enter employee name": "Enter employee name", "Enter Estimated Hours": "Enter Estimated Hours", "Enter Event Description": "Enter Event Description", "Enter Event Title": "Enter Event Title", "Enter Expense Prifix": "Enter Expense Prifix", "Enter Expertise": "Enter Expertise", "Enter First Name": "Enter First Name", "Enter Footer Text": "Enter Footer Text", "Enter Footer Title": "Enter Footer Title", "Enter From Name": "Enter From Name", "Enter Gift": "Enter Gift", "Enter Goal Type Name": "Enter Goal Type Name", "Enter Google Recaptcha Key": "Enter Google Recaptcha Key", "Enter Google Recaptcha Secret": "Enter Google Recaptcha Secret", "Enter Heading": "Enter Heading", "Enter Hours": "Enter Hours", "Enter Invoice Prifix": "Enter Invoice Prifix", "Enter IP": "Enter IP", "Enter Job Requirement": "Enter Job Requirement", "Enter Job Title": "Enter Job Title", "Enter Journal Prifix": "Enter Journal Prifix", "Enter Label Name": "Enter Label Name", "Enter Last Name": "Enter Last Name", "Enter Lead Stage Name": "Enter Lead Stage Name", "Enter Leave Type Name": "Enter Leave Type Name", "Enter Limit": "Enter Limit", "Enter Link": "Enter Link", "Enter Loan Option Name": "Enter Loan Option Name", "Enter Long Description": "Enter Long Description", "Enter Mail Driver": "Enter Mail Driver", "Enter Mail Encryption": "Enter Mail Encryption", "Enter Mail From Address": "Enter Mail From Address", "Enter Mail From Name": "Enter Mail From Name", "Enter Mail Host": "Enter Mail Host", "Enter Mail Password": "Enter Mail Password", "Enter Mail Port": "Enter Mail Port", "Enter Mail To": "Enter Mail To", "Enter Mail Username": "Enter Mail Username", "Enter Maximum Clients": "Enter Maximum Clients", "Enter Maximum Customers": "Enter Maximum Customers", "Enter Maximum Users": "Enter Maximum Users", "Enter Maximum Vendors": "Enter Maximum Vendors", "Enter Meeting Note": "Enter Meeting Note", "Enter Meeting Title": "Enter Meeting Title", "Enter Message": "Enter Message", "Enter Minimum Payout": "Enter Minimum Payout", "Enter Mobile No": "Enter Mobile No", "Enter Mobile Number": "Enter Mobile Number", "Enter Name": "Enter Name", "Enter New Password": "Enter New Password", "Enter Note": "Enter Note", "Enter Number": "Enter Number", "Enter Number of days": "Enter Number of days", "Enter Occation": "Enter Occation", "Enter Old Password": "Enter Old Password", "Enter Opening Balance": "Enter Opening Balance", "Enter Order Number": "Enter Order Number", "Enter Owner Name": "Enter Owner Name", "Enter Page Name": "Enter Page Name", "Enter Page URL": "Enter Page URL", "Enter Password": "Enter Password", "Enter Payslip Type Name": "Enter Payslip Type Name", "Enter Performance Type Name": "Enter Performance Type Name", "Enter Permission Name": "Enter Permission Name", "Enter Phone": "Enter Phone", "Enter Phone no.": "Enter Phone no.", "Enter Pipeline Name": "Enter Pipeline Name", "Enter Place of Visit": "Enter Place of Visit", "Enter Plan Name": "Enter Plan Name", "Enter Plan Price": "Enter Plan Price", "Enter Pos Prifix": "Enter Pos Prifix", "Enter Positions": "Enter Positions", "Enter Price": "Enter Price", "Enter Project Budget": "Enter Project Budget", "Enter Project Estimated Hours": "Enter Project Estimated Hours", "Enter Project Name": "Enter Project Name", "Enter Project Stage Name": "Enter Project Stage Name", "Enter Project Tag": "Enter Project Tag", "Enter Project Task Stage Name": "Enter Project Task Stage Name", "Enter Promotion Title": "Enter Promotion Title", "Enter Proposal Prifix": "Enter Proposal Prifix", "Enter Purchase Price": "Enter Purchase Price", "Enter Purchase Prifix": "Enter Purchase Prifix", "Enter Purpose of Visit": "<PERSON><PERSON> of Visit", "Enter Quantity": "Enter Quantity", "Enter question": "Enter question", "Enter Question": "Enter Question", "Enter Question Name": "Enter Question Name", "Enter Questions": "Enter Questions", "Enter Quotation Prifix": "Enter Quotation Prifix", "Enter Rate": "Enter Rate", "Enter Re-type New Password": "Enter Re-type New Password", "Enter Reason": "Enter Reason", "Enter Ref NUmber": "Enter Ref <PERSON>", "Enter Reference": "Enter Reference", "Enter Request Amount": "Enter Request Amount", "Enter Role Name": "Enter Role Name", "Enter Salary": "<PERSON>ter <PERSON>", "Enter Sale Price": "Enter Sale Price", "Enter Shipping Address": "Enter Shipping Address", "Enter Shipping City": "Enter Shipping City", "Enter Shipping Country": "Enter Shipping Country", "Enter Shipping Name": "Enter Shipping Name", "Enter Shipping Phone": "Enter Shipping Phone", "Enter Shipping State": "Enter Shipping State", "Enter Shipping Zip": "Enter Shipping Zip", "Enter Signature Key": "Enter Signature Key", "Enter Skrill Email": "<PERSON><PERSON>", "Enter Sku": "Enter Sku", "Enter Slack Webhook URL": "Enter Slack Webhook URL", "Enter Source Name": "Enter Source Name", "Enter stage title": "Enter stage title", "Enter Star": "Enter Star", "Enter State": "Enter State", "Enter Store Id": "Enter Store Id", "Enter Stripe Key": "Enter Stripe Key", "Enter Stripe Secret": "Enter Stripe Secret", "Enter Subject": "Enter Subject", "Enter Support": "Enter Support", "Enter Target Achievement": "Enter Target Achievement", "Enter Task Name": "Enter Task Name", "Enter Tax Number": "Enter Tax Number", "Enter tax payer id": "Enter tax payer id", "Enter Tax Rate Name": "Enter Tax Rate Name", "Enter Telegram AccessToken": "Enter Telegram AccessToken", "Enter Telegram ChatID": "Enter Telegram ChatID", "Enter Termination Type Name": "Enter Termination Type Name", "Enter Title": "Enter Title", "Enter Tracking Interval Time": "Enter Tracking Interval Time", "Enter Training Type Name": "Enter Training Type Name", "Enter Trial days": "Enter Trial days", "Enter Twilio From": "<PERSON><PERSON>", "Enter Twilio SID": "<PERSON><PERSON>", "Enter Twilio Token": "<PERSON><PERSON> <PERSON>", "Enter Unit Name": "Enter Unit Name", "Enter User Contact": "Enter User Contact", "Enter User Email": "Enter User Email", "Enter User Name": "Enter User Name", "Enter valid amount.": "Enter valid amount.", "Enter VAT / GST Number": "Enter VAT / GST Number", "Enter Vendor Prifix": "<PERSON><PERSON> Vendor Prifi<PERSON>", "Enter Webhook Url": "Enter Webhook Url", "Enter Your Bank Details": "Enter Your Bank Details", "Enter Your Confirm Password": "Enter Your Confirm Password", "Enter your cover letter": "Enter your cover letter", "Enter Your Email": "Enter Your Email", "Enter Your Email Address": "Enter Your Email Address", "Enter Your Name": "Enter Your Name", "Enter Your New Password": "Enter Your New Password", "Enter Your Password": "Enter Your Password", "Enter Zip": "<PERSON><PERSON>", "Enter Zip Code": "Enter Zip Code", "Enter Zoom Accound Id": "Enter Zoom Accound Id", "Enter Zoom Client Id": "Enter Zoom Client Id", "Enter Zoom Client Secret Key": "Enter Zoom Client Secret Key", "ERPGo": "ERPGo", "ERPGo SaaS": "ERPGo SaaS", "Error": "Error", "error": "error", "Estimate": "Estimate", "Estimated Hours": "Estimated Hours", "ESTIMATION": "ESTIMATION", "Estimation Detail": "Estimation Detail", "Event": "Event", "Event  successfully created.": "Event  successfully created.", "Event Description": "Event Description", "Event End Date": "Event End Date", "Event Select Color": "Event Select Color", "Event Setup": "Event Setup", "Event Start Date": "Event Start Date", "Event start Date": "Event start Date", "Event successfully created.": "Event successfully created.", "Event successfully deleted.": "Event successfully deleted.", "Event successfully updated.": "Event successfully updated.", "Event Title": "Event Title", "Event View": "Event View", "Example : Bank : bank name Account Number : 0000 0000": "Example : Bank : bank name Account Number : 0000 0000", "Example:": "Example:", "Existing Client": "Existing Client", "Exit Company Login": "Exit Company Login", "Expand": "Expand", "Expense": "Expense", "Expense :": "Expense :", "Expense : ": "Expense : ", "Expense = Payment + Bill :": "Expense = Payment + Bill :", "Expense Account": "Expense Account", "Expense added successfully.": "Expense added successfully.", "Expense By Category": "Expense By Category", "Expense Create": "Expense Create", "Expense Deleted successfully.": "Expense Deleted successfully.", "Expense Detail": "Expense Detail", "Expense Edit": "Expense Edit", "Expense Not Found.": "Expense Not Found.", "Expense Number": "Expense Number", "Expense Prefix": "Expense Prefix", "Expense product successfully deleted.": "Expense product successfully deleted.", "Expense successfully created, Webhook call failed.": "Expense successfully created, Webhook call failed.", "Expense successfully created.": "Expense successfully created.", "Expense successfully deleted.": "Expense successfully deleted.", "Expense successfully updated.": "Expense successfully updated.", "Expense Summary": "Expense Summary", "Expense tax not found": "Expense tax not found", "Expense This Month": "Expense This Month", "Expense Today": "Expense Today", "Expense Updated successfully.": "Expense Updated successfully.", "Expenses": "Expenses", "Experience Certificate": "Experience Certificate", "Experience Certificate Settings": "Experience Certificate Settings", "Experience Certificate successfully saved!": "Experience Certificate successfully saved!", "Expertise": "Expertise", "Expiration": "Expiration", "Export": "Export", "FAQ": "FAQ", "FAQ List": "FAQ List", "Favicon": "Favicon", "Favorites": "Favorites", "Feature": "Feature", "Features": "Features", "Features Block": "Features Block", "Features List": "Features List", "February": "February", "Fedapay": "Fedapay", "FedaPay": "FedaPay", "Feel free to reach out if you have any questions.": "Feel free to reach out if you have any questions.", "Female": "Female", "Field not found.": "Field not found.", "Field successfully created.": "Field successfully created.", "File Added Successfully!": "File Added Successfully!", "File Deleted Successfully!": "File Deleted Successfully!", "file here": "file here", "File is not exist.": "File is not exist.", "Files": "Files", "Filter": "Filter", "Financial Goal": "Financial Goal", "Find Employee Payslip": "Find Employee Payslip", "Finished": "Finished", "First Name": "First Name", "Float Number": "Float Number", "Flutterwave": "Flutterwave", "Footer": "Footer", "Footer Text": "Footer Text", "For chart representation": "For chart representation", "For What": "For What", "Forgot Password": "Forgot Password", "Forgot Your Password?": "Forgot Your Password?", "Forgot your password?": "Forgot your password?", "Form Builder": "Form Builder", "Form Builder Edit": "Form Builder Edit", "Form field": "Form field", "Form is not active.": "Form is not active.", "Form not found please contact to admin.": "Form not found please contact to admin.", "Form successfully created.": "Form successfully created.", "Form successfully deleted!": "Form successfully deleted!", "Form successfully deleted.": "Form successfully deleted.", "Form successfully updated.": "Form successfully updated.", "Free Trial Days : ": "Free Trial Days : ", "Fri": "<PERSON><PERSON>", "from": "from", "From": "From", "From A-Z": "From A-Z", "From Account": "From Account", "From Date": "From Date", "From Warehouse": "From Warehouse", "From Z-A": "From Z-A", "From:": "From:", "FROM:": "FROM:", "Full Name": "Full Name", "Gantt Chart": "Gantt Chart", "Gender": "Gender", "General": "General", "General Report": "General Report", "Generate": "Generate", "Generate content with AI": "Generate content with AI", "Generate Content With AI": "Generate Content With AI", "Generate Payslip": "Generate Payslip", "Generate Token": "Generate Token", "Generate with AI": "Generate with AI", "Get Paid": "Get Paid", "Gift": "Gift", "Goal": "Goal", "Goal successfully created.": "Goal successfully created.", "Goal successfully deleted.": "Goal successfully deleted.", "Goal successfully updated.": "Goal successfully updated.", "Goal Tracking": "Goal Tracking", "Goal tracking successfully created.": "Goal tracking successfully created.", "Goal tracking successfully updated.": "Goal tracking successfully updated.", "Goal Type": "Goal Type", "GoalTracking successfully deleted.": "GoalTracking successfully deleted.", "GoalType  successfully created.": "GoalType  successfully created.", "GoalType successfully deleted.": "GoalType successfully deleted.", "GoalType successfully updated.": "GoalType successfully updated.", "GoalTypes": "GoalTypes", "Google Calendar Id": "Google Calendar Id", "Google Calendar json File": "Google Calendar json File", "Google Calendar Settings": "Google Calendar Settings", "Google Calender": "Google Calender", "Google Recaptcha Key": "Google Recaptcha Key", "Google Recaptcha Secret": "Google Recaptcha Secret", "Google Recaptcha Version": "Google Recaptcha Version", "Grammar check with AI": "Grammar check with AI", "Grid View": "Grid View", "Gross Profit": "Gross Profit", "GST Number": "GST Number", "GuideLine": "GuideLine", "GuideLines": "GuideLines", "Half Day": "Half Day", "has assigned task ": "has assigned task ", "has invited": "has invited", "has removed ": "has removed ", "Header": "Header", "Heading": "Heading", "Hello ": "Hello ", "here": "here", "here.": "here.", "Hi Dear,": "Hi <PERSON>,", "Hi, ": "Hi, ", "High": "High", "Holder Name": "Holder Name", "Holiday": "Holiday", "Holiday Date": "Holiday Date", "Holiday List": "Holiday List", "Holiday successfully created, Webhook call failed.": "Holiday successfully created, Webhook call failed.", "Holiday Title": "Holiday Title", "Holidays": "Holidays", "Home": "Home", "Home Section": "Home Section", "Horizontal View": "Horizontal View", "Hours": "Hours", "Hours Estimation": "Hours Estimation", "How to Get Google reCaptcha Site and Secret key": "How to Get Google reCaptcha Site and Secret key", "HR Admin Setup": "HR Admin Setup", "HRM": "HRM", "HRM ": "HRM ", "HRM System": "HRM System", "HRM System Setup": "HRM System Setup", "I agree to the ": "I agree to the ", "Id": "Id", "Image": "Image", "Image Screenshot Take Interval time ( 1 = 1 min)": "Image Screenshot Take Interval time ( 1 = 1 min)", "Import": "Import", "Import Attendance CSV Data": "Import Attendance CSV Data", "Import customer CSV file": "Import customer CSV file", "Import Customers CSV Data": "Import Customers CSV Data", "Import Deal CSV Data": "Import Deal CSV Data", "Import Deal CSV file": "Import Deal CSV file", "Import employee attendance CSV file": "Import employee attendance CSV file", "Import Employee CSV Data": "Import Employee CSV Data", "Import employee CSV file": "Import employee CSV file", "Import Lead CSV Data": "Import Lead CSV Data", "Import Lead CSV file": "Import Lead CSV file", "Import product CSV file": "Import product CSV file", "Import product services CSV Data": "Import product services CSV Data", "Import Venders CSV Data": "Import Venders CSV Data", "Import Vendor CSV File": "Import Vendor CSV File", "In": "In", "in deal": "in deal", "in estimation": "in estimation", "in lead": "in lead", "In Progress": "In Progress", "Inactive": "Inactive", "Inactive Job ": "Inactive Job ", "Inactive Jobs": "Inactive Jobs", "Inbound": "Inbound", "Income": "Income", "Income & Expense": "Income & Expense", "Income :": "Income :", "Income : ": "Income : ", "Income = Revenue + Invoice :": "Income = Revenue + Invoice :", "Income Account": "Income Account", "Income By Category": "Income By Category", "Income Period": "Income Period", "Income Summary": "Income Summary", "Income tax not found": "Income tax not found", "Income This Month": "Income This Month", "Income Today": "Income Today", "Income VS Expense": "Income VS Expense", "Income Vs Expense": "Income Vs Expense", "Income Vs Expense Summary": "Income Vs Expense Summary", "Income vs Expense Summary": "Income vs Expense Summary", "Incomplete": "Incomplete", "Indicator": "Indicator", "Indicator Detail": "Indicator <PERSON>ail", "Indicator successfully created.": "Indicator successfully created.", "Indicator successfully deleted.": "Indicator successfully deleted.", "Indicator successfully updated.": "Indicator successfully updated.", "Industry Type": "Industry Type", "Interview Date": "Interview Date", "Interview On": "Interview On", "Interview Schedule": "Interview Schedule", "Interview schedule successfully created.": "Interview schedule successfully created.", "Interview schedule successfully deleted.": "Interview schedule successfully deleted.", "Interview schedule successfully updated.": "Interview schedule successfully updated.", "Interview Time": "Interview Time", "Interview To": "Interview To", "Interviewer": "Interviewer", "Invalid amount.": "Invalid amount.", "Invalid Client.": "Invalid Client.", "Invalid Permission.": "Invalid Permission.", "Invite Client For Zoom Meeting": "Invite Client For Zoom Meeting", "Invite User": "Invite User", "Invoice": "Invoice", "INVOICE": "INVOICE", "Invoice :": "Invoice :", "Invoice : ": "Invoice : ", "Invoice Balance": "Invoice Balance", "Invoice Count": "Invoice Count", "Invoice Create": "Invoice Create", "Invoice Detail": "Invoice Detail", "Invoice Due Date": "Invoice Due Date", "Invoice duplicate successfully.": "Invoice duplicate successfully.", "Invoice Edit": "Invoice Edit", "Invoice Generated": "Invoice Generated", "Invoice is deleted.": "Invoice is deleted.", "Invoice Issue Date": "Invoice Issue Date", "Invoice Item": "Invoice Item", "Invoice Logo": "Invoice Logo", "Invoice Name": "Invoice Name", "Invoice not found": "Invoice not found", "Invoice not found!": "Invoice not found!", "Invoice not found.": "Invoice not found.", "Invoice Not Found.": "Invoice Not Found.", "Invoice Number": "Invoice Number", "Invoice paid Successfully!": "Invoice paid Successfully!", "Invoice Payment Amount": "Invoice Payment Amount", "Invoice payment amount should not greater than subtotal.": "Invoice payment amount should not greater than subtotal.", "Invoice Payment Date": "Invoice Payment Date", "Invoice Payment Method": "Invoice Payment Method", "Invoice Payment Number": "Invoice Payment Number", "Invoice payment request send successfully.": "Invoice payment request send successfully.", "Invoice payment request status updated successfully.": "Invoice payment request status updated successfully.", "Invoice Prefix": "Invoice Prefix", "Invoice Print Setting": "Invoice Print Setting", "Invoice product not found!": "Invoice product not found!", "Invoice product successfully deleted.": "Invoice product successfully deleted.", "Invoice Reminder": "Invoice Reminder", "Invoice Setting updated successfully": "Invoice Setting updated successfully", "Invoice Status": "Invoice Status", "Invoice successfully .": "Invoice successfully .", "Invoice successfully created, Webhook call failed.": "Invoice successfully created, Webhook call failed.", "Invoice successfully created.": "Invoice successfully created.", "Invoice successfully deleted.": "Invoice successfully deleted.", "Invoice successfully sent.": "Invoice successfully sent.", "Invoice successfully updated.": "Invoice successfully updated.", "Invoice Summary": "Invoice Summary", "Invoice Template": "Invoice Template", "Invoice Url": "Invoice Url", "Invoices": "Invoices", "Invoices Monthly Statistics": "Invoices Monthly Statistics", "Invoices Weekly Statistics": "Invoices Weekly Statistics", "IP": "IP", "Ip": "Ip", "Ip Restrict": "Ip Restrict", "IP Restriction Settings": "IP Restriction Settings", "IP successfully created.": "IP successfully created.", "IP successfully deleted.": "IP successfully deleted.", "IP successfully updated.": "IP successfully updated.", "Is Dashboard Display": "Is Dashboard Display", "Is Enabled": "Is Enabled", "Is Required": "Is Required", "Isp": "Isp", "Issue Date": "Issue Date", "Issue Date:": "Issue Date:", "It will auto convert from response on lead based on below setting. It will not convert old response.": "It will auto convert from response on lead based on below setting. It will not convert old response.", "It's looking like you may have taken a wrong turn. Don't worry... it happens to the best of us. Here's a little tip that might help you get back on track.": "It's looking like you may have taken a wrong turn. Don't worry... it happens to the best of us. Here's a little tip that might help you get back on track.", "Item": "<PERSON><PERSON>", "Item description": "Item description", "Item Name": "Item Name", "Item Price": "<PERSON><PERSON>", "Items": "Items", "Items not found!": "Items not found!", "Iyzipay": "Iyzipay", "Iyzipay Mode": "Iyzipay Mode", "Jan-Mar": "Jan-<PERSON>", "January": "January", "Job": "Job", "Job  successfully created.": "Job  successfully created.", "Job  successfully deleted.": "Job  successfully deleted.", "Job  successfully updated.": "Job  successfully updated.", "Job Application": "Job Application", "Job Application Details": "Job Application Details", "Job application not found.": "Job application not found.", "Job application notes successfully added.": "Job application notes successfully added.", "Job application notes successfully deleted.": "Job application notes successfully deleted.", "Job application skill successfully added.": "Job application skill successfully added.", "Job application successfully added to archive.": "Job application successfully added to archive.", "Job application successfully created.": "Job application successfully created.", "Job application successfully deleted.": "Job application successfully deleted.", "Job application successfully remove to archive.": "Job application successfully remove to archive.", "Job application successfully send": "Job application successfully send", "Job application successfully updated.": "Job application successfully updated.", "Job board Candidate succefully updated.": "Job board Candidate succefully updated.", "Job Candidate": "Job Candidate", "Job Category": "Job Category", "Job category  successfully created.": "Job category  successfully created.", "Job category  successfully updated.": "Job category  successfully updated.", "Job category successfully deleted.": "Job category successfully deleted.", "Job Create": "Job Create", "Job Description": "Job Description", "Job Detail": "Job Detail", "Job Details": "Job Details", "Job Edit": "Job Edit", "Job Not Found.": "Job Not Found.", "Job On-boarding": "Job On-boarding", "Job onBoard successfully deleted.": "Job onBoard successfully deleted.", "Job openings": "Job openings", "Job Requirement": "Job Requirement", "Job Stage": "Job Stage", "Job stage  successfully created.": "Job stage  successfully created.", "Job stage  successfully updated.": "Job stage  successfully updated.", "Job stage successfully deleted.": "Job stage successfully deleted.", "Job Title": "Job Title", "Job title": "Job title", "Job type": "Job type", "Job Type": "Job Type", "Jobs": "Jobs", "Join meeting": "Join meeting", "Join URL": "Join <PERSON>", "Join Us": "Join Us", "Join Us User": "Join <PERSON>r", "Join User": "Join User", "Joing Letter successfully saved!": "Joing <PERSON> successfully saved!", "Joining at": "Joining at", "Joining Date": "Joining Date", "Joining Letter": "Joining Letter", "Joining Letter Settings": "Joining Letter Settings", "Journal": "Journal", "Journal Account": "Journal Account", "Journal account successfully deleted.": "Journal account successfully deleted.", "Journal Account Summary": "Journal Account Summary", "Journal Date": "Journal Date", "Journal Detail": "Journal Detail", "Journal Entry": "Journal Entry", "Journal entry account successfully deleted.": "Journal entry account successfully deleted.", "Journal Entry Create": "Journal Entry Create", "Journal Entry Edit": "Journal Entry Edit", "Journal entry successfully created.": "Journal entry successfully created.", "Journal entry successfully deleted.": "Journal entry successfully deleted.", "Journal entry successfully updated.": "Journal entry successfully updated.", "Journal ID": "Journal ID", "Journal No": "Journal No", "Journal Number": "Journal Number", "Journal Prefix": "Journal Prefix", "Journal Ref": "Journal Ref", "Jul-Sep": "Jul-Sep", "July": "July", "June": "June", "Kanban": "Ka<PERSON><PERSON>", "Kanban View": "Kanban View", "Khalti": "Khalti", "Label": "Label", "label": "label", "Label Name": "Label Name", "Label successfully created!": "Label successfully created!", "Label successfully deleted!": "Label successfully deleted!", "Label successfully updated!": "Label successfully updated!", "Labels": "Labels", "Labels successfully updated!": "Labels successfully updated!", "Landing Page": "<PERSON>", "Language": "Language", "Language Change Successfully!": "Language Change Successfully!", "Language change successfully.": "Language change successfully.", "Language Code": "Language Code", "Language Deleted Successfully.": "Language Deleted Successfully.", "Language Disabled Successfully": "Language Disabled Successfully", "Language Enabled Successfully": "Language Enabled Successfully", "Language Name": "Language Name", "Language save successfully.": "Language save successfully.", "Language successfully change.": "Language successfully change.", "Language successfully created.": "Language successfully created.", "Last 10 Days": "Last 10 Days", "Last 30 Days Total Deals": "Last 30 Days Total Deals", "Last 30 Days Total Estimate": "Last 30 Days Total Estimate", "Last 7 Days": "Last 7 Days", "Last 7 days": "Last 7 days", "Last 7 days hours spent": "Last 7 days hours spent", "Last 7 days task done": "Last 7 days task done", "Last Login": "Last Login", "Last Name": "Last Name", "Last week": "Last week", "Last Working Date": "Last Working Date", "Late": "Late", "Latest Contract": "Latest Contract", "Latest Expense": "Latest Expense", "Latest Income": "Latest Income", "Latitude": "Latitude", "Layout settings": "Layout settings", "Lead": "Lead", "Lead : ": "Lead : ", "Lead Detail": "Lead Detail", "Lead Edit": "Lead Edit", "Lead Email": "Lead Email", "Lead Name": "Lead Name", "Lead Per Month": "Lead Per Month", "Lead Pipeline": "Lead Pipeline", "Lead Report": "Lead Report", "Lead Stage": "Lead Stage", "Lead Stage Name": "Lead Stage Name", "Lead Stage successfully created!": "Lead Stage successfully created!", "Lead Stage successfully deleted!": "Lead Stage successfully deleted!", "Lead Stage successfully updated!": "Lead Stage successfully updated!", "Lead Stages": "Lead Stages", "Lead Status": "Lead Status", "Lead Subject": "Lead Subject", "Lead successfully converted": "Lead successfully converted", "Lead successfully converted!": "Lead successfully converted!", "Lead successfully converted, Webhook call failed.": "Lead successfully converted, Webhook call failed.", "Lead successfully created!": "Lead successfully created!", "Lead successfully created, Webhook call failed.": "Lead successfully created, Webhook call failed.", "Lead successfully deleted!": "Lead successfully deleted!", "Lead successfully moved.": "Lead successfully moved.", "Lead successfully updated!": "Lead successfully updated!", "Lead to Deal Conversion": "Lead to Deal Conversion", "Lead User Name": "Lead User Name", "Leads": "Leads", "Leave": "Leave", "Leave Action": "Leave Action", "Leave Date": "Leave Date", "Leave Days": "Leave Days", "Leave End Date": "Leave End Date", "Leave Management Setup": "Leave Management Setup", "Leave Name": "Leave Name", "Leave Reason": "Leave Reason", "Leave Remark": "Leave Remark", "Leave Report": "Leave Report", "Leave Report of": "Leave Report of", "Leave Start Date": "Leave Start Date", "Leave Status": "Leave Status", "Leave status successfully updated.": "Leave status successfully updated.", "Leave successfully created.": "Leave successfully created.", "Leave successfully deleted.": "Leave successfully deleted.", "Leave successfully updated.": "Leave successfully updated.", "Leave Summary": "Leave Summary", "Leave Type": "Leave Type", "Leave Type ": "Leave Type ", "LeaveType  successfully created.": "LeaveType  successfully created.", "LeaveType successfully deleted.": "LeaveType successfully deleted.", "LeaveType successfully updated.": "LeaveType successfully updated.", "Ledger Summary": "Ledger Summary", "Let me choose": "Let me choose", "li": "li", "Liabilities & Equity": "Liabilities & Equity", "Lifetime": "Lifetime", "lifetime": "lifetime", "Limit": "Limit", "Link Copy on Clipboard": "<PERSON>py on Clipboard", "List": "List", "List View": "List View", "Live": "Live", "Live Demo": "Live Demo", "Live Demo Link": "Live Demo Link", "Loading...": "Loading...", "Loan": "Loan", "Loan  successfully created.": "<PERSON><PERSON>  successfully created.", "Loan Amount": "<PERSON><PERSON>", "Loan Option": "Loan <PERSON>tion", "Loan Options": "Loan Options", "Loan Options*": "Loan Options*", "Loan successfully deleted.": "<PERSON><PERSON> successfully deleted.", "Loan successfully updated.": "<PERSON>an successfully updated.", "LoanOption  successfully created.": "LoanOption  successfully created.", "LoanOption successfully deleted.": "LoanOption successfully deleted.", "LoanOption successfully updated.": "LoanOption successfully updated.", "Local": "Local", "Local Calender": "Local Calender", "Logged Hours": "Logged Hours", "Login": "<PERSON><PERSON>", "Login As Company": "Login As Company", "Login Disable": "Login Disable", "Login Enable": "Login Enable", "Login is enable": "Login is enable", "Logo": "Logo", "Logo dark": "Logo dark", "Logo Light": "Logo Light", "Logout": "Logout", "Long Description": "Long Description", "Longitude": "Longitude", "Low": "Low", "Mail Driver": "Mail Driver", "Mail Encryption": "Mail Encryption", "Mail From Address": "Mail From Address", "Mail From Name": "Mail From Name", "Mail Host": "Mail Host", "Mail not send, email is empty": "Mail not send, email is empty", "Mail not send, email not found": "Mail not send, email not found", "Mail Password": "Mail Password", "Mail Port": "Mail Port", "Mail Send": "Mail Send", "Mail To": "Mail To", "Mail Username": "Mail Username", "Make Payment": "Make Payment", "Make this a sub-account": "Make this a sub-account", "Male": "Male", "Manage Allowance Option": "Manage Allowance Option", "Manage Announcement": "Manage Announcement", "Manage Appraisal": "Manage Appraisal", "Manage Archive Application": "Manage Archive Application", "Manage Attendance": "Manage Attendance", "Manage Attendance List": "Manage Attendance List", "Manage Award": "Manage Award", "Manage Award Type": "Manage Award Type", "Manage Bank Account": "Manage Bank Account", "Manage Bills": "Manage Bills", "Manage Branch": "Manage Branch", "Manage Budget Planner": "Manage Budget Planner", "Manage Bug Report": "Manage Bug Report", "Manage Bulk Attendance": "Manage Bulk Attendance", "Manage Chart of Account Type": "Manage Chart of Account Type", "Manage Chart of Accounts": "Manage Chart of Accounts", "Manage Client": "Manage Client", "Manage Companies": "Manage Companies", "Manage Company Policy": "Manage Company Policy", "Manage Competencies": "Manage Competencies", "Manage Complain": "Manage Complain", "Manage Contract": "Manage Contract", "Manage Contract Type": "Manage Contract Type", "Manage Coupon": "Manage Coupon", "Manage Coupon Details": "Manage Coupon Details", "Manage Credit Notes": "Manage Credit Notes", "Manage Custom Field": "Manage Custom Field", "Manage Custom Question for interview": "Manage Custom Question for interview", "Manage Customer-Detail": "Manage Customer-Detail", "Manage Customers": "Manage Customers", "Manage Deal": "Manage Deal", "Manage Deal Stages": "Manage Deal Stages", "Manage Deals": "Manage Deals", "Manage Debit Notes": "Manage Debit Notes", "Manage Deduction Option": "Manage Deduction Option", "Manage Department": "Manage Department", "Manage Designation": "Manage Designation", "Manage Document": "Manage Document", "Manage Document Type": "Manage Document Type", "Manage Employee": "Manage Employee", "Manage Employee  Salary List": "Manage Employee  Salary List", "Manage Employee Salary": "Manage Employee Salary", "Manage Estimate": "Manage Estimate", "Manage Expenses": "Manage Expenses", "Manage Form Builder": "Manage Form Builder", "Manage Goal Tracking": "Manage Goal Tracking", "Manage Goal Type": "Manage Goal Type", "Manage Goals": "Manage Goals", "Manage Holiday": "Manage Holiday", "Manage Indicator": "Manage Indicator", "Manage Interview Schedule": "Manage Interview Schedule", "Manage Invoices": "Manage Invoices", "Manage Job": "Manage Job", "Manage Job Application": "Manage Job Application", "Manage Job Category": "Manage Job Category", "Manage Job On-boarding": "Manage Job On-boarding", "Manage Job Stage": "Manage Job Stage", "Manage Journal Entry": "Manage Journal Entry", "Manage Labels": "Manage Labels", "Manage Language": "Manage Language", "Manage Lead": "Manage Lead", "Manage Lead Stages": "Manage Lead Stages", "Manage Leads": "Manage Leads", "Manage Leave": "Manage Leave", "Manage Leave Report": "Manage Leave Report", "Manage Leave Type": "Manage Leave Type", "Manage Loan Option": "Manage Loan Option", "Manage Meeting": "Manage Meeting", "Manage Monthly Attendance": "Manage Monthly Attendance", "Manage Order Summary": "Manage Order Summary", "Manage Payments": "Manage Payments", "Manage Payroll": "Manage Payroll", "Manage Payslip Type": "Manage Payslip Type", "manage performance type": "manage performance type", "Manage Permissions": "Manage Permissions", "Manage Pipelines": "Manage Pipelines", "Manage Plan": "Manage Plan", "Manage Pos": "Manage Pos", "Manage Product & Service Unit": "Manage Product & Service Unit", "Manage Product & Services": "Manage Product & Services", "Manage Product Stock": "Manage Product Stock", "Manage Product-Service & Income-Expense Category": "Manage Product-Service & Income-Expense Category", "Manage Project Bug Status": "Manage Project Bug Status", "Manage Project Stages": "Manage Project Stages", "Manage Project Task Stages": "Manage Project Task Stages", "Manage Projects": "Manage Projects", "Manage Promotion": "Manage Promotion", "Manage Proposals": "Manage Proposals", "Manage Purchase": "Manage Purchase", "Manage Quotation": "Manage Quotation", "Manage Resignation": "Manage Resignation", "Manage Revenues": "Manage Revenues", "Manage Role": "Manage Role", "Manage Sources": "Manage Sources", "Manage Tax Rate": "Manage Tax Rate", "Manage Termination": "Manage Termination", "Manage Termination Type": "Manage Termination Type", "Manage Tracker": "Manage Tracker", "Manage Trainer": "Manage Trainer", "Manage Training": "Manage Training", "Manage Training Type": "Manage Training Type", "Manage Transfer": "Manage Transfer", "Manage Trip": "Manage Trip", "Manage User": "Manage User", "Manage User Log": "Manage User Log", "Manage Vendor-Detail": "Manage Vendor-Detail", "Manage Vendors": "Manage Vendors", "Manage Warning": "Manage Warning", "Manage Zoom Meeting": "Manage Zoom Meeting", "Manage Zoom-Meeting": "Manage Zoom-Meeting", "Manual": "Manual", "Manually": "Manually", "Manually plan upgraded by Super Admin": "Manually plan upgraded by Super Admin", "Manually Upgrade By Super Admin": "Manually Upgrade By Super Admin", "March": "March", "Mark as favorite": "<PERSON> as favorite", "Mark Attandance": "<PERSON>", "Mark Attendance": "Mark Attendance", "Mark Sent": "<PERSON>", "Max upload size": "Max upload size", "Max upload size ( In KB)": "Max upload size ( In KB)", "Maximum Clients": "Maximum Clients", "Maximum Customers": "Maximum Customers", "Maximum Result Length": "Maximum Result Length", "Maximum Storage Limit": "Maximum Storage Limit", "Maximum Users": "Maximum Users", "Maximum Venders": "Maximum Venders", "Maximum Vendors": "Maximum Vendors", "May": "May", "MB": "MB", "Medium": "Medium", "Meduium": "Meduium", "Meeting": "Meeting", "Meeting  successfully created.": "Meeting  successfully created.", "Meeting Date": "Meeting Date", "Meeting Date : ": "Meeting Date : ", "Meeting Id": "Meeting Id", "Meeting List": "Meeting List", "Meeting Note": "Meeting Note", "Meeting schedule": "Meeting schedule", "Meeting successfully created, Webhook call failed.": "Meeting successfully created, Webhook call failed.", "Meeting successfully deleted.": "Meeting successfully deleted.", "Meeting successfully updated.": "Meeting successfully updated.", "Meeting Time": "Meeting Time", "Meeting title": "Meeting title", "Meeting Title": "Meeting Title", "Member": "Member", "Members": "Members", "MEMBERS": "MEMBERS", "Menu Bar": "Menu Bar", "Mercado": "<PERSON><PERSON><PERSON>", "Mercado Mode": "Mercado Mode", "Mercado Pago": "Mercado <PERSON>", "Merchant ID": "Merchant ID", "Merchant Id": "Merchant Id", "Merchant Key": "Merchant Key", "Merchant Login Id": "Merchant Login Id", "Merchant Salt": "Merchant Salt", "Merchant Secret": "Merchant Secret", "Merchant Transaction Key": "Merchant Transaction Key", "Message": "Message", "Message successfully added!": "Message successfully added!", "Messages": "Messages", "Messenger": "<PERSON>", "Meta Description": "Meta Description", "Meta Image": "Meta Image", "Meta Keywords": "Meta Keywords", "Method": "Method", "Mettings": "<PERSON><PERSON><PERSON>", "Midtrans": "Midtrans", "Midtrans Mode": "Midtrans Mode", "Milestone": "Milestone", "Milestone Progress": "Milestone Progress", "Milestone successfully created.": "Milestone successfully created.", "Milestone successfully deleted.": "Milestone successfully deleted.", "Milestone updated successfully.": "Milestone updated successfully.", "Milestones": "Milestones", "Minimum Threshold Amount": "Minimum Threshold Amount", "Minutes": "Minutes", "Mobile No": "Mobile No", "Mobile Number": "Mobile Number", "Module": "<PERSON><PERSON><PERSON>", "Module Settings": "<PERSON><PERSON><PERSON>", "Mollie": "<PERSON><PERSON>", "Mollie Api Key": "<PERSON><PERSON>", "Mollie Partner Id": "Mollie Partner Id", "Mollie Profile Id": "Mollie Profile Id", "Mon": "Mon", "Month": "Month", "Monthly": "Monthly", "Monthly Attendance": "Monthly Attendance", "Monthly Cashflow": "Monthly Cashflow", "Monthly Pos": "Monthly Pos", "Monthly Pos Report": "Monthly Pos Report", "Monthly Purchase": "Monthly Purchase", "Monthly Purchase Report": "Monthly Purchase Report", "Monthly Report": "Monthly Report", "Months": "Months", "More Information": "More Information", "Most Purchase Plan : ": "Most Purchase Plan : ", "Moved the deal": "Moved the deal", "Moved the lead": "Moved the lead", "Moved the Task": "Moved the Task", "Name": "Name", "Name on card": "Name on card", "Need to ask ?": "Need to ask ?", "Need to show option ?": "Need to show option ?", "Nepalste": "Nepalste", "Neplaste": "Neplaste", "Net Amount": "Net Amount", "Net Profit": "Net Profit", "NET PROFIT :": "NET PROFIT :", "Net Profit = Total Income - Total Expense": "Net Profit = Total Income - Total Expense", "Net Profit = Total Income - Total Expense ": "Net Profit = Total Income - Total Expense ", "Net Profit/Loss": "Net Profit/Loss", "Net Salary": "Net Salary", "New Announcement": "New Announcement", "New Award": "New Award", "New Bill": "New Bill", "New Budget": "New Budget", "New Client": "New Client", "New Company Policy": "New Company Policy", "New Confirm Password": "New Confirm Password", "New Contract": "New Contract", "New Customer": "New Customer", "New Deal": "New Deal", "New Event": "New Event", "New Holiday": "New Holiday", "New Invoice": "New Invoice", "New Invoice Payment": "New Invoice Payment", "New Lead": "New Lead", "New Meeting": "New Meeting", "New Monthly Payslip": "New Monthly Payslip", "New Password": "New Password", "New Payment": "New Payment", "New Project": "New Project", "New Proposal": "New Proposal", "New Revenue": "New Revenue", "New Stage Name": "New Stage Name", "New Support Ticket": "New Support Ticket", "New Task": "New Task", "New Task Comment": "New Task Comment", "New User": "New User", "New Vendor": "New Vendor", "Newest": "Newest", "No": "No", "No accouncement present yet.": "No accouncement present yet.", "No activities found": "No activities found", "No Attachments Found.": "No Attachments Found.", "No Bug found": "No Bug found", "No data available in table": "No data available in table", "No Data Found": "No Data Found", "No Data Found.!": "No Data Found.!", "No Due Projects Found.": "No Due Projects Found.", "No entries found": "No entries found", "No Expense Found.": "No Expense Found.", "No Interview Scheduled!": "No Interview Scheduled!", "No meeting scheduled yet.": "No meeting scheduled yet.", "No milestone found": "No milestone found", "No Milestone Found.": "No Milestone Found.", "No Product Available": "No Product Available", "No Projects Found.": "No Projects Found.", "No tasks found": "No tasks found", "No Todo List Found..!": "No Todo List Found..!", "No User Exist": "No User Exist", "No User Found": "No User Found", "No User Found.": "No User Found.", "NOC": "NOC", "NOC Settings": "NOC Settings", "NOC successfully saved!": "NOC successfully saved!", "Not currect amount": "Not currect amount", "Not exists in notification template.": "Not exists in notification template.", "Not Required": "Not Required", "Not Sent": "Not Sent", "Note": "Note", "Note : super admin has disabled the referral program.": "Note : super admin has disabled the referral program.", "Note successfully deleted!": "Note successfully deleted!", "Note successfully saved!": "Note successfully saved!", "Note successfully saved.": "Note successfully saved.", "Note that you can use the biometric attendance system only if you are using the ZKTeco machine for biometric attendance.": "Note that you can use the biometric attendance system only if you are using the ZKTeco machine for biometric attendance.", "Note: \"-1\" for Unlimited": "Note: \"-1\" for Unlimited", "Note: Add currency code as per three-letter ISO code": "Note: Add currency code as per three-letter ISO code", "Note: Add currency code as per three-letter ISO code.": "Note: Add currency code as per three-letter ISO code.", "Note: Discount in Percentage": "Note: Discount in Percentage", "Notes": "Notes", "Notice Date": "Notice Date", "Notification Message": "Notification Message", "Notification Template": "Notification Template", "Notification Template successfully updated.": "Notification Template successfully updated.", "November": "November", "Now": "Now", "Number": "Number", "Number Of Days": "Number Of Days", "Number of days": "Number of days", "Number of Hours": "Number of Hours", "Number of Result": "Number of Result", "Number:": "Number:", "Occasion": "Occasion", "Oct-Dec": "Oct-Dec", "October": "October", "of": "of", "Off": "Off", "Offer Expiration Date": "Offer Expiration Date", "Offer Letter": "Offer Letter", "Offer Letter Settings": "Offer Letter Settings", "Offer Letter successfully saved!": "Offer Letter successfully saved!", "Offer Text": "Offer Text", "OfferLetter DOC": "OfferLetter DOC", "OfferLetter PDF": "OfferLetter PDF", "Old Password": "Old Password", "Old Stage Name": "Old Stage Name", "Oldest": "Oldest", "On": "On", "On / Off": "On / Off", "On Hold": "On Hold", "On/Off": "On/Off", "Only Upload Files": "Only Upload Files", "Open": "Open", "Open media in new tab": "Open media in new tab", "Open Task": "Open Task", "Opening Balance": "Opening Balance", "opps something wren wrong.": "opps something wren wrong.", "Opps...": "Opps...", "OR": "OR", "Order": "Order", "Order Id": "Order Id", "Order Number": "Order Number", "Order Summary": "Order Summary", "OrderId": "OrderId", "Orders": "Orders", "Org": "Org", "OS": "OS", "Os Name": "Os Name", "Other Payment": "Other Payment", "OtherPayment  successfully created.": "OtherPayment  successfully created.", "OtherPayment successfully deleted.": "OtherPayment successfully deleted.", "OtherPayment successfully updated.": "OtherPayment successfully updated.", "Out": "Out", "Outbound": "Outbound", "Over Due": "Over Due", "Overall Rating": "Overall Rating", "Overdue": "Overdue", "Overtime": "Overtime", "OverTime": "OverTime", "Overtime  successfully created.": "Overtime  successfully created.", "Overtime successfully deleted.": "Overtime successfully deleted.", "Overtime successfully updated.": "Overtime successfully updated.", "Overtime Title": "Overtime Title", "Overtime Title*": "Overtime Title*", "Overview": "Overview", "Owner Name": "Owner Name", "Ozow": "Ozow", "Ozow Api Key": "Ozow Api Key", "Ozow Mode": "Ozow Mode", "Ozow Private Key": "Ozow Private Key", "Ozow Site Key": "Ozow Site Key", "Page Content": "Page Content", "Page Name": "Page Name", "Page Not Found": "Page Not Found", "Page URL": "Page URL", "Paid": "Paid", "Paid Amount": "<PERSON><PERSON>", "Paid By": "<PERSON><PERSON>", "Paid Users : ": "Paid Users : ", "Paiement Pro": "Paiement Pro", "Parent Account": "Parent Account", "Parent Account Name": "Parent Account Name", "Partial Paid": "Partial Paid", "Password": "Password", "Password ( Optional )": "Password ( Optional )", "Password Confirmation": "Password Confirmation", "Password Protected": "Password Protected", "Password required": "Password required", "Password successfully updated.": "Password successfully updated.", "pay": "pay", "PAY": "PAY", "Pay Now": "Pay Now", "Payable Details": "Payable Details", "Payable Reports": "Payable Reports", "Payable Summary": "Payable Summary", "Payables": "Payables", "Payee": "Payee", "PayFast": "PayFast", "Payfast": "Payfast", "Payfast Mode": "Payfast Mode", "PayHere": "PayHere", "PayHere Environment": "PayHere Environment", "Paylip": "<PERSON><PERSON>", "Payment": "Payment", "Payment :": "Payment :", "Payment : ": "Payment : ", "Payment Amount": "Payment Amount", "Payment Bill": "Payment Bill", "Payment completed successfully!": "Payment completed successfully!", "Payment Date": "Payment Date", "Payment Due Amount": "Payment Due Amount", "Payment failed": "Payment failed", "Payment is already completed!": "Payment is already completed!", "Payment Method": "Payment Method", "Payment Name": "Payment Name", "Payment Price": "Payment Price", "Payment Receipt": "Payment Receipt", "Payment Reminder Date": "Payment Reminder Date", "Payment Reminder Name": "Payment Reminder Name", "Payment reminder successfully send.": "Payment reminder successfully send.", "Payment setting successfully updated.": "Payment setting successfully updated.", "Payment Settings": "Payment Settings", "Payment Status": "Payment Status", "Payment successfully added": "Payment successfully added", "Payment successfully added!": "Payment successfully added!", "Payment successfully added.": "Payment successfully added.", "Payment successfully added. ": "Payment successfully added. ", "Payment successfully created": "Payment successfully created", "Payment successfully deleted.": "Payment successfully deleted.", "Payment successfully, Webhook call failed.": "Payment successfully, Webhook call failed.", "Payment Summary": "Payment Summary", "Payment Type": "Payment Type", "Payment Updated Successfully": "Payment Updated Successfully", "PaymentWall": "PaymentWall", "Paymentwall": "Paymentwall", "Payout": "Payout", "PayOut": "PayOut", "Payout History": "Payout History", "Payout Request": "Payout Request", "PAYPAL": "PAYPAL", "Paypal": "<PERSON><PERSON>", "Payroll": "Payroll", "Payroll Month": "Payroll Month", "Payroll Report": "Payroll Report", "Payroll Report of": "Payroll Report of", "Payroll Setup": "Payroll Setup", "Payroll Summary": "Payroll Summary", "Payroll Type": "Payroll Type", "Payslip": "Payslip", "payslip": "payslip", "Payslip Already created.": "Payslip Already created.", "Payslip Bulk Payment successfully.": "Payslip Bulk Payment successfully.", "Payslip Name": "Payslip Name", "Payslip Payment failed.": "Payslip Payment failed.", "Payslip Payment successfully.": "Payslip Payment successfully.", "Payslip Salary Month ": "Payslip Salary Month ", "Payslip successfully created, Webhook call failed.": "Payslip successfully created, Webhook call failed.", "Payslip successfully created.": "Payslip successfully created.", "Payslip successfully sent.": "<PERSON><PERSON><PERSON> successfully sent.", "Payslip Type": "Payslip Type", "Payslip Type*": "Payslip Type*", "Payslip Url": "Payslip Url", "PayslipType  successfully created.": "PayslipType  successfully created.", "PayslipType successfully deleted.": "PayslipType successfully deleted.", "PayslipType successfully updated.": "PayslipType successfully updated.", "Paystack": "Paystack", "Paytab": "Paytab", "PayTab": "PayTab", "paytm": "paytm", "Paytm": "Paytm", "Paytm Environment": "Paytm Environment", "PayTR": "PayTR", "PDF": "PDF", "Pending": "Pending", "Pending Leave Detail": "Pending Leave Detail", "Pending Leaves": "Pending Leaves", "Per Month": "Per Month", "Per Year": "Per Year", "Performance": "Performance", "Performance Setup": "Performance Setup", "Performance Type": "Performance Type", "Performance Type successfully created.": "Performance Type successfully created.", "Performance Type successfully deleted.": "Performance Type successfully deleted.", "Performance Type successfully updated.": "Performance Type successfully updated.", "Permission Denied": "Permission Denied", "Permission Denied . ": "Permission Denied . ", "Permission denied!": "Permission denied!", "Permission denied.": "Permission denied.", "Permission Denied.": "Permission Denied.", "Permissions": "Permissions", "Permissions successfully updated!": "Permissions successfully updated!", "Personal Detail": "Personal Detail", "Personal Info": "Personal Info", "Phone": "Phone", "Pipeline": "Pipeline", "Pipeline Name": "Pipeline Name", "Pipeline Report": "Pipeline Report", "Pipeline successfully created!": "Pipeline successfully created!", "Pipeline successfully deleted!": "Pipeline successfully deleted!", "Pipeline successfully updated!": "Pipeline successfully updated!", "Pipelines": "Pipelines", "Pipelines Report": "Pipelines Report", "Placeholders": "Placeholders", "Plan": "Plan", "plan": "plan", "PLAN": "PLAN", "Plan activated Successfully!": "Plan activated Successfully!", "Plan Activated Successfully!": "Plan Activated Successfully!", "Plan activated Successfully.": "Plan activated Successfully.", "Plan deleted successfully": "Plan deleted successfully", "Plan Expired : ": "Plan Expired : ", "Plan fail to upgrade.": "Plan fail to upgrade.", "Plan is deleted.": "Plan is deleted.", "Plan Name": "Plan Name", "Plan Not Found!": "Plan Not Found!", "Plan not found!": "Plan not found!", "Plan Not Found.": "Plan Not Found.", "Plan not found.": "Plan not found.", "Plan payment request send successfully": "Plan payment request send successfully", "Plan payment status updated successfully.": "Plan payment status updated successfully.", "Plan Price": "Plan Price", "Plan Request": "Plan Request", "Plan Section": "Plan Section", "Plan storage limit is over so please upgrade the plan.": "Plan storage limit is over so please upgrade the plan.", "Plan Successfully Activated": "Plan Successfully Activated", "Plan successfully activated.": "Plan successfully activated.", "Plan Successfully created.": "Plan Successfully created.", "Plan successfully disable.": "Plan successfully disable.", "Plan successfully enable.": "Plan successfully enable.", "Plan successfully updated.": "Plan successfully updated.", "Plan successfully upgraded.": "Plan successfully upgraded.", "Plan Trial Expired : ": "Plan Trial Expired : ", "Plan-Request": "Plan-Request", "Please add phone number to your profile.": "Please add phone number to your profile.", "Please add pusher settings for using messenger.": "Please add pusher settings for using messenger.", "Please correct the errors and try again.": "Please correct the errors and try again.", "Please create new employee": "Please create new employee", "Please create new Tax": "Please create new Tax", "Please create pipeline.": "Please create pipeline.", "Please Create Stage for This Pipeline.": "Please Create Stage for This Pipeline.", "Please create stage for this pipeline.": "Please create stage for this pipeline.", "Please delete related record of this account.": "Please delete related record of this account.", "Please enter a 16-digit card number.": "Please enter a 16-digit card number.", "Please enter correct current password.": "Please enter correct current password.", "Please enter valid amount": "Please enter valid amount", "Please first create auth token": "Please first create auth token", "Please first create employee or edit employee code.": "Please first create employee or edit employee code.", "Please first generate auth token": "Please first generate auth token", "Please first generate auth token.": "Please first generate auth token.", "Please remove this field from Convert Lead.": "Please remove this field from Convert Lead.", "Please select a chat to start messaging": "Please select a chat to start messaging", "Please select file!": "Please select file!", "Please Select Valid Clients!": "Please Select Valid Clients!", "Please Select Valid Product!": "Please Select Valid Product!", "Please Select Valid User!": "Please Select Valid User!", "Please set proper configuration for Api Key": "Please set proper configuration for Api Key", "Please set proper configuration for storage.": "Please set proper configuration for storage.", "Please set stripe api key & secret key for add new plan.": "Please set stripe api key & secret key for add new plan.", "Please upload a valid image file. Size of image should not be more than 2MB.": "Please upload a valid image file. Size of image should not be more than 2MB.", "Please use with country code. (ex. +91)": "Please use with country code. (ex. +91)", "Please write checklist name!": "Please write checklist name!", "Please write comment!": "Please write comment!", "Please write todo title!": "Please write todo title!", "POS": "POS", "Pos": "Pos", "POS Amount": "POS Amount", "POS Barcode Print": "POS Barcode Print", "POS Daily/Monthly Report": "POS Daily/Monthly Report", "POS Detail": "POS Detail", "POS ID": "POS ID", "POS Invoice": "POS Invoice", "POS Logo": "POS Logo", "Pos Not Found.": "Pos Not Found.", "POS Of This Month": "POS Of This Month", "Pos Prefix": "Pos Prefix", "POS Print Setting": "POS Print Setting", "POS Product Barcode": "POS Product Barcode", "POS Setting updated successfully": "POS Setting updated successfully", "POS Summary": "POS Summary", "POS System": "POS System", "POS Template": "POS Template", "POS Vs Purchase": "POS Vs Purchase", "Pos VS Purchase Report": "Pos VS Purchase Report", "Position": "Position", "position available": "position available", "Positions": "Positions", "Post": "Post", "Pre": "Pre", "Preview": "Preview", "PreView": "PreView", "Preview :": "Preview :", "Price": "Price", "Price:": "Price:", "Pricing Plan": "Pricing Plan", "Primary color settings": "Primary color settings", "Print": "Print", "Print Barcode": "Print Barcode", "Print Estimation": "Print Estimation", "Print Settings": "Print Settings", "Print-Settings": "Print-Settings", "Priority": "Priority", "Priority: ": "Priority: ", "Private Key": "Private Key", "Product": "Product", "Product & Services": "Product & Services", "Product Image": "Product Image", "Product Name": "Product Name", "Product out of stock!.": "Product out of stock!.", "Product quantity updated manually.": "Product quantity updated manually.", "Product removed from cart!": "Product removed from cart!", "Product Stock": "Product Stock", "Product successfully created.": "Product successfully created.", "Product successfully deleted.": "Product successfully deleted.", "Product successfully updated.": "Product successfully updated.", "Product Summary": "Product Summary", "Production": "Production", "Products": "Products", "Products successfully deleted!": "Products successfully deleted!", "Products successfully updated!": "Products successfully updated!", "Products System": "Products System", "Profile": "Profile", "Profile Account": "Profile Account", "Profile Id": "Profile Id", "Profile Image": "Profile Image", "Profit": "Profit", "Profit & Loss": "Profit & Loss", "Profit & Loss Summary": "Profit & Loss Summary", "Profit = Income - Expense ": "Profit = Income - Expense ", "Profit = POS - Purchase": "Profit = POS - Purchase", "Progress": "Progress", "Projeat not found": "Projeat not found", "Project": "Project", "Project ": "Project ", "Project Add Successfully": "Project Add Successfully", "Project add successfully, Webhook call failed.": "Project add successfully, Webhook call failed.", "Project Bug Status": "Project Bug Status", "Project Dashboard": "Project Dashboard", "Project End Date": "Project End Date", "Project File": "Project File", "Project Image": "Project Image", "Project Name": "Project Name", "Project Not Found.": "Project Not Found.", "Project Report": "Project Report", "Project Report Download": "Project Report Download", "Project Reports": "Project Reports", "Project Stage": "Project Stage", "Project Stage Name": "Project Stage Name", "Project stage successfully created.": "Project stage successfully created.", "Project stage successfully deleted.": "Project stage successfully deleted.", "Project stage successfully updated.": "Project stage successfully updated.", "Project Start Date": "Project Start Date", "Project Status": "Project Status", "Project Successfully Deleted.": "Project Successfully Deleted.", "Project System": "Project System", "Project System Setup": "Project System Setup", "Project task already assign this stage , so please remove or move task to other project stage.": "Project task already assign this stage , so please remove or move task to other project stage.", "Project Task Stage": "Project Task Stage", "Project Task Stage Added Successfully": "Project Task Stage Added Successfully", "Project Task Stage Name": "Project Task Stage Name", "Project Task Stages": "Project Task Stages", "Project Updated Successfully": "Project Updated Successfully", "Project User": "Project User", "Projects": "Projects", "projects": "projects", "Projects Details": "Projects Details", "Projects Members": "Projects Members", "Projects: ": "Projects: ", "Promotion": "Promotion", "Promotion  successfully created.": "Promotion  successfully created.", "Promotion Date": "Promotion Date", "Promotion successfully deleted.": "Promotion successfully deleted.", "Promotion successfully updated.": "Promotion successfully updated.", "Promotion Title": "Promotion Title", "Proposal": "Proposal", "PROPOSAL": "PROPOSAL", "Proposal convert to invoice": "Proposal convert to invoice", "Proposal Create": "Proposal Create", "Proposal Detail": "Proposal Detail", "Proposal Details": "Proposal Details", "Proposal duplicate successfully.": "Proposal duplicate successfully.", "Proposal Edit": "Proposal Edit", "Proposal Email": "Proposal Email", "Proposal Logo": "Proposal Logo", "Proposal Name": "Proposal Name", "Proposal Not Found.": "Proposal Not Found.", "Proposal Number": "Proposal Number", "Proposal Prefix": "Proposal Prefix", "Proposal Print Setting": "Proposal Print Setting", "Proposal product successfully deleted.": "Proposal product successfully deleted.", "Proposal Setting updated successfully": "Proposal Setting updated successfully", "Proposal Status": "Proposal Status", "Proposal status changed successfully.": "Proposal status changed successfully.", "Proposal successfully created.": "Proposal successfully created.", "Proposal successfully deleted.": "Proposal successfully deleted.", "Proposal successfully sent.": "Proposal successfully sent.", "Proposal successfully updated.": "Proposal successfully updated.", "Proposal Template": "Proposal Template", "Proposal to invoice convert successfully.": "Proposal to invoice convert successfully.", "Proposal Url": "Proposal Url", "Proposal/Invoice/Bill/Purchase/POS Footer Note": "Proposal/Invoice/Bill/Purchase/POS Footer Note", "Proposal/Invoice/Bill/Purchase/POS Footer Title": "Proposal/Invoice/Bill/Purchase/POS Footer Title", "Proposed Start Date": "Proposed Start Date", "Public Key": "Public Key", "Purchase": "Purchase", "PURCHASE": "PURCHASE", "Purchase Create": "Purchase Create", "Purchase Daily/Monthly Report": "Purchase Daily/Monthly Report", "Purchase Date": "Purchase Date", "Purchase Detail": "Purchase Detail", "Purchase Edit": "Purchase Edit", "Purchase Logo": "Purchase Logo", "Purchase Not Found.": "Purchase Not Found.", "Purchase Number": "Purchase Number", "Purchase Of This Month": "Purchase Of This Month", "Purchase Prefix": "Purchase Prefix", "Purchase Price": "Purchase Price", "Purchase Print Setting": "Purchase Print Setting", "Purchase product successfully deleted.": "Purchase product successfully deleted.", "Purchase Setting updated successfully": "Purchase Setting updated successfully", "Purchase successfully created.": "Purchase successfully created.", "Purchase successfully deleted.": "Pur<PERSON> successfully deleted.", "Purchase successfully sent.": "<PERSON><PERSON><PERSON> successfully sent.", "Purchase successfully updated.": "Purchase successfully updated.", "Purchase Template": "Purchase Template", "Purchase Vs POS Report": "Purchase Vs POS Report", "Purchases": "Purchases", "Purpose of Trip": "Purpose of Trip", "Pusher App Cluster": "<PERSON><PERSON><PERSON> App Cluster", "Pusher App Id": "<PERSON><PERSON><PERSON> App Id", "Pusher App Key": "<PERSON><PERSON><PERSON>pp Key", "Pusher App Secret": "<PERSON><PERSON><PERSON> App Secret", "Pusher Settings": "<PERSON><PERSON><PERSON>", "Pusher Settings updated successfully": "<PERSON><PERSON><PERSON> updated successfully", "QR Display?": "QR Display?", "Qty": "Qty", "QTY": "QTY", "Quantity": "Quantity", "quantity added by manually": "quantity added by manually", "Quantity of Bills": "Quantity of Bills", "Quantity of Invoice": "Quantity of Invoice", "Quantity Ordered": "Quantity Ordered", "quantity purchase in bill": "quantity purchase in bill", "Quantity Sold": "Quantity Sold", "Quantity:": "Quantity:", "Quarter Day": "Quarter Day", "Quarterly": "Quarterly", "Quarterly Cashflow": "Quarterly Cashflow", "Query": "Query", "Question": "Question", "Question Name": "Question Name", "Question successfully created.": "Question successfully created.", "Question successfully deleted.": "Question successfully deleted.", "Question successfully updated.": "Question successfully updated.", "Questions": "Questions", "Quotataion": "Quotataion", "Quotataion Create": "Quotataion Create", "Quotataion ID": "Quotataion ID", "Quotation": "Quotation", "quotation": "quotation", "Quotation Create": "Quotation Create", "Quotation Date": "Quotation Date", "Quotation Detail": "Quotation Detail", "Quotation Edit": "Quotation Edit", "quotation Edit": "quotation Edit", "Quotation Logo": "Quotation Logo", "Quotation Not Found.": "Quotation Not Found.", "Quotation Number": "Quotation Number", "Quotation Prefix": "Quotation Prefix", "Quotation Print Setting": "Quotation Print Setting", "Quotation product successfully deleted.": "Quotation product successfully deleted.", "Quotation Setting updated successfully": "Quotation Setting updated successfully", "Quotation successfully created.": "Quotation successfully created.", "Quotation successfully deleted.": "Quotation successfully deleted.", "Quotation successfully updated.": "Quotation successfully updated.", "Quotation Summary": "Quotation Summary", "Quotation Template": "Quotation Template", "Rate": "Rate", "Rate %": "Rate %", "Rating": "Rating", "Razorpay": "Razorpay", "Re Generate": "Re Generate", "Re-type New Password": "Re-type New Password", "Read more": "Read more", "Reason": "Reason", "ReCaptcha Settings": "ReCaptcha Settings", "Recaptcha Settings updated successfully": "Recaptcha Settings updated successfully", "Receipt": "Receipt", "Receipt Reminder": "Receipt Reminder", "Receipt Summary": "Receipt Summary", "Receivable Details": "Receivable Details", "Receivable Reports": "Receivable Reports", "Receivable Summary": "Receivable Summary", "Receivables": "Receivables", "Recent": "Recent", "Recent Bills": "Recent Bills", "Recent Invoices": "Recent Invoices", "Recent Order": "Recent Order", "Records not found": "Records not found", "Recruitment Setup": "Recruitment Setup", "Ref Number": "Ref Number", "Refer ": "<PERSON><PERSON> ", "Reference": "Reference", "Referral Company Name": "Referral Company Name", "Referral Program": "Referral Program", "Referral Program Setting successfully Updated.": "Referral Program Setting successfully Updated.", "Referral Transaction": "Referral Transaction", "Referrer Host": "Referrer Host", "Referrer Path": "Referrer Path", "Refund": "Refund", "Regards,": "<PERSON><PERSON>,", "Region": "Region", "Region Name": "Region Name", "Register": "Register", "Registration Number": "Registration Number", "Reject": "Reject", "Rejected Leave": "Rejected Leave", "Rejected Leave Detail": "Rejected Leave Detail", "Rejected Leaves": "Rejected Leaves", "Remain Task": "Re<PERSON>in Task", "Remark": "Remark", "Remarks": "Remarks", "Remove all items from cart?": "Remove all items from cart?", "Replies": "Replies", "Reply": "Reply", "Reply Ticket": "<PERSON><PERSON>", "Report": "Report", "Reports": "Reports", "Request Aceepted Successfully.": "Request Aceepted Successfully.", "Request Amount": "Request Amount", "Request Cancel Successfully.": "Request Cancel Successfully.", "Request Canceled Successfully.": "Request Canceled Successfully.", "Request Rejected Successfully.": "Request Rejected Successfully.", "Request Send Successfully.": "Request Send Successfully.", "Requested Amount": "Requested Amount", "Requested Date": "Requested Date", "Requesting manual payment for the planned amount for the subscriptions plan.": "Requesting manual payment for the planned amount for the subscriptions plan.", "Required": "Required", "Required Field": "Required Field", "Requirements": "Requirements", "Resend Bill": "Resend Bill", "Resend Invoice": "Resend Invoice", "Resend Proposal": "Resend Proposal", "Resend Purchase": "Resend Purchase", "Resend Verification Email": "Resend Verification Email", "Reset": "Reset", "Reset Password": "Reset Password", "Resignation": "Resignation", "Resignation  successfully created.": "Resignation  successfully created.", "Resignation Date": "Resignation Date", "Resignation successfully deleted.": "Resignation successfully deleted.", "Resignation successfully updated.": "Resignation successfully updated.", "Response": "Response", "Response Detail": "Response Detail", "Restore": "Rest<PERSON>", "Resume": "Resume", "Return Home": "Return Home", "Revenue": "Revenue", "Revenue :": "Revenue :", "Revenue : ": "Revenue : ", "Revenue Amount": "Revenue Amount", "Revenue Date": "Revenue Date", "Revenue successfully created": "Revenue successfully created", "Revenue successfully created, Webhook call failed.": "Revenue successfully created, Webhook call failed.", "Revenue successfully created.": "Revenue successfully created.", "Revenue successfully deleted.": "Revenue successfully deleted.", "Revenue Updated Successfully": "Revenue Updated Successfully", "Role": "Role", "Role Edit": "Role Edit", "Role successfully deleted.": "Role successfully deleted.", "RTL Layout": "RTL Layout", "S3 Bucket": "S3 Bucket", "S3 Endpoint": "S3 Endpoint", "S3 Key": "S3 Key", "S3 Region": "S3 Region", "S3 Secret": "S3 Secret", "S3 URL": "S3 URL", "Safe money transfer using your bank account. We support Mastercard, Visa, Discover and American express.": "Safe money transfer using your bank account. We support Mastercard, Visa, Discover and American express.", "Salary": "Salary", "Salary Date": "Salary Date", "Salary Duration": "Salary Duration", "Salary Slip": "<PERSON><PERSON>", "Salary Type": "Salary Type", "Sale Price": "Sale Price", "Sales": "Sales", "Sales by Customer": "Sales by Customer", "Sales by Item": "Sales by Item", "Sales Report": "Sales Report", "Sales With Tax": "Sales With Tax", "Salt Key": "Salt Key", "Salt Passphrase": "Salt Passphrase", "Sandbox": "Sandbox", "Saturation Deduction": "Saturation Deduction", "SaturationDeduction  successfully created.": "SaturationDeduction  successfully created.", "SaturationDeduction successfully deleted.": "SaturationDeduction successfully deleted.", "SaturationDeduction successfully updated.": "SaturationDeduction successfully updated.", "Save": "Save", "Save Change": "Save Change", "Save Changes": "Save Changes", "Save messages secretly": "Save messages secretly", "Saved Messages": "Saved Messages", "Schedule Detail": "Schedule Detail", "Schedule List": "Schedule List", "Screenshot": "Screenshot", "Screenshots": "Screenshots", "SCREENSHOTS": "SCREENSHOTS", "Screenshots List": "Screenshots List", "Search by Barcode Scanner": "Search by Barcode Scanner", "Search by Name": "Search by Name", "Search by Name or skill": "Search by Name or skill", "Search Product": "Search Product", "Secrect Key": "Secrect Key", "Secret Key": "Secret Key", "See Detail": "See Detail", "See My Tasks": "See My Tasks", "Select any Department": "Select any Department", "Select any Designation": "Select any Designation", "Select Barcode Format": "Select Barcode Format", "Select Barcode Type": "Select Barcode Type", "Select Bill": "Select Bill", "Select Branch": "Select Branch", "select Branch": "select Branch", "Select Category": "Select Category", "Select Client": "Select Client", "Select CSV File": "Select CSV File", "Select Customer": "Select Customer", "Select Date": "Select Date", "Select Date/Time": "Select Date/Time", "Select Department": "Select Department", "Select Designation": "Select Designation", "Select Designation ...": "Select Designation ...", "Select Employee": "Select Employee", "Select Invoice": "Select Invoice", "Select Leave Type": "Select Leave Type", "Select Method": "Select Method", "Select Milestone": "Select Milestone", "Select Module": "Select Module", "Select Month": "Select Month", "Select Pipeline": "Select Pipeline", "Select Product": "Select Product", "Select Products": "Select Products", "Select Project": "Select Project", "Select Source": "Select Source", "Select Sources": "Select Sources", "Select Stage": "Select Stage", "Select Status": "Select Status", "Select Timezone": "Select Timezone", "Select User": "Select User", "Select Users": "Select Users", "Select Warehouse": "Select Warehouse", "Select Year": "Select Year", "Send": "Send", "Send Bill": "Send Bill", "Send Email": "Send Email", "Send Invoice": "Send Invoice", "Send Password Reset Link": "Send Password Reset Link", "Send Proposal": "Send Proposal", "Send Purchase": "Send Purchase", "Send Request": "Send Request", "Send Test Mail": "Send Test Mail", "Sent on": "<PERSON>t on", "SEO Settings": "SEO Settings", "September": "September", "Server Key": "Server Key", "Service": "Service", "Service Description": "Service Description", "Set Basic Sallary": "Set Basic Sallary", "Set Priority of your task": "Set Priority of your task", "Set salary": "Set salary", "Set Salary": "Set Salary", "Setting added successfully saved.": "Setting added successfully saved.", "Setting saved successfully!": "Setting saved successfully!", "Setting successfully updated.": "Setting successfully updated.", "Settings": "Settings", "Setup Subscription Plan": "Setup Subscription Plan", "Share Your Link": "Share Your Link", "shared photos": "shared photos", "Shared Project Settings": "Shared Project Settings", "Shared project settings": "Shared project settings", "Ship to": "Ship to", "Ship To": "Ship To", "Ship To:": "Ship To:", "Shipped To": "Shipped To", "Shipped To :": "Shipped To :", "Shipping Address": "Shipping Address", "Shipping address status successfully changed.": "Shipping address status successfully changed.", "Shipping City": "Shipping City", "Shipping Country": "Shipping Country", "Shipping Info": "Shipping Info", "Shipping Name": "Shipping Name", "Shipping Phone": "Shipping Phone", "Shipping Same As Billing": "Shipping Same As Billing", "Shipping State": "Shipping State", "Shipping Zip": "Shipping Zip", "Shop ID Key": "Shop ID Key", "Show": "Show", "Show All": "Show All", "Sidebar settings": "Sidebar settings", "Sign": "Sign", "signature": "signature", "Signature Key": "Signature Key", "Signin": "Signin", "Site Description": "Site Description", "Site Logo": "Site Logo", "Skill": "Skill", "Skills": "Skills", "Skrill": "Skrill", "Skrill Email": "Skrill Email", "SKU": "SKU", "Sku": "S<PERSON>", "Slack Settings": "<PERSON><PERSON><PERSON> Settings", "Slack updated successfully.": "Slack updated successfully.", "Slack Webhook URL": "Slack Webhook URL", "Slect Stage": "Slect Stage", "Slider": "Slide<PERSON>", "Soft Deleted": "Soft Deleted", "Some Thing Is Wrong!": "Some Thing Is Wrong!", "Something is wrong.": "Something is wrong.", "Something went wrong": "Something went wrong", "something went wrong please try again": "something went wrong please try again", "Something went wrong please try again.": "Something went wrong please try again.", "Something went wrong!": "Something went wrong!", "Something went wrong.": "Something went wrong.", "Soon will be available": "Soon will be available", "Sorry we can't find any timesheet records on this week.": "Sorry we can't find any timesheet records on this week.", "Source": "Source", "Source Name": "Source Name", "Source successfully created!": "Source successfully created!", "Source successfully deleted!": "Source successfully deleted!", "Source successfully updated!": "Source successfully updated!", "Sources": "Sources", "Sources Conversion": "Sources Conversion", "Sources successfully deleted!": "Sources successfully deleted!", "Sources successfully updated!": "Sources successfully updated!", "span": "span", "SSPay": "SSPay", "Sspay": "Sspay", "SSpay": "SSpay", "SSPpay": "SSPpay", "Staff": "Staff", "Staff Report": "Staff Report", "Stage": "Stage", "Stage Name": "Stage Name", "Star": "Star", "Starred": "Starred", "Start": "Start", "Start Date": "Start Date", "Start Date   :": "Start Date   :", "Start Date  :": "Start Date  :", "Start Date / Time": "Start Date / Time", "Start Date : ": "Start Date : ", "Start Free Trial": "Start Free Trial", "Start meeting": "Start meeting", "Start Month": "Start Month", "Start Time": "Start Time", "Start URl": "Start URl", "Start with Starter": "Start with Starter", "State": "State", "Status": "Status", "status": "status", "Status successfully updated!": "Status successfully updated!", "Storage": "Storage", "Storage Limit": "Storage Limit", "Storage limit": "Storage limit", "Storage Settings": "Storage Settings", "Store Id": "Store Id", "Strictly Cookie Description": "Strictly Cookie Description", "Strictly Cookie Title": "Strictly Cookie Title", "Strictly necessary cookies": "Strictly necessary cookies", "STRIPE": "STRIPE", "Stripe": "Stripe", "Stripe / Paypal": "Stripe / Paypal", "Stripe Key": "Stripe Key", "Stripe Secret": "Stripe Secret", "Strivtly Cookie Title": "Strivtly Cookie Title", "Sub Task": "Sub Task", "Sub Total": "Sub Total", "Sub Total:": "Sub Total:", "Subject": "Subject", "Subject : ": "Subject : ", "Submit": "Submit", "Submit your application": "Submit your application", "Subtotal": "Subtotal", "Success": "Success", "success": "success", "Summary": "Summary", "Sun": "Sun", "Suppiler": "Suppiler", "Support": "Support", "Support Description": "Support Description", "Support End Date": "Support End Date", "Support for User": "Support for User", "Support Not Found.": "Support Not Found.", "Support Priority": "Support Priority", "Support Reply": "Support Reply", "Support reply successfully send.": "Support reply successfully send.", "Support successfully added, Webhook call failed.": "Support successfully added, Webhook call failed.", "Support successfully added.": "Support successfully added.", "Support successfully deleted.": "Support successfully deleted.", "Support successfully updated.": "Support successfully updated.", "Support System": "Support System", "Support Title": "Support Title", "Support User Name": "Support User Name", "Supported Date": "Supported Date", "Sync": "Sync", "Sync All": "Sync All", "Synchronize in Google Calendar ?": "Synchronize in Google Calendar ?", "System Setting": "System Setting", "System Settings": "System Settings", "System will consider last stage as a completed / done task for get progress on project.": "System will consider last stage as a completed / done task for get progress on project.", "Tag": "Tag", "Tags": "Tags", "Tap": "Tap", "Target": "Target", "Target Achievement": "Target Achievement", "Target Rating": "Target Rating", "Task": "Task", "Task added successfully, Webhook call failed.": "Task added successfully, Webhook call failed.", "Task added successfully.": "Task added successfully.", "Task Calendar": "Calendar", "Task Calender": "Task Calender", "Task Checklist": "Task Checklist", "Task Deleted Successfully!": "Task Deleted Successfully!", "Task Deleted successfully.": "Task Deleted successfully.", "Task Detail": "Task Detail", "Task End Date": "Task End Date", "Task members": "Task members", "Task Name": "Task Name", "Task name": "Task name", "Task Priority": "Task Priority", "Task Progress": "Task Progress", "Task Stage": "Task Stage", "Task Stage Add Successfully": "Task Stage Add Successfully", "Task Stage Successfully Deleted.": "Task Stage Successfully Deleted.", "Task Stage Updated": "Task Stage Updated", "Task Start Date": "Task Start Date", "Task Status": "Task Status", "Task successfully created!": "Task successfully created!", "Task successfully deleted!": "Task successfully deleted!", "Task successfully updated!": "Task successfully updated!", "Task successfully updated, Webhook call failed.": "Task successfully updated, Webhook call failed.", "Task Updated successfully.": "Task Updated successfully.", "Task User": "Task User", "Tasks": "Tasks", "Tasks Overview": "Tasks Overview", "Tax": "Tax", "Tax %": "Tax %", "Tax Amount": "Tax Amount", "Tax Amount:": "Tax Amount:", "Tax Name": "Tax Name", "Tax Number": "Tax Number", "Tax Number ": "Tax Number ", "Tax Payer Id": "Tax Payer Id", "Tax Rate %": "Tax Rate %", "Tax Rate Name": "Tax Rate Name", "Tax rate successfully created.": "Tax rate successfully created.", "Tax rate successfully deleted.": "Tax rate successfully deleted.", "Tax rate successfully updated.": "Tax rate successfully updated.", "Tax Summary": "Tax Summary", "Tax:": "Tax:", "Taxes": "Taxes", "Team Member": "Team Member", "Telegram AccessToken": "Telegram AccessToken", "Telegram ChatID": "Telegram ChatID", "Telegram Settings": "Telegram Settings", "Telegram updated successfully.": "Telegram updated successfully.", "Telephone": "Telephone", "Termination": "Termination", "Termination  successfully created.": "Termination  successfully created.", "Termination Date": "Termination Date", "Termination date is required.": "Termination date is required.", "Termination successfully deleted.": "Termination successfully deleted.", "Termination successfully updated.": "Termination successfully updated.", "Termination Type": "Termination Type", "TerminationType  successfully created.": "TerminationType  successfully created.", "TerminationType successfully deleted.": "TerminationType successfully deleted.", "TerminationType successfully updated.": "TerminationType successfully updated.", "Terms": "Terms", "Terms And Conditions": "Terms And Conditions", "Testimonial": "Testimonial", "Testimonials": "Testimonials", "TESTIMONIALS": "TESTIMONIALS", "Testimonials List": "Testimonials List", "Thank you": "Thank you", "Thank You For Shopping With Us. Please visit again.": "Thank You For Shopping With Us. Please visit again.", "Thank you for your business!": "Thank you for your business!", "Thank you!": "Thank you!", "Thanks for signing up! Before getting started, could you verify your email address by clicking on the link we just emailed to you? If you didn't receive the email, we will gladly send you another.": "Thanks for signing up! Before getting started, could you verify your email address by clicking on the link we just emailed to you? If you didn't receive the email, we will gladly send you another.", "The admin has not set the payment method.": "The admin has not set the payment method.", "The company has subscribed to this plan, so it cannot be deleted.": "The company has subscribed to this plan, so it cannot be deleted.", "The company has subscribed to this plan, so it cannot be disabled.": "The company has subscribed to this plan, so it cannot be disabled.", "Theme Customizer": "Theme Customizer", "There are some deals on stage, please remove it first!": "There are some deals on stage, please remove it first!", "There are some Stages and Deals on Pipeline, please remove it first!": "There are some Stages and Deals on Pipeline, please remove it first!", "there is no account balance": "there is no account balance", "There is no Announcement List": "There is no Announcement List", "there is no employee": "there is no employee", "There is no event in this month": "There is no event in this month", "There is no goal.": "There is no goal.", "There is no holiday in this month": "There is no holiday in this month", "There is no latest contract": "There is no latest contract", "There is no latest expense": "There is no latest expense", "There is no latest income": "There is no latest income", "There is no meeting in this month": "There is no meeting in this month", "There is no recent bill": "There is no recent bill", "There is no recent invoice": "There is no recent invoice", "These credentials do not match our records.": "These credentials do not match our records.", "These details will be used to collect invoice payments. Each invoice will have a payment button based on the below configuration.": "These details will be used to collect invoice payments. Each invoice will have a payment button based on the below configuration.", "These details will be used to collect subscription plan payments.Each subscription plan will have a payment button based on the below configuration.": "These details will be used to collect subscription plan payments.Each subscription plan will have a payment button based on the below configuration.", "This action can not be undone. Do you want to continue?": "This action can not be undone. Do you want to continue?", "This branch has employees. Please remove the employee from this branch.": "This branch has employees. Please remove the employee from this branch.", "This candidate stage successfully changed.": "This candidate stage successfully changed.", "this category is already assign so please move or remove this category related data.": "this category is already assign so please move or remove this category related data.", "This client has assigned some estimation.": "This client has assigned some estimation.", "This coupon code has expired.": "This coupon code has expired.", "This Coupon Code is already taken.": "This Coupon Code is already taken.", "This coupon code is invalid or has expired.": "This coupon code is invalid or has expired.", "This data has not been inserted.": "This data has not been inserted.", "This department has employees. Please remove the employee from this department.": "This department has employees. Please remove the employee from this department.", "This designation has employees. Please remove the employee from this designation.": "This designation has employees. Please remove the employee from this designation.", "This document is password-protected. Please enter a password.": "This document is password-protected. Please enter a password.", "This employee is already sync.": "This employee is already sync.", "This ip is not allowed to clock in & clock out.": "This ip is not allowed to clock in & clock out.", "This is a page meant for more advanced users, simply ignore it if you don't understand what cache is.": "This is a page meant for more advanced users, simply ignore it if you don't understand what cache is.", "This mail send only for testing purpose.": "This mail send only for testing purpose.", "This Month Total Deals": "This Month Total Deals", "This Month Total Estimate": "This Month Total Estimate", "This Product is not found!": "This Product is not found!", "This product is out of stock!": "This product is out of stock!", "This request cannot be accepted because it exceeds the commission amount.": "This request cannot be accepted because it exceeds the commission amount.", "This request cannot be accepted because it less than the threshold amount.": "This request cannot be accepted because it less than the threshold amount.", "This SMTP will be used for sending your company-level email. If this field is empty, then SuperAdmin SMTP will be used for sending emails.": "This SMTP will be used for sending your company-level email. If this field is empty, then SuperAdmin SMTP will be used for sending emails.", "This SMTP will be used for system-level email sending. Additionally, if a company user does not set their SMTP, then this SMTP will be used for sending emails.": "This SMTP will be used for system-level email sending. Additionally, if a company user does not set their SMTP, then this SMTP will be used for sending emails.", "this tax is already assign to proposal or bill or invoice or product&service so please move or remove this tax related data.": "this tax is already assign to proposal or bill or invoice or product&service so please move or remove this tax related data.", "This textarea will autosize while you type": "This textarea will autosize while you type", "this type is already use so please transfer or delete this type related data.": "this type is already use so please transfer or delete this type related data.", "this unit is already assign so please move or remove this unit related data.": "this unit is already assign so please move or remove this unit related data.", "This week": "This week", "This Week Deals Conversions": "This Week Deals Conversions", "This Week Leads Conversions": "This Week Leads Conversions", "This Week Total Deals": "This Week Total Deals", "This Week Total Estimate": "This Week Total Estimate", "Thousands Separator": "Thousands Separator", "Thu": "<PERSON>hu", "Ticket": "Ticket", "Time": "Time", "Time Format": "Time Format", "Time Logged": "Time Logged", "Time Tracker Settings": "Time Tracker Settings", "Time Tracker successfully updated.": "Time Tracker successfully updated.", "Time Updated": "Time Updated", "Timesheet": "Timesheet", "Timesheet Created Successfully!": "Timesheet Created Successfully!", "Timesheet deleted Successfully!": "Timesheet deleted Successfully!", "Timesheet List": "Timesheet List", "Timesheet Logged Hours": "Timesheet Logged Hours", "Timesheet Updated Successfully!": "Timesheet Updated Successfully!", "TimeTracker successfully deleted.": "TimeTracker successfully deleted.", "Timezone": "Timezone", "Title": "Title", "Title Text": "Title Text", "to": "to", "To": "To", "To Account": "To Account", "To add timesheet record go to ": "To add timesheet record go to ", "To add timesheet record go to Add Task on Timesheet": "To add timesheet record go to Add Task on Timesheet", "To Date": "To Date", "To do list": "To do list", "To Warehouse": "To Warehouse", "Today's Not Clock In": "Today's Not Clock In", "Todo Added Successfully!": "<PERSON><PERSON> Added Successfully!", "Todo Deleted Successfully!": "Todo Deleted Successfully!", "Todo Title": "Todo Title", "Todo Updated Successfully!": "Todo Updated Successfully!", "Token": "Token", "Tone": "<PERSON><PERSON>", "Top Bar": "Top Bar", "Top Due Project": "Top Due Project", "Top Due Projects": "Top Due Projects", "Top Due Task": "Top Due Task", "Top Due Tasks": "Top Due Tasks", "Total": "Total", "total :": "total :", "Total :": "Total :", "Total Allowance": "Total Allowance", "Total Amount": "Total Amount", "Total Amounts": "Total Amounts", "Total Basic Salary": "Total Basic Salary", "Total Bill": "Total Bill", "Total Bills": "Total Bills", "Total Bugs": "Total Bugs", "Total Client": "Total Client", "Total Clients": "Total Clients", "Total Commission": "Total Commission", "Total Companies": "Total Companies", "Total Completed task in last 7 days": "Total Completed task in last 7 days", "Total Contract": "Total Contract", "Total Credit": "Total Credit", "Total Customers": "Total Customers", "Total Days": "Total Days", "Total Deal": "Total Deal", "Total Deals": "Total Deals", "Total Debit": "Total Debit", "Total Deduction": "Total Deduction", "Total Due": "Total Due", "Total early leave in hours": "Total early leave in hours", "Total Earning": "Total Earning", "Total Employee": "Total Employee", "Total Estimate": "Total Estimate", "Total Expense": "Total Expense", "Total Expense =  Payment + Bill ": "Total Expense =  Payment + Bill ", "Total Expenses": "Total Expenses", "Total hrs of project ": "Total hrs of project ", "Total Income": "Total Income", "Total Income =  Revenue + Invoice ": "Total Income =  Revenue + Invoice ", "Total Invoice": "Total Invoice", "Total Invoices": "Total Invoices", "Total Jobs": "Total Jobs", "Total late in hours": "Total late in hours", "Total Lead": "Total Lead", "Total leave": "Total leave", "Total Loan": "Total Loan", "Total Logged Hours": "Total Logged Hours", "Total Members": "Total Members", "Total Milestone": "Total Milestone", "Total Net Salary": "Total Net Salary", "Total Order Amount : ": "Total Order Amount : ", "Total Orders": "Total Orders", "Total Other Payment": "Total Other Payment", "Total Overtime": "Total Overtime", "Total overtime in hours": "Total overtime in hours", "Total Paid": "Total Paid", "Total Plans": "Total Plans", "Total present": "Total present", "Total Product": "Total Product", "Total Project": "Total Project", "Total Project Tasks": "Total Project Tasks", "Total project time spent": "Total project time spent", "Total Projects": "Total Projects", "Total Saturation Deduction": "Total Saturation Deduction", "Total Staff": "Total Staff", "Total Sum of Bills": "Total Sum of Bills", "Total Sum of Invoices": "Total Sum of Invoices", "Total Task": "Total Task", "Total Tasks": "Total Tasks", "Total Time": "Total Time", "Total Time worked on this task": "Total Time worked on this task", "Total Training": "Total Training", "Total Unpaid Employee": "Total Unpaid Employee", "Total User": "Total User", "Total Users": "Total Users", "Total Vendors": "Total Vendors", "Total Warehouse": "Total Warehouse", "Total:": "Total:", "Totals": "Totals", "Toyyibpay": "Toyyibpay", "Track not found.": "Track not found.", "Track remove successfully.": "Track remove successfully.", "Tracker": "Tracker", "Tracker details": "Tracker details", "Tracker Photo remove successfully.": "Tracker Photo remove successfully.", "Tracking Interval": "Tracking Interval", "Trainer": "Trainer", "Trainer ": "Trainer ", "Trainer  successfully created.": "Trainer  successfully created.", "Trainer  successfully updated.": "Trainer  successfully updated.", "Trainer Detail": "Trainer <PERSON><PERSON>", "Trainer Option": "Trainer Option", "Trainer successfully deleted.": "Trainer successfully deleted.", "Trainig Details": "Trainig Details", "Training": "Training", "Training Cost": "Training Cost", "Training Details": "Training Details", "Training Duration": "Training Duration", "Training Employee": "Training Employee", "Training List": "Training List", "Training Not Found.": "Training Not Found.", "Training Setup": "Training Setup", "Training status successfully updated.": "Training status successfully updated.", "Training successfully created.": "Training successfully created.", "Training successfully deleted.": "Training successfully deleted.", "Training successfully updated.": "Training successfully updated.", "Training Type": "Training Type", "TrainingType  successfully created.": "TrainingType  successfully created.", "TrainingType successfully deleted.": "TrainingType successfully deleted.", "TrainingType successfully updated.": "TrainingType successfully updated.", "Transaction": "Transaction", "Transaction Date": "Transaction Date", "Transaction fail": "Transaction fail", "Transaction fail!": "Transaction fail!", "Transaction has been completed.": "Transaction has been completed.", "Transaction has been complted.": "Transaction has been complted.", "Transaction has been failed": "Transaction has been failed", "Transaction has been failed! ": "Transaction has been failed! ", "Transaction has been failed.": "Transaction has been failed.", "Transaction has been Successfull! ": "Transaction has been Successfull! ", "Transaction has failed": "Transaction has failed", "Transaction Summary": "Transaction Summary", "Transaction Type": "Transaction Type", "Transaction Unsuccesfull": "Transaction Unsuccesfull", "Transfer": "Transfer", "Transfer  successfully created.": "Transfer  successfully created.", "Transfer Branch": "Transfer Branch", "Transfer Date": "Transfer Date", "Transfer Department": "Transfer Department", "Transfer Desciption": "Transfer Desciption", "Transfer successfully deleted.": "Transfer successfully deleted.", "Transfer successfully updated.": "Transfer successfully updated.", "Transparent layout": "Transparent layout", "Travel  successfully created.": "Travel  successfully created.", "Travel successfully deleted.": "Travel successfully deleted.", "Travel successfully updated.": "Travel successfully updated.", "Trial Balance": "Trial Balance", "Trial Days": "Trial Days", "Trial is enable(on/off)": "Trial is enable(on/off)", "Trip": "Trip", "Trusted by": "Trusted by", "Tue": "<PERSON><PERSON>", "Twilio From": "<PERSON><PERSON><PERSON>", "Twilio Settings": "<PERSON><PERSON><PERSON>", "Twilio SID ": "<PERSON><PERSON><PERSON> ", "Twilio Token": "<PERSON><PERSON><PERSON>", "Twilio updated successfully.": "<PERSON><PERSON><PERSON> updated successfully.", "Type": "Type", "type": "type", "Type here....": "Type here....", "UnArchive": "UnArchive", "Unit": "Unit", "Unit Name": "Unit Name", "Unit successfully created.": "Unit successfully created.", "Unit successfully deleted.": "Unit successfully deleted.", "Unit successfully updated.": "Unit successfully updated.", "Unknown error occurred": "Unknown error occurred", "Unlimited": "Unlimited", "Unpaid": "Unpaid", "UnPaid": "UnPaid", "Upcoming Events": "Upcoming Events", "Update": "Update", "Update permission": "Update permission", "Update Quantity": "Update Quantity", "Update Role": "Update Role", "Update Sources": "Update Sources", "Update Status": "Update Status", "Upgrade Plan": "Upgrade Plan", "Upload": "Upload", "Upload new file": "Upload new file", "Url": "Url", "Used": "Used", "Used 100 % discount coupon code.": "Used 100 % discount coupon code.", "User": "User", "user": "user", "User Assigned": "User Assigned", "User Avtar": "User Avtar", "User invited successfully.": "User invited successfully.", "User Log": "User Log", "User login disable successfully.": "User login disable successfully.", "User login enable successfully.": "User login enable successfully.", "User Logs History": "User Logs History", "User Management": "User Management", "User Name": "User Name", "User Role": "User Role", "User successfully created.": "User successfully created.", "User successfully deleted .": "User successfully deleted .", "User successfully deleted!": "User successfully deleted!", "User successfully disable.": "User successfully disable.", "User successfully unable.": "User successfully unable.", "Username": "Username", "Users": "Users", "Users successfully updated!": "Users successfully updated!", "v2": "v2", "v3": "v3", "Value": "Value", "Variables": "Variables", "VAT Number": "VAT Number", "Vender": "Vender", "Vendor": "<PERSON><PERSON><PERSON>", "Vendor Balance": "<PERSON><PERSON><PERSON>", "Vendor Email": "<PERSON><PERSON><PERSON>", "Vendor Id": "Vendor Id", "Vendor Info": "Vendor Info", "Vendor Name": "Vendor Name", "Vendor Not Found.": "V<PERSON>or Not Found.", "Vendor Prefix": "<PERSON><PERSON><PERSON>", "Vendor successfully created.": "<PERSON><PERSON><PERSON> successfully created.", "Vendor successfully deleted.": "<PERSON><PERSON><PERSON> successfully deleted.", "Vendor successfully updated.": "<PERSON><PERSON><PERSON> successfully updated.", "Vendors": "Vend<PERSON>", "Verical View": "Verical View", "Verify Email": "<PERSON><PERSON><PERSON>", "Vertical View": "Vertical View", "View": "View", "View Budget Planner": "View Budget Planner", "View Detail": "View Detail", "View Employee Detail": "View Employee Detail", "View Project Report": "View Project Report", "View Response": "View Response", "View Screenshot images": "View Screenshot images", "View User Logs": "View User Logs", "Walk-in Customer": "Walk-in Customer", "Warehouse": "Warehouse", "Warehouse Details": "Warehouse Details", "Warehouse Name": "Warehouse Name", "Warehouse Report": "Warehouse Report", "Warehouse Stock Details": "Warehouse Stock Details", "Warehouse successfully created.": "Warehouse successfully created.", "Warehouse successfully deleted.": "Warehouse successfully deleted.", "Warehouse successfully updated.": "Warehouse successfully updated.", "Warehouse Transfer": "Warehouse Transfer", "Warehouse Transfer successfully created.": "Warehouse Transfer successfully created.", "Warehouse Transfer successfully deleted.": "Warehouse Transfer successfully deleted.", "Warning": "Warning", "Warning  successfully created.": "Warning  successfully created.", "Warning By": "Warning By", "Warning Date": "Warning Date", "Warning successfully deleted.": "Warning successfully deleted.", "Warning successfully updated.": "Warning successfully updated.", "Warning To": "Warning To", "Wasabi": "<PERSON><PERSON>", "Wasabi Bucket": "<PERSON><PERSON>", "Wasabi Key": "<PERSON><PERSON>", "Wasabi Region": "Wasabi Region", "Wasabi Root": "<PERSON><PERSON>", "Wasabi Secret": "<PERSON>abi <PERSON>", "Wasabi URL": "Wasabi URL", "We couldn't find any data": "We couldn't find any data", "We successfully planned a refund and assigned a free plan.": "We successfully planned a refund and assigned a free plan.", "We will send a link to reset your password.": "We will send a link to reset your password.", "Webhook call failed.": "Webhook call failed.", "Webhook Edit": "Webhook Edit", "Webhook Settings": "Webhook Settings", "Webhook successfully created.": "Webhook successfully created.", "Webhook successfully deleted.": "Webhook successfully deleted.", "Webhook successfully Updated.": "Webhook successfully Updated.", "Wed": "Wed", "Week": "Week", "Week total": "Week total", "Welcome, ": "Welcome, ", "With Currency Name": "With Currency Name", "With Currency Symbol": "With Currency Symbol", "With space": "With space", "Without space": "Without space", "Work there. Find the dream job you’ve always wanted..": "Work there. Find the dream job you’ve always wanted..", "Working Location": "Working Location", "Write here...": "Write here...", "Write message": "Write message", "Xendit": "Xendit", "Xendit   ": "Xendit   ", "Year": "Year", "Year :": "Year :", "Yearly": "Yearly", "Yes": "Yes", "Yookassa": "<PERSON><PERSON><PERSON>", "You": "You", "You already send request to another plan.": "You already send request to another plan.", "You are unable to upgrade this plan because it is disabled.": "You are unable to upgrade this plan because it is disabled.", "You can easily change order of deal stage using drag & drop.": "You can easily change order of deal stage using drag & drop.", "You can easily change order of job stage using drag & drop.": "You can easily change order of job stage using drag & drop.", "You can easily change order of lead stage using drag & drop.": "You can easily change order of lead stage using drag & drop.", "You can easily change order of project Bug status using drag & drop.": "You can easily change order of project Bug status using drag & drop.", "You can easily change order of project task stage using drag & drop.": "You can easily change order of project task stage using drag & drop.", "You can find out how to do that here.": "You can find out how to do that here.", "You can not delete By default Company": "You can not delete By default Company", "you can only update current day attendance.": "you can only update current day attendance.", "You can't change Date!": "You can't change Date!", "You need help?": "You need help?", "You want to confirm convert to invoice. Press Yes to continue or Cancel to go back": "You want to confirm convert to invoice. Press Yes to continue or Can<PERSON> to go back", "You want to confirm duplicate this invoice. Press Yes to continue or Cancel to go back": "You want to confirm duplicate this invoice. Press Yes to continue or <PERSON><PERSON> to go back", "Your Account is deactive by admin,please contact your Administrator.": "Your Account is deactive by admin,please contact your Administrator.", "Your Account is deleted by admin,please contact your Administrator.": "Your Account is deleted by admin,please contact your Administrator.", "Your Account is disable from company.": "Your Account is disable from company.", "Your Account is disable,please contact your Administrator.": "Your Account is disable,please contact your Administrator.", "Your contact list is empty": "Your contact list is empty", "Your currency is not supported.": "Your currency is not supported.", "Your employee limit is over, Please upgrade plan.": "Your employee limit is over, Please upgrade plan.", "Your favorite list is empty": "Your favorite list is empty", "Your member list is empty": "Your member list is empty", "Your Payment has failed!": "Your Payment has failed!", "Your payment has failed.": "Your payment has failed.", "Your payment is cancel": "Your payment is cancel", "Your Plan is expired.": "Your Plan is expired.", "Your plan storage limit is over , so you can not see customer uploaded payment receipt": "Your plan storage limit is over , so you can not see customer uploaded payment receipt", "Your Transaction has been failed.": "Your Transaction has been failed.", "Your Transaction is fail please try again": "Your Transaction is fail please try again", "Your Transaction is fail please try again.": "Your Transaction is fail please try again.", "Your transaction on pandding": "Your transaction on pandding", "Your Transaction on pending": "Your Transaction on pending", "Your transaction on pending": "Your transaction on pending", "Your Trial plan Expired.": "Your Trial plan Expired.", "Your user limit is over, Please upgrade plan.": "Your user limit is over, Please upgrade plan.", "Zero Price": "Zero Price", "Zip": "Zip", "Zip Code": "Zip Code", "Zip/Post Code": "Zip/Post Code", "ZKTeco Api URL": "ZKTeco Api URL", "Zoom Account ID": "Zoom Account ID", "Zoom Client ID": "Zoom Client ID", "Zoom Client Secret Key": "Zoom Client Secret Key", "Zoom Meeting": "Zoom Meeting", "Zoom Meeting successfully created.": "Zoom Meeting successfully created.", "Zoom Settings": "<PERSON>m <PERSON>s"}