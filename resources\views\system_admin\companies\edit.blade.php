@extends('layouts.admin')

@section('page-title')
    {{ __('Edit Company') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('system-admin.dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ route('system-admin.companies') }}">{{ __('Companies') }}</a></li>
    <li class="breadcrumb-item">{{ __('Edit') }}</li>
@endsection

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-12">
                            {{ Form::model($company, ['route' => ['system-admin.companies.update', $company->id], 'method' => 'PUT', 'enctype' => 'multipart/form-data']) }}
                            <div class="row">
                                <div class="col-lg-6 col-md-6 col-sm-6">
                                    <div class="form-group">
                                        {{ Form::label('name', __('Company Name'), ['class' => 'form-label']) }}
                                        {{ Form::text('name', null, ['class' => 'form-control', 'placeholder' => __('Enter Company Name'), 'required' => 'required']) }}
                                        @error('name')
                                            <span class="invalid-name" role="alert">
                                                <strong class="text-danger">{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-6">
                                    <div class="form-group">
                                        {{ Form::label('email', __('Email'), ['class' => 'form-label']) }}
                                        {{ Form::email('email', null, ['class' => 'form-control', 'placeholder' => __('Enter Email'), 'required' => 'required']) }}
                                        @error('email')
                                            <span class="invalid-email" role="alert">
                                                <strong class="text-danger">{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-6">
                                    <div class="form-group">
                                        {{ Form::label('password', __('Password'), ['class' => 'form-label']) }}
                                        {{ Form::password('password', ['class' => 'form-control', 'placeholder' => __('Enter Password (leave blank to keep current)'), 'minlength' => '6']) }}
                                        <small class="text-muted">{{ __('Leave blank to keep current password') }}</small>
                                        @error('password')
                                            <span class="invalid-password" role="alert">
                                                <strong class="text-danger">{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-6">
                                    <div class="form-group">
                                        {{ Form::label('plan_id', __('Plan'), ['class' => 'form-label']) }}
                                        {{ Form::select('plan_id', $plans->pluck('name', 'id'), $company->plan, ['class' => 'form-control select', 'placeholder' => __('Select Plan')]) }}
                                        @error('plan_id')
                                            <span class="invalid-plan_id" role="alert">
                                                <strong class="text-danger">{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 text-end">
                            <input type="submit" value="{{ __('Update') }}" class="btn-create badge-blue">
                            <input type="button" value="{{ __('Cancel') }}" onclick="location.href = '{{ route('system-admin.companies') }}';" class="btn-create bg-gray">
                        </div>
                    </div>
                    {{ Form::close() }}
                </div>
            </div>
        </div>
    </div>
@endsection
