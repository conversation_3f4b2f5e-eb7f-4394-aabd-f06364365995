@extends('layouts.admin')

@section('page-title')
    {{ __('Staff Permissions') }} - {{ $staff->name }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('system-admin.dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ route('system-admin.staff.index') }}">{{ __('Staff Management') }}</a></li>
    <li class="breadcrumb-item">{{ __('Permissions') }}</li>
@endsection

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('Manage Permissions for') }} {{ $staff->name }}</h5>
                    <small class="text-muted">{{ __('Company: ') . $company->name }}</small>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('system-admin.staff.permissions.update', $staff->id) }}">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <i class="ti ti-info-circle"></i>
                                    {{ __('Select the permissions you want to grant to this staff member. These permissions will determine what sections of the system admin panel they can access.') }}
                                </div>
                            </div>
                        </div>

                        @foreach($allPermissions as $category => $permissions)
                            <div class="row mb-4">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">{{ ucfirst(str_replace('_', ' ', $category)) }} {{ __('Permissions') }}</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                @foreach($permissions as $permission)
                                                    <div class="col-md-4 mb-2">
                                                        <div class="form-check">
                                                            <input class="form-check-input" 
                                                                   type="checkbox" 
                                                                   name="permissions[]" 
                                                                   value="{{ $permission->name }}" 
                                                                   id="permission_{{ $permission->id }}"
                                                                   {{ in_array($permission->name, $staffPermissions) ? 'checked' : '' }}>
                                                            <label class="form-check-label" for="permission_{{ $permission->id }}">
                                                                {{ ucfirst(str_replace('_', ' ', $permission->name)) }}
                                                            </label>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach

                        <div class="row">
                            <div class="col-12">
                                <div class="text-end">
                                    <a href="{{ route('system-admin.staff.index') }}" class="btn btn-secondary">
                                        {{ __('Back to Staff') }}
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        {{ __('Update Permissions') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<script>
    $(document).ready(function() {
        // Add select all functionality for each category
        $('.card-header').each(function() {
            const cardHeader = $(this);
            const cardBody = cardHeader.next('.card-body');
            const checkboxes = cardBody.find('input[type="checkbox"]');
            
            // Add select all button
            cardHeader.append(`
                <div class="float-end">
                    <button type="button" class="btn btn-sm btn-outline-primary select-all-btn">
                        ${checkboxes.filter(':checked').length === checkboxes.length ? 'Deselect All' : 'Select All'}
                    </button>
                </div>
            `);
        });

        // Handle select all button clicks
        $(document).on('click', '.select-all-btn', function() {
            const button = $(this);
            const cardBody = button.closest('.card').find('.card-body');
            const checkboxes = cardBody.find('input[type="checkbox"]');
            const allChecked = checkboxes.filter(':checked').length === checkboxes.length;
            
            checkboxes.prop('checked', !allChecked);
            button.text(allChecked ? 'Select All' : 'Deselect All');
        });

        // Update select all button text when individual checkboxes change
        $(document).on('change', 'input[type="checkbox"][name="permissions[]"]', function() {
            const checkbox = $(this);
            const cardBody = checkbox.closest('.card-body');
            const card = cardBody.closest('.card');
            const selectAllBtn = card.find('.select-all-btn');
            const checkboxes = cardBody.find('input[type="checkbox"]');
            const allChecked = checkboxes.filter(':checked').length === checkboxes.length;
            
            selectAllBtn.text(allChecked ? 'Deselect All' : 'Select All');
        });
    });
</script>
@endpush
