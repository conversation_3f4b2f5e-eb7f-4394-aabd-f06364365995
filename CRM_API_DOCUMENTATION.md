# CRM & Booking API Documentation

This document describes the CRM and Booking API endpoints that require SSO token authentication and return JSON responses.

## Authentication

All CRM API endpoints require SSO token authentication. You need to:

1. **Login** to get an authentication token:
   ```
   POST /api/login
   {
     "email": "<EMAIL>",
     "password": "password"
   }
   ```
   **Response:**
   ```json
   {
     "success": true,
     "data": {
       "token": "your_auth_token_here",
       "user": 123,
       "settings": {
         "shot_time": 0.5
       }
     },
     "message": "Login successfully."
   }
   ```

2. **Generate SSO Token** using the authentication token:
   ```
   POST /api/generate-sso-token
   Headers: Authorization: Bearer {auth_token}
   ```
   **Response:**
   ```json
   {
     "success": true,
     "token": "your_sso_token_here",
     "expires_in": 315360000
   }
   ```

3. **Use SSO Token** for CRM API calls:
   ```
   Headers: Authorization: Bearer {sso_token}
   ```

4. **Logout** (revoke all tokens):
   ```
   POST /api/logout
   Headers: Authorization: Bearer {auth_token}
   ```
   **Response:**
   ```json
   {
     "success": true,
     "data": [],
     "message": "Tokens Revoked"
   }
   ```

5. **Validate SSO Token**:
   ```
   POST /api/validate-sso-token
   Headers: Authorization: Bearer {sso_token}
   ```

### SSO Token Details
- **Expires in:** 15 minutes (900 seconds)
- **Algorithm:** HS256
- **Required Headers:** `Authorization: Bearer {sso_token}` or `X-SSO-Token: {sso_token}`
- **Alternative:** Can be passed as `token` parameter in request

## Webhooks

Many CRM operations trigger webhook calls to external systems. The following actions trigger webhooks:

### Lead Webhooks
- **Lead Created:** `crm.lead_created`
- **Lead Updated:** `crm.lead_updated`
- **Lead Deleted:** `crm.lead_deleted`
- **Lead Stage Changed:** `crm.lead_stage_changed`

### Deal Webhooks
- **Deal Created:** `crm.deal_created`
- **Deal Updated:** `crm.deal_updated`
- **Deal Deleted:** `crm.deal_deleted`
- **Deal Stage Changed:** `crm.deal_stage_changed`

**Note:** Webhook calls are made to the base URL from the module integration table with endpoint `/saas-webhook`. Failed webhook dispatches are logged but do not affect the API response.

## Move Operations Response Format

Move operations return a consistent response format:

**Success Response:**
```json
{
  "status": "success",
  "message": "Lead moved from Stage 1 to Stage 2",
  "lead_id": 123,
  "old_stage_id": 1,
  "new_stage_id": 2
}
```

**Error Response:**
```json
{
  "error": "Validation failed",
  "messages": {
    "stage_id": ["The stage id field is required."]
  }
}
```

**Permission Denied Response:**
```json
{
  "status": "error",
  "message": "Permission Denied."
}
```

## Base URL

- CRM API endpoints are prefixed with `/api/crm/`
- Booking API endpoints are prefixed with `/api/booking/`

## Response Format

All endpoints return JSON responses in the following format:

**Success Response:**
```json
{
  "success": true,
  "data": {...},
  "message": "Operation completed successfully"
}
```

**Error Response:**
```json
{
  "error": "Error type",
  "message": "Detailed error message",
  "messages": {...} // Validation errors (if applicable)
}
```

## Leads API

### Get All Leads
- **GET** `/api/crm/leads`
- **Response:** List of all leads with stage, pipeline, and labels

### Get Single Lead
- **GET** `/api/crm/leads/{id}`
- **Response:** Lead details with related data (files, tasks, calls, emails, discussions)

### Create Lead
- **POST** `/api/crm/leads`
- **Body:**
  ```json
  {
    "subject": "Lead subject",
    "name": "Lead name",
    "email": "<EMAIL>",
    "pipeline_id": 1,
    "stage_id": 1,
    "sources": "Website",
    "products": "Product name",
    "notes": "Optional notes",
    "labels": [1, 2], // Optional array of label IDs
    "date": "2024-01-01", // Optional
    "next_follow_up_date": "2024-01-15", // Optional
    "users": [1, 2] // Optional array of user IDs to assign
  }
  ```

### Update Lead
- **PUT** `/api/crm/leads/{id}`
- **Body:** Same as create lead

### Delete Lead
- **DELETE** `/api/crm/leads/{id}`

### Get Lead Tasks
- **GET** `/api/crm/leads/{leadId}/tasks`

### Get Lead Activity Logs
- **GET** `/api/crm/leads/{leadId}/activity-logs`

### Move Lead to Stage
- **POST** `/api/crm/leads/{id}/move-stage`
- **Body:**
  ```json
  {
    "stage_id": 2
  }
  ```
- **Response:**
  ```json
  {
    "status": "success",
    "message": "Lead moved from Stage 1 to Stage 2",
    "lead_id": 123,
    "old_stage_id": 1,
    "new_stage_id": 2
  }
  ```

### Move Lead with Order (Kanban Style)
- **POST** `/api/crm/leads/move-to-stage`
- **Body:**
  ```json
  {
    "lead_id": 123,
    "stage_id": 2,
    "order": [123, 456, 789]
  }
  ```
- **Response:**
  ```json
  {
    "status": "success",
    "message": "Lead successfully moved.",
    "lead_id": 123,
    "old_stage_id": 1,
    "new_stage_id": 2
  }
  ```

### Assign Lead to Users
- **POST** `/api/crm/leads/{id}/assign-users`
- **Body:**
  ```json
  {
    "user_ids": [1, 2, 3]
  }
  ```

### Unassign Lead from Users
- **DELETE** `/api/crm/leads/{id}/unassign-users`
- **Body:**
  ```json
  {
    "user_ids": [1, 2]
  }
  ```

### Move Lead to Pipeline
- **POST** `/api/crm/leads/{id}/move-pipeline`
- **Body:**
  ```json
  {
    "pipeline_id": 2,
    "stage_id": 5
  }
  ```

### Add Labels to Lead
- **POST** `/api/crm/leads/{id}/add-labels`
- **Body:**
  ```json
  {
    "label_ids": [1, 2, 3]
  }
  ```

### Remove Labels from Lead
- **DELETE** `/api/crm/leads/{id}/remove-labels`
- **Body:**
  ```json
  {
    "label_ids": [1, 2]
  }
  ```

### Export Leads
- **GET** `/api/crm/leads/export` - Export all leads
- **GET** `/api/crm/leads/{id}/export` - Export single lead

### Lead Custom Fields
- **GET** `/api/crm/leads/{id}/custom-fields` - Get lead custom fields
- **POST** `/api/crm/leads/{id}/custom-fields` - Update lead custom fields
- **Body:**
  ```json
  {
    "custom_fields": [
      {
        "field_id": 1,
        "value": "Custom value"
      }
    ]
  }
  ```

## Deals API

### Get All Deals
- **GET** `/api/crm/deals`

### Get Single Deal
- **GET** `/api/crm/deals/{id}`

### Create Deal
- **POST** `/api/crm/deals`
- **Body:**
  ```json
  {
    "name": "Deal name",
    "phone": "************", // Optional
    "price": 1000, // Optional, defaults to 0
    "pipeline_id": 1,
    "stage_id": 1,
    "sources": "Website", // Optional
    "products": "Product name", // Optional
    "notes": "Optional notes",
    "labels": [1, 2], // Optional array of label IDs
    "status": "Active", // Optional, defaults to "Active"
    "clients": [1, 2] // Optional array of client IDs
  }
  ```

### Update Deal
- **PUT** `/api/crm/deals/{id}`
- **Body:** Same as create deal

### Delete Deal
- **DELETE** `/api/crm/deals/{id}`

### Get Deal Tasks
- **GET** `/api/crm/deals/{dealId}/tasks`

### Get Deal Activity Logs
- **GET** `/api/crm/deals/{dealId}/activity-logs`

### Move Deal to Stage
- **POST** `/api/crm/deals/{id}/move-stage`
- **Body:**
  ```json
  {
    "stage_id": 3
  }
  ```
- **Response:**
  ```json
  {
    "status": "success",
    "message": "Deal moved from Stage 2 to Stage 3",
    "deal_id": 456,
    "old_stage_id": 2,
    "new_stage_id": 3
  }
  ```

### Move Deal with Order (Kanban Style)
- **POST** `/api/crm/deals/move-to-stage`
- **Body:**
  ```json
  {
    "deal_id": 456,
    "stage_id": 3,
    "order": [456, 789, 123]
  }
  ```
- **Response:**
  ```json
  {
    "status": "success",
    "message": "Deal successfully moved."
  }
  ```

## Customers API

### Get All Customers
- **GET** `/api/crm/customers`

### Get Single Customer
- **GET** `/api/crm/customers/{id}`

### Create Customer
- **POST** `/api/crm/customers`
- **Body:**
  ```json
  {
    "name": "Customer name",
    "contact": "************",
    "email": "<EMAIL>",
    "tax_number": "TAX123", // Optional
    "password": "password" // Optional, defaults to "password"
  }
  ```

### Update Customer
- **PUT** `/api/crm/customers/{id}`
- **Body:** Same as create customer

### Delete Customer
- **DELETE** `/api/crm/customers/{id}`

## Pipelines API

### Get All Pipelines
- **GET** `/api/crm/pipelines`

### Create Pipeline
- **POST** `/api/crm/pipelines`
- **Body:**
  ```json
  {
    "name": "Pipeline name" // Max 20 characters
  }
  ```

### Update Pipeline
- **PUT** `/api/crm/pipelines/{id}`
- **Body:** Same as create pipeline

### Delete Pipeline
- **DELETE** `/api/crm/pipelines/{id}`

## Stages API

### Get All Stages
- **GET** `/api/crm/stages`

### Get Stages by Pipeline
- **GET** `/api/crm/pipelines/{pipelineId}/stages`

### Create Stage
- **POST** `/api/crm/stages`
- **Body:**
  ```json
  {
    "name": "Stage name", // Max 20 characters
    "pipeline_id": 1
  }
  ```

## Sources API

### Get All Sources
- **GET** `/api/crm/sources`

### Create Source
- **POST** `/api/crm/sources`
- **Body:**
  ```json
  {
    "name": "Source name" // Max 20 characters
  }
  ```

## Labels API

### Get All Labels
- **GET** `/api/crm/labels`

### Create Label
- **POST** `/api/crm/labels`
- **Body:**
  ```json
  {
    "name": "Label name", // Max 20 characters
    "pipeline_id": 1,
    "color": "#FF0000" // Hex color code
  }
  ```

## Users API

### Get All Users
- **GET** `/api/crm/users`
- **Response:** List of active users for assignment purposes
- **Response Structure:**
  ```json
  {
    "success": true,
    "data": [
      {
        "id": 1,
        "name": "John Smith",
        "email": "<EMAIL>",
        "type": "employee"
      }
    ]
  }
  ```

## Additional SSO Authentication APIs

### SSO Login (Web Interface)
- **GET** `/sso-login?token={sso_token}`
- **Description:** Direct SSO login via URL with token parameter
- **Response:** Redirects to dashboard on success, login page on failure

### Module Integration SSO
- **GET** `/module-integrations/{id}/sso-login`
- **Description:** Generate SSO token and redirect to external module
- **Permissions:** Requires system admin or super admin with module permissions

### API User Sync (Module Integration)
- **POST** `/api/sync-user`
- **PUT** `/api/sync-user`
- **DELETE** `/api/sync-user`
- **Description:** Sync user data between modules

## Permissions

The following permissions are required for move operations:

### Lead Move Permissions
- **move lead:** Required to move leads between stages
- **manage lead:** General lead management permission

### Deal Move Permissions
- **move deal:** Required to move deals between stages
- **manage deal:** General deal management permission

## Error Codes

- **401 Unauthorized:** Invalid or missing SSO token, or insufficient permissions
- **404 Not Found:** Resource not found
- **422 Unprocessable Entity:** Validation errors
- **500 Internal Server Error:** Server error

### SSO Token Error Details

- **Token expired:** SSO token has expired (15 minutes lifetime)
- **Invalid token signature:** SSO token signature is invalid
- **Token not yet valid:** SSO token is not yet valid
- **Invalid token audience:** SSO token is not valid for this application
- **Invalid user:** User associated with SSO token not found

## Testing

Use the provided `test_crm_api.php` script to test all endpoints:

```bash
php test_crm_api.php
```

Make sure to update the configuration variables in the test script before running.

---

# Booking & Appointment API

## Bookings API

### Get All Bookings
- **GET** `/api/booking/bookings`
- **Response:** List of all bookings with their related calendar events

### Get Single Booking
- **GET** `/api/booking/bookings/{id}`
- **Response:** Booking details with related calendar event

### Create Booking
- **POST** `/api/booking/bookings`
- **Body:**
  ```json
  {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890", // Optional
    "event_id": 1,
    "date": "2024-02-15",
    "time": "10:00",
    "custom_fields": ["notes"], // Optional array
    "custom_fields_value": ["Custom notes"] // Optional array
  }
  ```

### Update Booking
- **PUT** `/api/booking/bookings/{id}`
- **Body:** Same as create booking (excluding event_id)

### Delete Booking
- **DELETE** `/api/booking/bookings/{id}`

## Appointment Bookings API

### Get All Appointment Bookings
- **GET** `/api/booking/appointment-bookings`
- **Query Parameters:**
  - `event_id` (optional): Filter by specific event
  - `start_date` & `end_date` (optional): Filter by date range

### Get Single Appointment Booking
- **GET** `/api/booking/appointment-bookings/{id}`

### Create Appointment Booking
- **POST** `/api/booking/appointment-bookings`
- **Body:**
  ```json
  {
    "event_id": 1,
    "event_location": "Online Meeting",
    "event_location_value": "https://meet.google.com/test", // Optional
    "event_date": "2024-02-20",
    "time_zone": "UTC",
    "time_slots": "14:00-14:30"
  }
  ```

### Update Appointment Booking
- **PUT** `/api/booking/appointment-bookings/{id}`
- **Body:** Same as create (excluding event_id)

### Delete Appointment Booking
- **DELETE** `/api/booking/appointment-bookings/{id}`

## Calendar Events API

### Get All Calendar Events
- **GET** `/api/booking/calendar-events`
- **Response:** List of calendar events with bookings and appointment bookings

### Get Single Calendar Event
- **GET** `/api/booking/calendar-events/{id}`
- **Response:** Calendar event with related bookings, appointment bookings, and weekly availability

### Create Calendar Event
- **POST** `/api/booking/calendar-events`
- **Body:**
  ```json
  {
    "title": "Consultation Meeting",
    "start_date": "2024-01-01",
    "end_date": "2024-12-31",
    "duration": 30, // Duration in minutes
    "booking_per_slot": 1, // Optional, defaults to 1
    "minimum_notice": 60, // Optional, minimum notice in minutes
    "description": "Event description", // Optional
    "location": "Online", // Optional
    "meet_link": "https://meet.google.com/test", // Optional
    "physical_address": "123 Main St", // Optional
    "require_name": true, // Optional, defaults to true
    "require_email": true, // Optional, defaults to true
    "require_phone": false, // Optional, defaults to false
    "weekly_availability": { // Optional
      "monday": ["09:00-17:00"],
      "tuesday": ["09:00-17:00"],
      "wednesday": ["09:00-17:00"],
      "thursday": ["09:00-17:00"],
      "friday": ["09:00-17:00"]
    },
    "custom_fields": [], // Optional array
    "date_override": [] // Optional array
  }
  ```

## Testing Booking APIs

Use the provided `test_booking_api.php` script to test all booking endpoints:

```bash
php test_booking_api.php
```
