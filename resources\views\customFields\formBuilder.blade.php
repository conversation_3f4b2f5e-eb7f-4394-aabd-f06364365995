@if($customFields)
    @foreach($customFields as $customField)
        @php
            $fieldName = 'custom_field[' . $customField->id . ']';
            $label = Form::label('custom_field-'.$customField->id, __($customField->name), ['class' => 'form-label']);
        @endphp

        <div class="col-lg-4 col-md-4 col-sm-6 col-12">
            <div class="form-group">
                {!! $label !!}
                <div class="input-group">
                    
                    @switch($customField->type)

                        @case('text')
                            {{ Form::text($fieldName, null, ['class' => 'form-control']) }}
                            @break

                        @case('email')
                            {{ Form::email($fieldName, null, ['class' => 'form-control']) }}
                            @break

                        @case('number')
                            {{ Form::number($fieldName, null, ['class' => 'form-control']) }}
                            @break

                        @case('date')
                            {{ Form::date($fieldName, null, ['class' => 'form-control']) }}
                            @break

                        @case('datetime')
                            {{ Form::datetimeLocal($fieldName, null, ['class' => 'form-control']) }}
                            @break

                        @case('textarea')
                            {{ Form::textarea($fieldName, null, ['class' => 'form-control', 'rows' => 1]) }}
                            @break

                            
                            @case('checkbox')
    @php $options = is_array($customField->options) ? $customField->options : json_decode($customField->options, true); @endphp
    @foreach($options as $option)
        <div class="form-check">
            {{ Form::checkbox($fieldName.'[]', $option, false, ['class' => 'form-check-input', 'id' => $fieldName.'_'.$loop->index]) }}
            {{ Form::label($fieldName.'_'.$loop->index, $option, ['class' => 'form-check-label']) }}
        </div>
    @endforeach
    @break

@case('radio')
    @php $options = is_array($customField->options) ? $customField->options : json_decode($customField->options, true); @endphp
    @foreach($options as $option)
        <div class="form-check">
            {{ Form::radio($fieldName, $option, false, ['class' => 'form-check-input', 'id' => $fieldName.'_'.$loop->index]) }}
            {{ Form::label($fieldName.'_'.$loop->index, $option, ['class' => 'form-check-label']) }}
        </div>
    @endforeach
    @break

@case('select')
    @php $options = is_array($customField->options) ? $customField->options : json_decode($customField->options, true); @endphp
    @php $selectOptions = ['' => 'Select an option']; @endphp
    @if($options)
        @foreach($options as $option)
            @php $selectOptions[$option] = $option; @endphp
        @endforeach
    @endif
    {{ Form::select($fieldName, $selectOptions, null, ['class' => 'form-control']) }}
    @break

@case('multiselect')
    @php $options = is_array($customField->options) ? $customField->options : json_decode($customField->options, true); @endphp
    @php $selectOptions = []; @endphp
    @if($options)
        @foreach($options as $option)
            @php $selectOptions[$option] = $option; @endphp
        @endforeach
    @endif
    {{ Form::select($fieldName.'[]', $selectOptions, null, ['class' => 'form-control', 'multiple' => 'multiple', 'size' => '4']) }}
    <small class="form-text text-muted">{{ __('Hold Ctrl (Cmd on Mac) to select multiple options') }}</small>
    @break

                        @case('link')
                            {{ Form::url($fieldName, null, ['class' => 'form-control']) }}
                            @break

                        @case('color')
                             {{ Form::input('color', $fieldName, '#000000', ['class' => 'form-control form-control-color', 'title' => 'Pick a color']) }}
                            @break

                        @case('file')
                            {{ Form::file($fieldName, ['class' => 'form-control', 'accept' => '*/*']) }}
                            @break

                        @case('file_multiple')
                            {{ Form::file($fieldName.'[]', ['class' => 'form-control', 'multiple' => 'multiple', 'accept' => '*/*']) }}
                            <small class="form-text text-muted">{{ __('You can select multiple files') }}</small>
                            @break

                        @default
                            <span class="text-danger">{{ __('Unknown Field Type') }}</span>
                    @endswitch
                </div>
            </div>
        </div>
    @endforeach
@endif
