@extends('layouts.admin')
@section('content')
<style>
    .modern-card {
        border-radius: 1rem;
        box-shadow: 0 4px 24px rgba(0,0,0,0.08);
        background: #fff;
        padding: 2rem 1.5rem 1.5rem 1.5rem;
        margin-bottom: 2rem;
        transition: box-shadow 0.3s;
    }
    .modern-card:hover {
        box-shadow: 0 8px 32px rgba(0,0,0,0.16);
    }
    .filter-box {
        border: 1px solid #e3e6ef;
        border-radius: 0.7rem;
        background: #f9fafb;
        padding: 0.5rem 1rem;
        display: flex;
        align-items: center;
        height: 48px;
        margin-bottom: 1rem;
    }
    .filter-box input,
    .filter-box select {
        border: none;
        background: transparent;
        outline: none;
        width: 100%;
        font-size: 1rem;
        color: #222;
    }
    .filter-box input:focus,
    .filter-box select:focus {
        box-shadow: none;
    }
    .filter-icon {
        color: #adb5bd;
        font-size: 1.2em;
        margin-right: 0.5rem;
    }
    .animated-row {
        animation: fadeInUp 0.6s;
    }
    @keyframes fadeInUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    .priority-badge {
        border-radius: 0.5rem;
        padding: 0.25em 0.75em;
        font-size: 0.95em;
        font-weight: 500;
        color: #fff;
        display: inline-block;
        margin-right: 0.25em;
    }
    .priority-critical { background: #e74c3c; }
    .priority-high { background: #f39c12; }
    .priority-medium { background: #3498db; }
    .priority-low { background: #27ae60; }
    .filter-animate { transition: box-shadow 0.3s, border-color 0.3s; }
    .filter-animate:focus { box-shadow: 0 0 0 0.2rem #a5d8ff; border-color: #339af0; }
    .search-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #adb5bd;
        font-size: 1.2em;
    }
    .spinner-overlay {
        position: fixed;
        top: 0; left: 0; width: 100vw; height: 100vh;
        background: rgba(255,255,255,0.7);
        z-index: 9999;
        display: flex; align-items: center; justify-content: center;
        display: none;
    }
    @media (max-width: 767px) {
        .modern-card { padding: 1rem 0.5rem; }
        .table-responsive { font-size: 0.95em; }
    }
</style>
<div class="container-fluid">
    <div class="spinner-overlay" id="spinnerOverlay" style="display:none">
        <div class="spinner-border text-primary" style="width: 4rem; height: 4rem;" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
    <form method="GET" action="" class="mb-0" id="filterForm">
        <div class="modern-card">
            <div class="row g-3 align-items-end">
                <div class="col-12 col-md-3">
                    <div class="filter-box">
                        <span class="filter-icon"><i class="ti ti-search"></i></span>
                        <input type="text" name="search" placeholder="Search Task" value="{{ request('search') }}">
                    </div>
                </div>
                <div class="col-12 col-md-3">
                    <div class="filter-box">
                        <span class="filter-icon"><i class="ti ti-calendar"></i></span>
                        <input type="text" name="date_range" id="dateRangeInput" placeholder="Task Creation Date Range" value="{{ request('date_range') }}">
                    </div>
                </div>
                <div class="col-12 col-md-2">
                    <div class="filter-box">
                        <select name="client_id">
                            <option value="">Client</option>
                            @foreach($clients as $id => $name)
                                <option value="{{ $id }}" {{ request('client_id') == $id ? 'selected' : '' }}>{{ $name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-12 col-md-2">
                    <div class="filter-box">
                        <select name="category">
                            <option value="">Category</option>
                            @foreach($categories as $cat)
                                <option value="{{ $cat }}" {{ request('category') == $cat ? 'selected' : '' }}>{{ $cat }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-12 col-md-2 d-flex gap-2">
                    <button type="submit" class="btn btn-primary w-100">Filter</button>
                    <a href="?" class="btn btn-outline-secondary w-100">Clear</a>
                </div>
            </div>
            <div class="row g-3 mt-2">
                <div class="col-12 col-md-2">
                    <div class="filter-box">
                        <select name="assigned_by">
                            <option value="">Assigned By</option>
                            @foreach($users as $id => $name)
                                <option value="{{ $id }}" {{ request('assigned_by') == $id ? 'selected' : '' }}>{{ $name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-12 col-md-2">
                    <div class="filter-box">
                        <select name="assigned_to">
                            <option value="">Assigned To</option>
                            @foreach($users as $id => $name)
                                <option value="{{ $id }}" {{ request('assigned_to') == $id ? 'selected' : '' }}>{{ $name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-12 col-md-2">
                    <div class="filter-box">
                        <select name="priority">
                            <option value="">Priority</option>
                            @foreach($priorities as $key => $label)
                                <option value="{{ $key }}" {{ request('priority') == $key ? 'selected' : '' }}>{{ $label }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-12 col-md-2">
                    <div class="filter-box">
                        <select name="status">
                            <option value="">Status</option>
                            @foreach($statuses as $id => $name)
                                <option value="{{ $id }}" {{ request('status') == $id ? 'selected' : '' }}>{{ $name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-12 col-md-2">
                    <button type="submit" class="btn btn-primary w-100 mb-2">
                        <i class="ti ti-filter me-1"></i> Filter
                    </button>
                </div>
                <div class="col-12 col-md-2">
                    <a href="{{ route('projects.team-performance') }}" class="btn btn-outline-secondary w-100 mb-2">
                        <i class="ti ti-refresh me-1"></i> Reset
                    </a>
                </div>
            </div>
        </div>
    </form>
    <div class="row mb-2">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <span class="badge bg-success p-2 fs-6">(Total Records: {{ $tasks->total() }} / {{ $tasks->perPage() }})</span>
            <div class="d-flex gap-2">
                <form method="GET" action="{{ route('projects.team-performance.export') }}" class="d-inline">
                    @foreach(request()->all() as $key => $value)
                        @if($key !== 'page')
                            <input type="hidden" name="{{ $key }}" value="{{ $value }}">
                        @endif
                    @endforeach
                    <button type="submit" class="btn btn-sm btn-secondary" data-bs-toggle="tooltip" title="{{ __('Export Excel') }}">
                        <i class="ti ti-file-export"></i> Excel
                    </button>
                </form>
                <form method="GET" action="{{ route('projects.team-performance.pdf') }}" class="d-inline" target="_blank">
                    @foreach(request()->all() as $key => $value)
                        @if($key !== 'page')
                            <input type="hidden" name="{{ $key }}" value="{{ $value }}">
                        @endif
                    @endforeach
                    <button type="submit" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="{{ __('Export PDF') }}">
                        <i class="ti ti-download"></i> PDF
                    </button>
                </form>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="modern-card">
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="table-light">
                            <tr>
                                <th>#</th>
                                <th>Task Name</th>
                                <th>Assigned To</th>
                                <th>Assigned By</th>
                                <th>Client Name</th>
                                <th>Priority</th>
                                <th>Status</th>
                                <th>Start Date</th>
                                <th>Due Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($tasks as $i => $task)
                                <tr class="animated-row">
                                    <td>{{ $tasks->firstItem() + $i }}</td>
                                    <td><strong>{{ $task->name }}</strong></td>
                                    <td>
                                        @foreach($task->users() as $user)
                                            <span class="me-1"><i class="ti ti-user"></i> {{ $user->name }}</span>
                                        @endforeach
                                    </td>
                                    <td>{{ optional($task->createdBy)->name }}</td>
                                    <td>{{ optional($task->project->client)->name }}</td>
                                    <td>
                                        @php $p = $task->priority; @endphp
                                        <span class="priority-badge priority-{{ $p }}">{{ $priorities[$p] ?? $p }}</span>
                                    </td>
                                    <td>{{ optional($task->stage)->name }}</td>
                                    <td>{{ $task->start_date }}</td>
                                    <td>{{ $task->end_date }}</td>
                                </tr>
                            @empty
                                <tr><td colspan="9" class="text-center">No tasks found.</td></tr>
                            @endforelse
                        </tbody>
                    </table>
                    <div class="d-flex justify-content-center">
                        {{ $tasks->appends(request()->except('page'))->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    // Show spinner on filter submit
    document.getElementById('filterForm').addEventListener('submit', function() {
        document.getElementById('spinnerOverlay').style.display = 'flex';
    });
    // Optionally, add a date picker for date_range
    if (window.flatpickr) {
        flatpickr('#dateRangeInput', { mode: 'range', dateFormat: 'd/m/Y' });
    }
</script>
@endsection 