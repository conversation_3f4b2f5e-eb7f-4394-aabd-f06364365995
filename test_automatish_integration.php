<?php

/**
 * Manual test script for Automatish integration
 * Run this script to test the Automatish integration manually
 * 
 * Usage: php test_automatish_integration.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\ModuleIntegration;
use App\Http\Controllers\ModuleIntegrationController;
use Illuminate\Support\Facades\Log;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Automatish Integration Test ===\n\n";

// 1. Check if Automatish module exists and is enabled
echo "1. Checking Automatish module configuration...\n";
$automatishModule = ModuleIntegration::where('name', 'Automatish')->first();

if (!$automatishModule) {
    echo "❌ ERROR: Automatish module not found in module_integrations table\n";
    echo "Please add Automatish module to the database first.\n";
    exit(1);
}

echo "✅ Automatish module found\n";
echo "   - Name: {$automatishModule->name}\n";
echo "   - Base URL: {$automatishModule->base_url}\n";
echo "   - Sync Endpoint: {$automatishModule->sync_endpoint}\n";
echo "   - SSO Endpoint: {$automatishModule->sso_endpoint}\n";
echo "   - Enabled: " . ($automatishModule->enabled ? 'Yes' : 'No') . "\n";
echo "   - API Token: " . (strlen($automatishModule->api_token) > 0 ? 'Set' : 'Not Set') . "\n\n";

if (!$automatishModule->enabled) {
    echo "❌ WARNING: Automatish module is disabled\n";
    echo "Enable it in the module integration settings.\n\n";
}

// 2. Test the sync method
echo "2. Testing Automatish sync method...\n";

// Create a test user (or use existing)
$testUser = User::where('email', '<EMAIL>')->first();
if (!$testUser) {
    echo "Creating test user...\n";
    $testUser = new User();
    $testUser->name = 'Test User';
    $testUser->email = '<EMAIL>';
    $testUser->password = bcrypt('testpassword123');
    $testUser->type = 'company';
    $testUser->created_by = 1;
    $testUser->save();
    echo "✅ Test user created\n";
} else {
    echo "✅ Using existing test user\n";
}

echo "   - User ID: {$testUser->id}\n";
echo "   - Name: {$testUser->name}\n";
echo "   - Email: {$testUser->email}\n\n";

// Test the sync
echo "3. Testing sync to Automatish...\n";
$controller = new ModuleIntegrationController();

try {
    $result = $controller->syncUserToAutomatish($testUser, 'testpassword123');
    
    if ($result) {
        echo "✅ Sync successful!\n";
        echo "User should now be created in Automatish with:\n";
        echo "   - fullName: {$testUser->name}\n";
        echo "   - email: {$testUser->email}\n";
        echo "   - password: testpassword123\n\n";
    } else {
        echo "❌ Sync failed\n";
        echo "Check the logs for more details.\n\n";
    }
} catch (Exception $e) {
    echo "❌ Exception during sync: " . $e->getMessage() . "\n\n";
}

// 4. Check sidebar integration
echo "4. Sidebar integration check...\n";
if ($automatishModule->enabled && $automatishModule->sso_endpoint) {
    echo "✅ Automatish should appear in the sidebar for all users\n";
    echo "   - Icon: Robot icon (ti-robot)\n";
    echo "   - Link: SSO login to Automatish\n";
    echo "   - Route: module-integration.sso-login with module ID {$automatishModule->id}\n\n";
} else {
    echo "❌ Automatish will not appear in sidebar\n";
    echo "   - Module enabled: " . ($automatishModule->enabled ? 'Yes' : 'No') . "\n";
    echo "   - SSO endpoint set: " . ($automatishModule->sso_endpoint ? 'Yes' : 'No') . "\n\n";
}

echo "=== Test Complete ===\n";
echo "Next steps:\n";
echo "1. Create a new user through the CRM interface\n";
echo "2. Check if the user appears in Automatish\n";
echo "3. Try clicking the Automatish link in the sidebar\n";
echo "4. Verify SSO login works\n";
