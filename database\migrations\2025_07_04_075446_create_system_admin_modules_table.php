<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('system_admin_modules', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });

        Schema::create('super_admin_module_permissions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('super_admin_id');
            $table->unsignedBigInteger('module_id');
            $table->boolean('has_access')->default(false);
            $table->json('permissions')->nullable(); // Store specific permissions for the module
            $table->unsignedBigInteger('granted_by'); // System admin who granted the permission
            $table->timestamps();

            $table->foreign('super_admin_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('module_id')->references('id')->on('system_admin_modules')->onDelete('cascade');
            $table->foreign('granted_by')->references('id')->on('users')->onDelete('cascade');
            $table->unique(['super_admin_id', 'module_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('super_admin_module_permissions');
        Schema::dropIfExists('system_admin_modules');
    }
};
