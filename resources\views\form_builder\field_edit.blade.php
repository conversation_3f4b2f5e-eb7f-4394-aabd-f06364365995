{{ Form::model($form_field, array('route' => array('form.field.update', $form->id, $form_field->id), 'method' => 'post', 'class'=>'needs-validation', 'novalidate')) }}
<div class="modal-body">
    <div class="row" id="frm_field_data">
        <div class="col-12 form-group">
            {{ Form::label('name', __('Question Name'),['class'=>'form-label']) }}<x-required></x-required>
            {{ Form::text('name', null, array('class' => 'form-control','required'=>'required', 'placeholder'=>__('Enter Question Name'))) }}
        </div>
        <div class="col-12 form-group">
            {{ Form::label('type', __('Type'),['class'=>'form-label']) }}<x-required></x-required>
            {{ Form::select('type', $types, null, array('class' => 'form-control select2 field-type-select','id'=>'field-type-edit','required'=>'required')) }}
        </div>

        <div class="col-12 form-group">
            <div class="form-check form-switch">
                {{ Form::checkbox('required', 1, $form_field->required == 1, ['class' => 'form-check-input', 'id' => 'required-toggle-edit']) }}
                {{ Form::label('required-toggle-edit', __('Required Field'), ['class' => 'form-check-label']) }}
            </div>
        </div>

        {{-- Dynamic Options Input --}}
        <div class="col-12 form-group options-container" style="display:none;">
            {{ Form::label('options[]', __('Options (for checkbox/radio/select/multiselect)'), ['class' => 'form-label']) }}
            <div class="options-list">
                @php
                    $fieldOptions = $form_field->options ?? [];
                    $fieldOptions = is_array($fieldOptions) ? $fieldOptions : [];
                @endphp
                @if(!empty($fieldOptions) && count($fieldOptions) > 0)
                    @foreach($fieldOptions as $option)
                        <div class="d-flex mb-2">
                            {{ Form::text('options[]', is_string($option) ? $option : '', ['class' => 'form-control me-2', 'placeholder' => 'Option']) }}
                            <button type="button" class="btn btn-sm btn-danger remove-option">-</button>
                        </div>
                    @endforeach
                @else
                    <div class="d-flex mb-2">
                        {{ Form::text('options[]', '', ['class' => 'form-control me-2', 'placeholder' => 'Option 1']) }}
                        <button type="button" class="btn btn-sm btn-success add-option">+</button>
                    </div>
                @endif
            </div>
            @if(!empty($fieldOptions) && count($fieldOptions) > 0)
                <button type="button" class="btn btn-sm btn-success add-option mt-2">Add Option</button>
            @endif
        </div>
    </div>
</div>
<div class="modal-footer">
    <input type="button" value="{{__('Cancel')}}" class="btn  btn-secondary" data-bs-dismiss="modal">
    <input type="submit" value="{{__('Update')}}" class="btn  btn-primary">
</div>
{{Form::close()}}

<script>
$(document).ready(function() {
    console.log('Form field edit script loaded');

    // Handle field type change for form builder edit
    $(document).on('change', '#commonModal .field-type-select, #commonModal #field-type-edit', function() {
        const selected = $(this).val();
        const optionsContainer = $('#commonModal .options-container');

        console.log('Field type changed to:', selected);

        if (selected === 'checkbox' || selected === 'radio' || selected === 'select' || selected === 'multiselect') {
            optionsContainer.show();
            console.log('Showing options container for form builder edit');
        } else {
            optionsContainer.hide();
            console.log('Hiding options container for form builder edit');
        }
    });

    // Handle add/remove option buttons for form builder edit
    $(document).on('click', '#commonModal .add-option', function(e) {
        e.preventDefault();
        console.log('Add option clicked in form builder edit');
        const optionsList = $('#commonModal .options-list');
        const newOption = `
            <div class="d-flex mb-2">
                <input type="text" name="options[]" class="form-control me-2" placeholder="Option">
                <button type="button" class="btn btn-sm btn-danger remove-option">-</button>
            </div>
        `;
        optionsList.append(newOption);
    });

    $(document).on('click', '#commonModal .remove-option', function(e) {
        e.preventDefault();
        console.log('Remove option clicked in form builder edit');
        $(this).closest('.d-flex').remove();
    });

    // Initialize when modal is shown for form builder edit
    $(document).on('shown.bs.modal', '#commonModal', function() {
        console.log('Modal shown event triggered');
        // Check if this is form builder edit modal by looking for field-type-select
        const typeSelect = $('#commonModal .field-type-select, #commonModal #field-type-edit');
        if (typeSelect.length) {
            console.log('Form Builder edit modal shown, initializing form. Current value:', typeSelect.val());
            // Small delay to ensure DOM is ready
            setTimeout(function() {
                typeSelect.trigger('change');
            }, 200);
        } else {
            console.log('No field type select found in modal');
        }
    });

    // Also trigger when modal content is loaded
    $(document).ajaxComplete(function(event, xhr, settings) {
        if (settings.url && settings.url.includes('field') && settings.url.includes('edit')) {
            console.log('Field edit AJAX completed');
            setTimeout(function() {
                const typeSelect = $('#commonModal .field-type-select, #commonModal #field-type-edit');
                if (typeSelect.length) {
                    console.log('Triggering change after AJAX complete. Current value:', typeSelect.val());
                    typeSelect.trigger('change');
                }
            }, 300);
        }
    });
});
</script>
