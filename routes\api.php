<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ApiController;
use App\Http\Controllers\CrmApiController;
use App\Http\Controllers\BookingApiController;

use App\Http\Controllers\PipelineController;
/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::post('login', [ApiController::class, 'login']);

// Public API endpoints - accessible without authentication for testing
Route::get('sso/contacts/list', [ApiController::class, 'getSsoContacts']);
Route::get('test/projects', [ApiController::class, 'getPublicProjects']);
Route::get('test/users', [ApiController::class, 'getPublicUsers']);
Route::get('test/status', [ApiController::class, 'getApiStatus']);

// Contact List API with JWT Token Authentication
Route::get('contact-list', [ApiController::class, 'getContactList']);

Route::group(['middleware' => ['auth:sanctum']], function () {
    Route::post('logout', [ApiController::class, 'logout']);
    Route::get('get-projects', [ApiController::class, 'getProjects']);
    Route::get('get-pipelines', [ApiController::class, 'getPipelines']);
    Route::get('test/pipelines', [ApiController::class, 'getPublicPipelines']); // Now requires authentication
    Route::apiResource('pipelines', PipelineController::class);
    Route::post('add-tracker', [ApiController::class, 'addTracker']);
    Route::post('stop-tracker', [ApiController::class, 'stopTracker']);
    Route::post('upload-photos', [ApiController::class, 'uploadImage']);
    Route::post('generate-sso-token', [ApiController::class, 'generateSsoToken']);
});

// ==================== CRM API ROUTES WITH SSO TOKEN AUTHENTICATION ====================
Route::group(['prefix' => 'crm', 'middleware' => ['sso.auth']], function () {

    // ==================== LEADS API ====================
    Route::get('leads', [CrmApiController::class, 'getLeads']);
    Route::get('leads/{id}', [CrmApiController::class, 'getLead']);
    Route::post('leads', [CrmApiController::class, 'createLead']);
    Route::put('leads/{id}', [CrmApiController::class, 'updateLead']);
    Route::delete('leads/{id}', [CrmApiController::class, 'deleteLead']);

    // Lead Tasks
    Route::get('leads/{leadId}/tasks', [CrmApiController::class, 'getLeadTasks']);

    // Lead Activity Logs
    Route::get('leads/{leadId}/activity-logs', [CrmApiController::class, 'getLeadActivityLogs']);

    // Move Lead APIs
    Route::post('leads/{id}/move-stage', [CrmApiController::class, 'moveLeadToStage']);
    Route::post('leads/move-to-stage', [CrmApiController::class, 'moveLeadWithOrder']);

    // Lead Assignment APIs
    Route::post('leads/{id}/assign-users', [CrmApiController::class, 'assignLeadToUsers']);
    Route::delete('leads/{id}/unassign-users', [CrmApiController::class, 'unassignLeadFromUsers']);

    // Lead Pipeline APIs
    Route::post('leads/{id}/move-pipeline', [CrmApiController::class, 'moveLeadToPipeline']);

    // Lead Labels/Tags APIs
    Route::post('leads/{id}/add-labels', [CrmApiController::class, 'addLeadLabels']);
    Route::delete('leads/{id}/remove-labels', [CrmApiController::class, 'removeLeadLabels']);

    // Lead Export APIs
    Route::get('leads/export', [CrmApiController::class, 'exportLeads']);
    Route::get('leads/{id}/export', [CrmApiController::class, 'exportSingleLead']);

    // Lead Custom Fields APIs
    Route::get('leads/{id}/custom-fields', [CrmApiController::class, 'getLeadCustomFields']);
    Route::post('leads/{id}/custom-fields', [CrmApiController::class, 'updateLeadCustomFields']);

    // ==================== DEALS API ====================
    Route::get('deals', [CrmApiController::class, 'getDeals']);
    Route::get('deals/{id}', [CrmApiController::class, 'getDeal']);
    Route::post('deals', [CrmApiController::class, 'createDeal']);
    Route::put('deals/{id}', [CrmApiController::class, 'updateDeal']);
    Route::delete('deals/{id}', [CrmApiController::class, 'deleteDeal']);

    // Deal Tasks
    Route::get('deals/{dealId}/tasks', [CrmApiController::class, 'getDealTasks']);

    // Deal Activity Logs
    Route::get('deals/{dealId}/activity-logs', [CrmApiController::class, 'getDealActivityLogs']);

    // Move Deal APIs
    Route::post('deals/{id}/move-stage', [CrmApiController::class, 'moveDealToStage']);
    Route::post('deals/move-to-stage', [CrmApiController::class, 'moveDealWithOrder']);

    // Deal Assignment APIs
    Route::post('deals/{id}/assign-users', [CrmApiController::class, 'assignDealToUsers']);
    Route::delete('deals/{id}/unassign-users', [CrmApiController::class, 'unassignDealFromUsers']);

    // Deal Pipeline APIs
    Route::post('deals/{id}/move-pipeline', [CrmApiController::class, 'moveDealToPipeline']);

    // Deal Labels/Tags APIs
    Route::post('deals/{id}/add-labels', [CrmApiController::class, 'addDealLabels']);
    Route::delete('deals/{id}/remove-labels', [CrmApiController::class, 'removeDealLabels']);

    // Create Deal from Lead
    Route::post('deals/from-lead/{leadId}', [CrmApiController::class, 'createDealFromLead']);

    // ==================== CUSTOMERS API ====================
    Route::get('customers', [CrmApiController::class, 'getCustomers']);
    Route::get('customers/{id}', [CrmApiController::class, 'getCustomer']);
    Route::post('customers', [CrmApiController::class, 'createCustomer']);
    Route::put('customers/{id}', [CrmApiController::class, 'updateCustomer']);
    Route::delete('customers/{id}', [CrmApiController::class, 'deleteCustomer']);

    // ==================== PIPELINES API ====================
    Route::get('pipelines', [CrmApiController::class, 'getPipelines']);
    Route::post('pipelines', [CrmApiController::class, 'createPipeline']);
    Route::put('pipelines/{id}', [CrmApiController::class, 'updatePipeline']);
    Route::delete('pipelines/{id}', [CrmApiController::class, 'deletePipeline']);

    // ==================== STAGES API ====================
    Route::get('stages', [CrmApiController::class, 'getStages']);
    Route::get('pipelines/{pipelineId}/stages', [CrmApiController::class, 'getStagesByPipeline']);
    Route::post('stages', [CrmApiController::class, 'createStage']);

    // ==================== SOURCES API ====================
    Route::get('sources', [CrmApiController::class, 'getSources']);
    Route::post('sources', [CrmApiController::class, 'createSource']);

    // ==================== LABELS API ====================
    Route::get('labels', [CrmApiController::class, 'getLabels']);
    Route::post('labels', [CrmApiController::class, 'createLabel']);

    // ==================== USERS API ====================
    Route::get('users', [CrmApiController::class, 'getUsers']);

});

// ==================== BOOKING & APPOINTMENT API ROUTES WITH SSO TOKEN AUTHENTICATION ====================
Route::group(['prefix' => 'booking', 'middleware' => ['sso.auth']], function () {

    // ==================== BOOKINGS API ====================
    Route::get('bookings', [BookingApiController::class, 'getBookings']);
    Route::get('bookings/{id}', [BookingApiController::class, 'getBooking']);
    Route::post('bookings', [BookingApiController::class, 'createBooking']);
    Route::put('bookings/{id}', [BookingApiController::class, 'updateBooking']);
    Route::delete('bookings/{id}', [BookingApiController::class, 'deleteBooking']);

    // ==================== APPOINTMENT BOOKINGS API ====================
    Route::get('appointment-bookings', [BookingApiController::class, 'getAppointmentBookings']);
    Route::get('appointment-bookings/{id}', [BookingApiController::class, 'getAppointmentBooking']);
    Route::post('appointment-bookings', [BookingApiController::class, 'createAppointmentBooking']);
    Route::put('appointment-bookings/{id}', [BookingApiController::class, 'updateAppointmentBooking']);
    Route::delete('appointment-bookings/{id}', [BookingApiController::class, 'deleteAppointmentBooking']);

    // ==================== CALENDAR EVENTS API ====================
    Route::get('calendar-events', [BookingApiController::class, 'getCalendarEvents']);
    Route::get('calendar-events/{id}', [BookingApiController::class, 'getCalendarEvent']);
    Route::post('calendar-events', [BookingApiController::class, 'createCalendarEvent']);
    // Add update and delete for events
    Route::put('calendar-events/{id}', [BookingApiController::class, 'updateCalendarEvent']);
    Route::delete('calendar-events/{id}', [BookingApiController::class, 'deleteCalendarEvent']);

    // ==================== APPOINTMENT ACTIONS ====================
    // Set appointment status (confirm, cancel, complete, reschedule)
    Route::post('appointment-bookings/{id}/status', [BookingApiController::class, 'setAppointmentStatus']);
    // Generate appointment link
    Route::post('appointment-bookings/{id}/generate-link', [BookingApiController::class, 'generateAppointmentLink']);
    // Send calendar invite
    Route::post('appointment-bookings/{id}/send-invite', [BookingApiController::class, 'sendCalendarInvite']);
    // Update appointment location
    Route::post('appointment-bookings/{id}/update-location', [BookingApiController::class, 'updateAppointmentLocation']);
    // Reschedule appointment
    Route::post('appointment-bookings/{id}/reschedule', [BookingApiController::class, 'rescheduleAppointment']);
    // Explicit cancel endpoint (optional, can use status as well)
    Route::post('appointment-bookings/{id}/cancel', [BookingApiController::class, 'cancelAppointment']);

});

