<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PricingPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'plan_type',
        'description',
        'price',
        'status',
        'module_permissions',
        'max_users',
        'max_customers',
        'max_vendors',
        'max_clients',
        'storage_limit',
        'duration',
        'sort_order',
    ];

    protected $casts = [
        'module_permissions' => 'array',
        'price' => 'decimal:2',
        'storage_limit' => 'float',
    ];

    /**
     * Available modules for pricing plans
     */
    public static function getAvailableModules()
    {
        return [
            'crm' => [
                'name' => 'CRM System',
                'permissions' => [
                    'view crm dashboard',
                    'manage lead',
                    'create lead',
                    'edit lead',
                    'delete lead',
                    'view lead',
                    'manage deal',
                    'create deal',
                    'edit deal',
                    'delete deal',
                    'view deal',
                    'manage form builder',
                    'create form builder',
                    'edit form builder',
                    'delete form builder',
                    'view form builder',
                    'manage contract',
                    'create contract',
                    'edit contract',
                    'delete contract',
                    'view contract',
                    'manage pipeline',
                    'create pipeline',
                    'edit pipeline',
                    'delete pipeline',
                    'view pipeline',
                    'manage stage',
                    'create stage',
                    'edit stage',
                    'delete stage',
                    'view stage',
                    'manage source',
                    'create source',
                    'edit source',
                    'delete source',
                    'view source',
                    'manage label',
                    'create label',
                    'edit label',
                    'delete label',
                    'view label',
                ]
            ],
            'hrm' => [
                'name' => 'HRM System',
                'permissions' => [
                    'view hrm dashboard',
                    'manage employee',
                    'create employee',
                    'edit employee',
                    'delete employee',
                    'view employee',
                    'manage set salary',
                    'create set salary',
                    'edit set salary',
                    'delete set salary',
                    'view set salary',
                    'manage pay slip',
                    'create pay slip',
                    'edit pay slip',
                    'delete pay slip',
                    'view pay slip',
                    'manage leave',
                    'create leave',
                    'edit leave',
                    'delete leave',
                    'view leave',
                    'manage attendance',
                    'create attendance',
                    'edit attendance',
                    'delete attendance',
                    'view attendance',
                    'manage training',
                    'create training',
                    'edit training',
                    'delete training',
                    'view training',
                    'manage award',
                    'create award',
                    'edit award',
                    'delete award',
                    'view award',
                    'manage branch',
                    'create branch',
                    'edit branch',
                    'delete branch',
                    'view branch',
                    'manage department',
                    'create department',
                    'edit department',
                    'delete department',
                    'view department',
                    'manage designation',
                    'create designation',
                    'edit designation',
                    'delete designation',
                    'view designation',
                    'manage document type',
                    'create document type',
                    'edit document type',
                    'delete document type',
                    'view document type',
                ]
            ],
            'account' => [
                'name' => 'Accounting System',
                'permissions' => [
                    'view account dashboard',
                    'manage customer',
                    'create customer',
                    'edit customer',
                    'delete customer',
                    'view customer',
                    'manage vender',
                    'create vender',
                    'edit vender',
                    'delete vender',
                    'view vender',
                    'manage invoice',
                    'create invoice',
                    'edit invoice',
                    'delete invoice',
                    'view invoice',
                    'manage bill',
                    'create bill',
                    'edit bill',
                    'delete bill',
                    'view bill',
                    'manage revenue',
                    'create revenue',
                    'edit revenue',
                    'delete revenue',
                    'view revenue',
                    'manage payment',
                    'create payment',
                    'edit payment',
                    'delete payment',
                    'view payment',
                    'manage proposal',
                    'create proposal',
                    'edit proposal',
                    'delete proposal',
                    'view proposal',
                    'manage goal',
                    'create goal',
                    'edit goal',
                    'delete goal',
                    'view goal',
                    'manage credit note',
                    'create credit note',
                    'edit credit note',
                    'delete credit note',
                    'view credit note',
                    'manage debit note',
                    'create debit note',
                    'edit debit note',
                    'delete debit note',
                    'view debit note',
                    'manage bank account',
                    'create bank account',
                    'edit bank account',
                    'delete bank account',
                    'view bank account',
                    'manage bank transfer',
                    'create bank transfer',
                    'edit bank transfer',
                    'delete bank transfer',
                    'view bank transfer',
                    'manage transaction',
                    'create transaction',
                    'edit transaction',
                    'delete transaction',
                    'view transaction',
                    'manage chart of account',
                    'create chart of account',
                    'edit chart of account',
                    'delete chart of account',
                    'view chart of account',
                    'manage journal entry',
                    'create journal entry',
                    'edit journal entry',
                    'delete journal entry',
                    'view journal entry',
                    'manage assets',
                    'create assets',
                    'edit assets',
                    'delete assets',
                    'view assets',
                    'manage constant custom field',
                    'create constant custom field',
                    'edit constant custom field',
                    'delete constant custom field',
                    'view constant custom field',
                    'manage report',
                    'view report',
                ]
            ],
            'project' => [
                'name' => 'Project System',
                'permissions' => [
                    'view project dashboard',
                    'manage project',
                    'create project',
                    'edit project',
                    'delete project',
                    'view project',
                    'manage project task',
                    'create project task',
                    'edit project task',
                    'delete project task',
                    'view project task',
                    'manage timesheet',
                    'create timesheet',
                    'edit timesheet',
                    'delete timesheet',
                    'view timesheet',
                    'manage bug report',
                    'create bug report',
                    'edit bug report',
                    'delete bug report',
                    'view bug report',
                    'manage milestone',
                    'create milestone',
                    'edit milestone',
                    'delete milestone',
                    'view milestone',
                    'manage project stage',
                    'create project stage',
                    'edit project stage',
                    'delete project stage',
                    'view project stage',
                    'manage project task stage',
                    'create project task stage',
                    'edit project task stage',
                    'delete project task stage',
                    'view project task stage',
                    'manage project expense',
                    'create project expense',
                    'edit project expense',
                    'delete project expense',
                    'view project expense',
                    'manage activity',
                    'create activity',
                    'edit activity',
                    'delete activity',
                    'view activity',
                    'manage bug status',
                    'create bug status',
                    'edit bug status',
                    'delete bug status',
                    'view bug status',
                ]
            ],
            'pos' => [
                'name' => 'POS System',
                'permissions' => [
                    'view pos dashboard',
                    'manage warehouse',
                    'create warehouse',
                    'edit warehouse',
                    'delete warehouse',
                    'view warehouse',
                    'manage purchase',
                    'create purchase',
                    'edit purchase',
                    'delete purchase',
                    'view purchase',
                    'manage quotation',
                    'create quotation',
                    'edit quotation',
                    'delete quotation',
                    'view quotation',
                    'manage pos',
                    'create pos',
                    'edit pos',
                    'delete pos',
                    'view pos',
                    'create barcode',
                    'manage product',
                    'create product',
                    'edit product',
                    'delete product',
                    'view product',
                    'manage product category',
                    'create product category',
                    'edit product category',
                    'delete product category',
                    'view product category',
                    'manage product unit',
                    'create product unit',
                    'edit product unit',
                    'delete product unit',
                    'view product unit',
                ]
            ],
            'support' => [
                'name' => 'Support System',
                'permissions' => [
                    'view support dashboard',
                    'manage support',
                    'create support',
                    'edit support',
                    'delete support',
                    'view support',
                    'reply support',
                ]
            ],
            'user_management' => [
                'name' => 'User Management',
                'permissions' => [
                    'manage user',
                    'create user',
                    'edit user',
                    'delete user',
                    'view user',
                    'manage client',
                    'create client',
                    'edit client',
                    'delete client',
                    'view client',
                ]
            ],
            'booking' => [
                'name' => 'Booking System',
                'permissions' => [
                    'view booking dashboard',
                    'manage booking',
                    'create booking',
                    'edit booking',
                    'delete booking',
                    'view booking',
                    'show booking',
                    'manage appointment',
                    'create appointment',
                    'edit appointment',
                    'delete appointment',
                    'view appointment',
                    'show appointment',
                    'manage appointment booking',
                    'create appointment booking',
                    'edit appointment booking',
                    'delete appointment booking',
                    'view appointment booking',
                    'show appointment booking',
                    'manage calendar event',
                    'create calendar event',
                    'edit calendar event',
                    'delete calendar event',
                    'view calendar event',
                    'show calendar event',
                ]
            ],
            'omx_flow' => [
                'name' => 'OMX Flow Integration',
                'permissions' => [
                    'access omx flow',
                    'whatsapp_flows',
                    'whatsapp_orders',
                    'campaigns',
                    'templates',
                    'chatbot',
                ]
            ],
            'personal_tasks' => [
                'name' => 'Personal Tasks',
                'permissions' => [
                    'manage personal task',
                    'create personal task',
                    'edit personal task',
                    'delete personal task',
                    'view personal task',
                    'create personal task comment',
                    'edit personal task comment',
                    'delete personal task comment',
                    'create personal task file',
                    'delete personal task file',
                    'create personal task checklist',
                    'edit personal task checklist',
                    'delete personal task checklist',
                    'manage personal task time tracking',
                ]
            ],
            'automatish' => [
                'name' => 'Automatish',
                'permissions' => [
                    'access automatish',
                ]
            ],
        ];
    }

    /**
     * Get status options
     */
    public static function getStatusOptions()
    {
        return [
            'active' => 'Active',
            'inactive' => 'Inactive',
        ];
    }

    /**
     * Get duration options
     */
    public static function getDurationOptions()
    {
        return [
            'monthly' => 'Monthly',
            'yearly' => 'Yearly',
            'lifetime' => 'Lifetime',
        ];
    }

    /**
     * Get plan type options
     */
    public static function getPlanTypeOptions()
    {
        return [
            'subaccount' => 'Sub Account',
            'white_label' => 'White Label',
        ];
    }

    /**
     * Scope for active plans
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }


    /**
     * Get formatted price
     */
    public function getFormattedPriceAttribute()
    {
        return '$' . number_format($this->price, 2);
    }

    /**
     * Check if plan has specific module
     */
    public function hasModule($module)
    {
        return isset($this->module_permissions[$module]);
    }

    /**
     * Check if plan has specific permission
     */
    public function hasPermission($module, $permission)
    {
        return isset($this->module_permissions[$module]) && 
               in_array($permission, $this->module_permissions[$module]);
    }
}
