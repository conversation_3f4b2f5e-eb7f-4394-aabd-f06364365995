<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCommentReactionToLeadCommentsTable extends Migration
{
    public function up()
    {
        Schema::table('lead_comments', function (Blueprint $table) {
            $table->json('comment_reaction')->nullable()->after('comment');
        });
    }

    public function down()
    {
        Schema::table('lead_comments', function (Blueprint $table) {
            $table->dropColumn('comment_reaction');
        });
    }
}