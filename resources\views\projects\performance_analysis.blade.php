@extends('layouts.admin')
@section('content')
<style>
    .modern-card {
        border-radius: 1rem;
        box-shadow: 0 4px 24px rgba(0,0,0,0.08);
        background: #fff;
        padding: 2rem 1.5rem 1.5rem 1.5rem;
        margin-bottom: 2rem;
        transition: box-shadow 0.3s;
    }
    .modern-card:hover {
        box-shadow: 0 8px 32px rgba(0,0,0,0.16);
    }
    .filter-box {
        border: 1px solid #e3e6ef;
        border-radius: 0.7rem;
        background: #f9fafb;
        padding: 0.5rem 1rem;
        display: flex;
        align-items: center;
        height: 48px;
        margin-bottom: 1rem;
    }
    .filter-box input,
    .filter-box select {
        border: none;
        background: transparent;
        outline: none;
        width: 100%;
        font-size: 1rem;
        color: #222;
    }
    .filter-box input:focus,
    .filter-box select:focus {
        box-shadow: none;
    }
    .filter-icon {
        color: #adb5bd;
        font-size: 1.2em;
        margin-right: 0.5rem;
    }
    @media (max-width: 767px) {
        .modern-card { padding: 1rem 0.5rem; }
        .table-responsive { font-size: 0.95em; }
    }
</style>
<div class="container-fluid">
    <form method="GET" action="" class="mb-0" id="filterForm">
        <div class="modern-card">
            <div class="row g-3 align-items-end">
                <div class="col-12 col-md-2">
                    <div class="filter-box">
                        <select name="assigned_by">
                            <option value="">Assigned By</option>
                            @foreach($users as $id => $name)
                                <option value="{{ $id }}" {{ request('assigned_by') == $id ? 'selected' : '' }}>{{ $name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-12 col-md-2">
                    <div class="filter-box">
                        <select name="assigned_to">
                            <option value="">Assigned To</option>
                            @foreach($users as $id => $name)
                                <option value="{{ $id }}" {{ request('assigned_to', optional($selectedUser)->id) == $id ? 'selected' : '' }}>{{ $name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-12 col-md-4">
                    <div class="filter-box">
                        <span class="filter-icon"><i class="ti ti-calendar"></i></span>
                        <input type="text" name="date_range" id="dateRangeInput" placeholder="Task Creation Date Range" value="{{ request('date_range') }}">
                    </div>
                </div>
                <div class="col-12 col-md-4 d-flex gap-2">
                    <button type="submit" class="btn btn-primary w-100">Filter</button>
                    <a href="?" class="btn btn-outline-secondary w-100">Clear</a>
                </div>
            </div>
        </div>
    </form>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
              <h3 class="card-title" style="margin-left:15px; margin-top:10px; margin-buttom:10px;">Assigned To: <strong>{{ optional($selectedUser)->name ?? '-' }}</strong></h3>
                <div class="card-body"> 
                    <div class="table-responsive">
                        <table class="table table-bordered text-center">
                            <thead>
                                <tr>
                                    <th>Task Allotted</th>
                                    <th>Pending</th>
                                    <th>In Progress</th>
                                    <th>Completed</th>
                                    <th>On Hold</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>{{ $statusCounts['allotted'] }}</td>
                                    <td>{{ $statusCounts['pending'] }}</td>
                                    <td>{{ $statusCounts['in_progress'] }}</td>
                                    <td>{{ $statusCounts['completed'] }}</td>
                                    <td>{{ $statusCounts['on_hold'] }}</td>
                                </tr>
                                <tr>
                                    <td>{{ $statusPercents['allotted'] }}%</td>
                                    <td>{{ $statusPercents['pending'] }} (%)</td>
                                    <td>{{ $statusPercents['in_progress'] }} (%)</td>
                                    <td>{{ $statusPercents['completed'] }} (%)</td>
                                    <td>{{ $statusPercents['on_hold'] }} (%)</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <h6 class="mt-4">Task Summary [Weekly & Monthly]</h6>
                    <ul class="list-group mb-3">
                        <li class="list-group-item">Tasks Assigned last 7 Days: {{ $tasksAssigned7 }}</li>
                        <li class="list-group-item">Completed Tasks last 7 Days: {{ $tasksCompleted7 }}</li>
                        <li class="list-group-item">Tasks Assigned last 30 Days: {{ $tasksAssigned30 }}</li>
                        <li class="list-group-item">Completed Tasks last 30 Days: {{ $tasksCompleted30 }}</li>
                    </ul>
                    <h6>Performance Metrics</h6>
                    <ul class="list-group">
                        <li class="list-group-item">Task Completion Rate: {{ $completionRate }}%</li>
                        <li class="list-group-item">Average Completion Time [Based on Total Completed Time/Total Completed Task]: {{ $avgCompletionStr }}</li>
                    </ul>

                    <!-- Performance Grade Section (Responsive) -->
                    <div class="card mt-4">
                        <div class="card-body">
                            <h5 class="mb-3 text-center"><strong>Performance Grade: {{ $performanceGrade ?? 'B (Good)' }}</strong></h5>
                            <div class="mb-2 text-center text-muted" style="font-size:0.97em;">
                                The Grading will consider <strong>Task Completion Rate: Higher completion Percentage &amp; Average Completion Time: Faster completion times.</strong>
                            </div>
                            <div class="mb-4 text-center text-muted" style="font-size:0.97em;">
                                Grading Criteria: <span class="fw-bold">Grade A:</span> Excellent, <span class="fw-bold">Grade B:</span> Good, <span class="fw-bold">Grade C:</span> Satisfactory, <span class="fw-bold">Grade D:</span> Needs Improvement, <span class="fw-bold">Grade E:</span> Poor.
                            </div>
                            <div class="table-responsive mb-4 mt-2">
                                <table class="table table-bordered text-center align-middle mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th colspan="6" class="text-start bg-white" style="font-size:1.08em;">
                                                <span class="fw-bold">Task Assigned To:</span> {{ optional($selectedUser)->name ?? '-' }}
                                            </th>
                                        </tr>
                                        <tr>
                                            <th>Assigned To</th>
                                            <th>Task Allotted</th>
                                            <th>Pending</th>
                                            <th>In Progress</th>
                                            <th>Completed</th>
                                            <th>On Hold</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>{{ optional($selectedUser)->name ?? '-' }}</strong></td>
                                            <td>{{ $statusCounts['allotted'] }}</td>
                                            <td>{{ $statusCounts['pending'] }}</td>
                                            <td>{{ $statusCounts['in_progress'] }}</td>
                                            <td>{{ $statusCounts['completed'] }}</td>
                                            <td>{{ $statusCounts['on_hold'] }}</td>
                                        </tr>
                                        <tr class="table-secondary">
                                            <td><strong>Task Allotted (%)</strong></td>
                                            <td>100%</td>
                                            <td>{{ $statusPercents['pending'] }} (%)</td>
                                            <td>{{ $statusPercents['in_progress'] }} (%)</td>
                                            <td>{{ $statusPercents['completed'] }} (%)</td>
                                            <td>{{ $statusPercents['on_hold'] }} (%)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6 mb-2 mb-md-0">
                                    <h6 class="text-primary">Task Summary [Weekly & Monthly]</h6>
                                    <ul class="list-group">
                                        <li class="list-group-item">Tasks Assigned last 7 Days: <span class="fw-bold">{{ $tasksAssigned7 }}</span></li>
                                        <li class="list-group-item">Completed Tasks last 7 Days: <span class="fw-bold">{{ $tasksCompleted7 }}</span></li>
                                        <li class="list-group-item">Tasks Assigned last 30 Days: <span class="fw-bold">{{ $tasksAssigned30 }}</span></li>
                                        <li class="list-group-item">Completed Tasks last 30 Days: <span class="fw-bold">{{ $tasksCompleted30 }}</span></li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-primary">Performance Metrics</h6>
                                    <ul class="list-group">
                                        <li class="list-group-item">Task Completion Rate: <span class="fw-bold">{{ $completionRate }}%</span></li>
                                        <li class="list-group-item">
                                            <div class="d-flex flex-column flex-md-row justify-content-between align-items-md-center">
                                                <span><strong>Average Completion Time:</strong></span>
                                                <span>
                                                    <span class="fw-bold">{{ $avgCompletionStr }}</span>
                                                    <span class="text-muted ms-2" style="font-size:0.95em;">@if(isset($avgCompletionRaw) && $avgCompletionRaw > 0) ({{ number_format($avgCompletionRaw) }} seconds) @endif</span>
                                                </span>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="text-center mt-4">
                                <h5 class="mb-2"><strong>Performance Grade: {{ $performanceGradeA ?? 'A (Excellent)' }}</strong></h5>
                                <div class="text-muted" style="font-size:0.97em;">
                                    The Grading will consider <strong>Task Completion Rate: Higher completion Percentage &amp; Average Completion Time: Faster completion times.</strong>
                                </div>
                                <div class="text-muted" style="font-size:0.97em;">
                                    Grading Criteria: <span class="fw-bold">Grade A:</span> Excellent, <span class="fw-bold">Grade B:</span> Good, <span class="fw-bold">Grade C:</span> Satisfactory, <span class="fw-bold">Grade D:</span> Needs Improvement, <span class="fw-bold">Grade E:</span> Poor.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    // Optionally, add a date picker for date_range
    if (window.flatpickr) {
        flatpickr('#dateRangeInput', { mode: 'range', dateFormat: 'd/m/Y' });
    }
</script>
@endsection 