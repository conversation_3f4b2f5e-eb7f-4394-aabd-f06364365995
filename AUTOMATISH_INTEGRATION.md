# Automatish Integration Documentation

## Overview

This document describes the integration between the CRM system and Automatish module. The integration allows automatic user synchronization from CRM to Automatish and provides SSO (Single Sign-On) access.

## Features

1. **Automatic User Sync**: When a user is created in the CRM, they are automatically synced to Automatish
2. **External Signup Endpoint**: Uses Automatish's `/external-signup/user` endpoint for user creation
3. **Sidebar Integration**: Automatish appears in the sidebar for all users
4. **SSO Login**: Users can access Automatish directly from the CRM sidebar

## Configuration

### Module Integration Setup

The Automatish module should be configured in the `module_integrations` table with:

```sql
INSERT INTO module_integrations (name, base_url, sync_endpoint, sso_endpoint, api_token, enabled) 
VALUES (
    'Automatish',
    'https://your-automatish-domain.com',
    '/external-signup/user',
    '/sso-login',
    'your-api-token',
    1
);
```

### Required Fields

- **name**: Must be exactly "Automatish" (case-insensitive matching)
- **base_url**: The base URL of your Automatish installation
- **sync_endpoint**: `/external-signup/user` (the endpoint for user creation)
- **sso_endpoint**: `/sso-login` (the endpoint for SSO login)
- **api_token**: API token for authentication with Automatish
- **enabled**: Set to 1 to enable the integration

## API Payload Format

When syncing users to Automatish, the following payload is sent to the `/external-signup/user` endpoint:

```json
{
    "fullName": "John Doe",
    "email": "<EMAIL>",
    "password": "securePassword123"
}
```

### Field Mapping

- `fullName`: User's full name from CRM
- `email`: User's email address from CRM
- `password`: Plain text password (if available during user creation)

## Implementation Details

### Files Modified

1. **ModuleIntegrationController.php**
   - Added `syncUserToAutomatish()` method
   - Handles the specific payload format for Automatish

2. **UserController.php**
   - Modified `syncUserToModules()` to accept plain password
   - Added special handling for Automatish module
   - Updated all calls to pass plain password when available

3. **menu.blade.php**
   - Added Automatish section to sidebar
   - Shows for all users when module is enabled
   - Uses robot icon (ti-robot)

### User Creation Flow

1. User is created in CRM through the user creation form
2. Plain password is captured before hashing
3. User is saved to database with hashed password
4. `syncUserToModules()` is called with plain password
5. For Automatish module, `syncUserToAutomatish()` is called
6. API request is made to Automatish with required payload
7. Success/failure is logged

### Sidebar Integration

The Automatish link appears in the sidebar when:
- Module is enabled in `module_integrations` table
- SSO endpoint is configured
- Uses the existing SSO login route: `module-integration.sso-login`

## Testing

### Manual Testing

1. Run the test script: `php test_automatish_integration.php`
2. Create a new user through the CRM interface
3. Check Automatish to verify user was created
4. Click Automatish link in sidebar to test SSO

### Automated Testing

Run the feature test:
```bash
php artisan test tests/Feature/AutomatishIntegrationTest.php
```

## Troubleshooting

### Common Issues

1. **User not syncing to Automatish**
   - Check if Automatish module is enabled
   - Verify API token is correct
   - Check logs for error messages
   - Ensure sync endpoint is correct

2. **Automatish not appearing in sidebar**
   - Verify module is enabled
   - Check if SSO endpoint is configured
   - Clear cache if necessary

3. **SSO login not working**
   - Verify SSO endpoint is correct
   - Check SSO_SECRET in .env file
   - Ensure user exists in both systems

### Logging

All sync attempts are logged with the following information:
- User ID and email
- Response status and body
- Success/failure status
- Error messages if any

Check Laravel logs for detailed information about sync attempts.

## Security Considerations

1. **API Token**: Store securely and rotate regularly
2. **Plain Password**: Only used during sync, not stored
3. **HTTPS**: Ensure all API calls use HTTPS
4. **SSO Token**: Uses JWT with configurable secret

## Future Enhancements

1. **Bulk User Sync**: Sync existing users to Automatish
2. **User Updates**: Sync user profile changes
3. **User Deletion**: Remove users from Automatish when deleted from CRM
4. **Error Handling**: Retry mechanism for failed syncs
5. **Configuration UI**: Admin interface for module settings
