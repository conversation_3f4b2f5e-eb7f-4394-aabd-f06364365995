<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create pricing plan permissions
        $permissions = [
            [
                'name' => 'manage pricing plan',
                'guard_name' => 'web',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'create pricing plan',
                'guard_name' => 'web',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'edit pricing plan',
                'guard_name' => 'web',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'delete pricing plan',
                'guard_name' => 'web',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        // Insert permissions into the permissions table
        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name'], 'guard_name' => $permission['guard_name']],
                $permission
            );
        }

        // Clear permission cache to ensure new permissions are available
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Assign permissions to system admin role
        $systemAdminRole = Role::where('name', 'system admin')->first();
        if ($systemAdminRole) {
            $permissionNames = [
                'manage pricing plan',
                'create pricing plan',
                'edit pricing plan',
                'delete pricing plan',
            ];
            
            // Check if permissions exist before assigning
            foreach ($permissionNames as $permissionName) {
                $permission = Permission::where('name', $permissionName)->where('guard_name', 'web')->first();
                if ($permission && !$systemAdminRole->hasPermissionTo($permission)) {
                    $systemAdminRole->givePermissionTo($permission);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove permissions from system admin role
        $systemAdminRole = Role::where('name', 'system admin')->first();
        if ($systemAdminRole) {
            $permissionNames = [
                'manage pricing plan',
                'create pricing plan',
                'edit pricing plan',
                'delete pricing plan',
            ];
            
            $systemAdminRole->revokePermissionTo($permissionNames);
        }

        // Delete the permissions
        $permissionNames = [
            'manage pricing plan',
            'create pricing plan',
            'edit pricing plan',
            'delete pricing plan',
        ];

        foreach ($permissionNames as $permissionName) {
            $permission = Permission::where('name', $permissionName)->first();
            if ($permission) {
                $permission->delete();
            }
        }
    }
};
