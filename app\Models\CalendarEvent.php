<?php

namespace App\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CalendarEvent extends Model
{
    protected $fillable = [
        'title',
        'start_date',
        'end_date',
        'duration',
        'booking_per_slot',
        'minimum_notice',
        'description',
        'location',
        'meet_link',
        'physical_address',
        'require_name',
        'require_email',
        'require_phone',
        'weekly_availability',
        'custom_field',
        'custom_field_value',
        'custom_fields',
        'created_by',
        'date_override',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'duration' => 'integer',
        'booking_per_slot' => 'integer',
        'minimum_notice' => 'integer',
        'require_name' => 'boolean',
        'require_email' => 'boolean',
        'require_phone' => 'boolean',
        'custom_fields' => 'array',
        'custom_field_value' => 'array',
        'date_override' => 'array',
        'weekly_availability' => 'array'
    ];

    public function weeklyAvailability(): HasMany
    {
        return $this->hasMany(EventWeeklyAvailability::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class, 'event_id');
    }

    public function appointmentBookings(): HasMany
    {
        return $this->hasMany(AppointmentBooking::class, 'event_id');
    }

    public function getFormattedDurationAttribute()
    {
        if (!$this->duration) return null;
        
        $hours = floor($this->duration / 60);
        $minutes = $this->duration % 60;
        
        if ($hours > 0 && $minutes > 0) {
            return "{$hours}h {$minutes}m";
        } elseif ($hours > 0) {
            return "{$hours}h";
        } else {
            return "{$minutes}m";
        }
    }


    
    

    

    public function getFormattedNoticeAttribute()
    {
        if (!$this->minimum_notice) return 'No notice required';
        
        
        $hours = floor($this->minimum_notice / 60);
        $days = floor($hours / 24);
        $weeks = floor($days / 7);
        
        if ($weeks > 0) return "{$weeks} week" . ($weeks > 1 ? 's' : '');
        if ($days > 0) return "{$days} day" . ($days > 1 ? 's' : '');
        if ($hours > 0) return "{$hours} hour" . ($hours > 1 ? 's' : '');
        return "{$this->minimum_notice} minute" . ($this->minimum_notice > 1 ? 's' : '');
    }
}