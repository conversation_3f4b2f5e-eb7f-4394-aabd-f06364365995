@php
    // Get all tasks for this project
    $allTasks = \App\Models\Project::projectTask($project->id);
@endphp

<div class="table-responsive">
    <table class="table table-hover">
        <thead>
            <tr>
                <th>{{ __('Task') }}</th>
                <th>{{ __('Stage') }}</th>
                <th>{{ __('Priority') }}</th>
                <th>{{ __('Assigned To') }}</th>
                <th>{{ __('Due Date') }}</th>
                <th>{{ __('Actions') }}</th>
            </tr>
        </thead>
        <tbody>
            @forelse($allTasks as $task)
                <tr>
                    <td>
                        <div>
                            <h6 class="mb-0">
                                <a href="#" class="text-decoration-none text-dark"
                                   data-url="{{ route('projects.tasks.show', [$project->id, $task->id]) }}"
                                   data-ajax-popup="true" data-size="lg"
                                   data-bs-original-title="{{ $task->name }}">{{ $task->name }}</a>
                            </h6>
                            @if($task->description)
                                <small class="text-muted">{{ Str::limit($task->description, 50) }}</small>
                            @endif
                        </div>
                    </td>
                    <td>
                        @php
                            $stageName = $task->stage->name ?? 'No Stage';
                            $stageColor = 'secondary'; // default color

                            // Map stage names to colors
                            $stageColors = [
                                'in_progress' => 'primary',
                                'done' => 'success',
                                'review' => 'purple',
                                'todo' => 'warning', // using warning for orange color
                            ];

                            // Check for exact match or partial match (case insensitive)
                            $lowerStageName = strtolower(str_replace(' ', '_', $stageName));
                            foreach ($stageColors as $stage => $color) {
                                if (strpos($lowerStageName, $stage) !== false) {
                                    $stageColor = $color;
                                    break;
                                }
                            }
                        @endphp
                        <span class="badge bg-{{ $stageColor }} text-white">{{ $stageName }}</span>
                    </td>
                    <td>
                        <span class="priority-badge priority-{{ $task->priority }}">
                            {{ ucfirst($task->priority) }}
                        </span>
                    </td>
                    <td>
                        <div class="d-flex">
                            @if($task->assign_to)
                                @foreach(explode(',', $task->assign_to) as $userId)
                                    @php $user = \App\Models\User::find($userId); @endphp
                                    @if($user)
                                        <div class="user-avatar-sm me-1" title="{{ $user->name }}">
                                            {{ substr($user->name, 0, 1) }}
                                        </div>
                                    @endif
                                @endforeach
                            @else
                                <span class="text-muted">{{ __('Unassigned') }}</span>
                            @endif
                        </div>
                    </td>
                    <td>
                        <small class="text-muted">{{ \Carbon\Carbon::parse($task->end_date)->format('M d, Y') }}</small>
                    </td>
                    <td>
                        <div class="d-flex gap-1">
                            @can('view project task')
                                <a href="#" data-size="lg"
                                   data-url="{{ route('projects.tasks.show', [$project->id, $task->id]) }}"
                                   data-ajax-popup="true" class="btn btn-sm btn-outline-primary"
                                   data-bs-original-title="{{ __('View') }}">
                                    <i class="ti ti-eye"></i>
                                </a>
                            @endcan
                            @can('edit project task')
                                <a href="#" data-size="lg"
                                   data-url="{{ route('projects.tasks.edit', [$project->id, $task->id]) }}"
                                   data-ajax-popup="true" class="btn btn-sm btn-outline-warning"
                                   data-bs-original-title="{{ __('Edit') }}">
                                    <i class="ti ti-pencil"></i>
                                </a>
                            @endcan
                            @can('delete project task')
                                <form method="POST" action="{{ route('projects.tasks.destroy', [$project->id, $task->id]) }}" 
                                      style="display: inline-block;">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-outline-danger bs-pass-para"
                                            data-bs-original-title="{{ __('Delete') }}">
                                        <i class="ti ti-trash"></i>
                                    </button>
                                </form>
                            @endcan
                        </div>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="6" class="text-center py-4">
                        <div class="text-center">
                            <i class="ti ti-clipboard-list" style="font-size: 2rem; color: #ccc;"></i>
                            <h6 class="text-muted mt-2">{{ __('No Tasks Found') }}</h6>
                            <p class="text-muted">{{ __('Tasks will appear here once created') }}</p>
                        </div>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

<style>
/* Custom purple badge color for review stage */
.badge.bg-purple {
    background-color: #6f42c1 !important;
    color: white !important;
}

.badge.bg-purple:hover {
    background-color: #5a359a !important;
}
</style>
