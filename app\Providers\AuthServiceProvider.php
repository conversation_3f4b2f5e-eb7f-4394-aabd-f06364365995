<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        // Define custom gate for automatish access that checks module permissions for all user types
        Gate::define('access automatish', function ($user) {
            // System admin always has access (they manage the system)
            if ($user->type === 'system admin') {
                return true;
            }

            // For company and employee users, ONLY check pricing plan permissions (no fallback to traditional permissions)
            if (in_array($user->type, ['company', 'employee'])) {
                return $user->hasModulePermission('automatish', 'access automatish');
            }

            // For other user types (super admin, staff), check traditional permissions
            return $user->can('access automatish');
        });

        // Define custom gate for OMX Flow access that checks module permissions for all user types
        Gate::define('access omx flow', function ($user) {
            // System admin always has access (they manage the system)
            if ($user->type === 'system admin') {
                return true;
            }

            // For company and employee users, ONLY check pricing plan permissions (no fallback to traditional permissions)
            if (in_array($user->type, ['company', 'employee'])) {
                return $user->hasModulePermission('omx_flow', 'access omx flow');
            }

            // For other user types (super admin, staff), check traditional permissions
            return $user->can('access omx flow');
        });
    }
}
