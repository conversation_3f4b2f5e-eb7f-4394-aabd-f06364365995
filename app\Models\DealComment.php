<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DealComment extends Model
{
    protected $fillable = [
        'deal_id',
        'user_id',
        'comment',
        'comment_reaction',
        'parent_id',
        'created_by',
    ];

    protected $casts = [
        'comment_reaction' => 'array'
    ];

    public function deal()
    {
        return $this->belongsTo(Deal::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function replies()
    {
        return $this->hasMany(DealComment::class, 'parent_id');
    }

    public function parent()
    {
        return $this->belongsTo(DealComment::class, 'parent_id');
    }

    public function getReactionCounts()
    {
        $reactions = $this->comment_reaction ?? [];
        $counts = [];
        foreach ($reactions as $userId => $data) {
            $reaction = is_array($data) && isset($data['reaction']) ? $data['reaction'] : $data;
            if (!isset($counts[$reaction])) {
                $counts[$reaction] = 0;
            }
            $counts[$reaction]++;
        }
        return $counts;
    }
}