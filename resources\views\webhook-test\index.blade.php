@extends('layouts.admin')

@section('page-title')
    {{ __('Webhook Testing') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('Webhook Testing') }}</li>
@endsection

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('Test CRM Webhooks') }}</h5>
                    <small class="text-muted">{{ __('Test webhook connectivity and send sample data to integrated modules') }}</small>
                </div>
                <div class="card-body">
                    
                    <!-- Module Integration Status -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h6>{{ __('Module Integrations') }}</h6>
                            @if($integrations->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>{{ __('Module Name') }}</th>
                                                <th>{{ __('Base URL') }}</th>
                                                <th>{{ __('Status') }}</th>
                                                <th>{{ __('Actions') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($integrations as $integration)
                                                <tr>
                                                    <td>{{ $integration->name }}</td>
                                                    <td>{{ $integration->base_url }}</td>
                                                    <td>
                                                        <span class="badge badge-{{ $integration->enabled ? 'success' : 'secondary' }}">
                                                            {{ $integration->enabled ? __('Enabled') : __('Disabled') }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        @if($integration->enabled)
                                                            <button class="btn btn-sm btn-primary test-module-btn" 
                                                                    data-integration-id="{{ $integration->id }}">
                                                                {{ __('Test Connection') }}
                                                            </button>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="alert alert-info">
                                    {{ __('No module integrations configured. Please add module integrations first.') }}
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Test Webhook Form -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>{{ __('Send Test Webhook') }}</h6>
                                </div>
                                <div class="card-body">
                                    <form id="test-webhook-form">
                                        <div class="form-group">
                                            <label for="action">{{ __('Webhook Action') }}</label>
                                            <select class="form-control" id="action" name="action" required>
                                                <option value="">{{ __('Select Action') }}</option>
                                                @foreach($actions as $action)
                                                    <option value="{{ $action }}">{{ $action }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="integration_id">{{ __('Target Module (Optional)') }}</label>
                                            <select class="form-control" id="integration_id" name="integration_id">
                                                <option value="">{{ __('Send to All Modules') }}</option>
                                                @foreach($integrations->where('enabled', true) as $integration)
                                                    <option value="{{ $integration->id }}">{{ $integration->name }}</option>
                                                @endforeach
                                            </select>
                                            <small class="text-muted">{{ __('Leave empty to send to all enabled modules') }}</small>
                                        </div>
                                        
                                        <button type="submit" class="btn btn-success">
                                            {{ __('Send Test Webhook') }}
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>{{ __('Webhook Logs') }}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <a href="{{ route('webhook-test.logs') }}" class="btn btn-sm btn-info" target="_blank">
                                            {{ __('View Full Logs') }}
                                        </a>
                                        <button class="btn btn-sm btn-warning" id="clear-logs-btn">
                                            {{ __('Clear Logs') }}
                                        </button>
                                    </div>
                                    <div id="webhook-results" class="border rounded p-3" style="height: 300px; overflow-y: auto; background-color: #f8f9fa;">
                                        <p class="text-muted">{{ __('Webhook test results will appear here...') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<script>
$(document).ready(function() {
    // Test module connection
    $('.test-module-btn').click(function() {
        const integrationId = $(this).data('integration-id');
        const btn = $(this);
        
        btn.prop('disabled', true).text('{{ __("Testing...") }}');
        
        $.ajax({
            url: '{{ route("webhook-test.test-module") }}',
            method: 'POST',
            data: {
                integration_id: integrationId,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                const alertClass = response.success ? 'alert-success' : 'alert-danger';
                showAlert(response.message, alertClass);
                
                // Add to results
                addToResults(`Module Test: ${response.message}`, response.details);
            },
            error: function() {
                showAlert('{{ __("Error testing module connection") }}', 'alert-danger');
            },
            complete: function() {
                btn.prop('disabled', false).text('{{ __("Test Connection") }}');
            }
        });
    });
    
    // Send test webhook
    $('#test-webhook-form').submit(function(e) {
        e.preventDefault();
        
        const formData = $(this).serialize();
        const submitBtn = $(this).find('button[type="submit"]');
        
        submitBtn.prop('disabled', true).text('{{ __("Sending...") }}');
        
        $.ajax({
            url: '{{ route("webhook-test.send") }}',
            method: 'POST',
            data: formData + '&_token={{ csrf_token() }}',
            success: function(response) {
                const alertClass = response.success ? 'alert-success' : 'alert-danger';
                showAlert(response.message, alertClass);
                
                // Add to results
                addToResults(`Webhook Test: ${response.message}`, response.details);
            },
            error: function() {
                showAlert('{{ __("Error sending test webhook") }}', 'alert-danger');
            },
            complete: function() {
                submitBtn.prop('disabled', false).text('{{ __("Send Test Webhook") }}');
            }
        });
    });
    
    // Clear logs
    $('#clear-logs-btn').click(function() {
        if (confirm('{{ __("Are you sure you want to clear all webhook logs?") }}')) {
            $.ajax({
                url: '{{ route("webhook-test.clear-logs") }}',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    showAlert(response.message, 'alert-success');
                    $('#webhook-results').html('<p class="text-muted">{{ __("Webhook test results will appear here...") }}</p>');
                },
                error: function() {
                    showAlert('{{ __("Error clearing logs") }}', 'alert-danger');
                }
            });
        }
    });
    
    function showAlert(message, alertClass) {
        const alert = `<div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>`;
        
        $('.card-body').first().prepend(alert);
        
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }
    
    function addToResults(message, details) {
        const timestamp = new Date().toLocaleString();
        const resultHtml = `
            <div class="mb-2 p-2 border-bottom">
                <small class="text-muted">${timestamp}</small><br>
                <strong>${message}</strong>
                ${details ? `<br><small><pre>${JSON.stringify(details, null, 2)}</pre></small>` : ''}
            </div>
        `;
        
        const resultsDiv = $('#webhook-results');
        if (resultsDiv.find('p.text-muted').length) {
            resultsDiv.empty();
        }
        
        resultsDiv.prepend(resultHtml);
    }
});
</script>
@endpush
