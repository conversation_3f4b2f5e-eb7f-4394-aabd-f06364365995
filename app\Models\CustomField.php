<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Custom<PERSON>ield extends Model
{
    protected $fillable = [
        'name',
        'type',
        'module',
        'created_by',
        'unique_key',
        'options',
    ];

    protected $casts = [
        'options' => 'array',
    ];

    // Override the options accessor to ensure it always returns an array
    public function getOptionsAttribute($value)
    {
        if (is_null($value)) {
            return [];
        }
        
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return is_array($decoded) ? $decoded : [];
        }
        
        if (is_array($value)) {
            return $value;
        }
        
        return [];
    }

    public static $fieldTypes = [
        'text' => 'Text',
        'email' => 'Email',
        'number' => 'Number',
        'date' => 'Date',
        'textarea' => 'Textarea',
        'select' => 'Dropdown menu',
        'radio' => 'Radio button group',
        'checkbox' => 'Single or multiple checkbox options',
        'multiselect' => 'Dropdown with multiple selection',
        'link' => 'Link',
        'color' => 'Color',
        'file' => 'Single file upload',
        'file_multiple' => 'Multiple file upload',
        'datetime' => 'DateTime',
    ];

    public static $modules = [
        'user' => 'User',
        'customer' => 'Customer',
        'vendor' => 'Vendor',
        'product' => 'Product',
        'proposal' => 'Proposal',
        'Invoice' => 'Invoice',
        'Bill' => 'Bill',
        'account' => 'Account',
        'Leads' => 'Leads',
    ];

    public static function saveData($obj, $data)
    {
        if ($data) {
            $RecordId = $obj->id;
            foreach ($data as $fieldId => $value) {
                \DB::insert(
                    'insert into custom_field_values (`record_id`, `field_id`,`value`,`created_at`,`updated_at`) values (?, ?, ?, ?, ?) 
                    ON DUPLICATE KEY UPDATE `value` = VALUES(`value`),`updated_at` = VALUES(`updated_at`)', [
                        $RecordId,
                        $fieldId,
                        is_array($value) ? json_encode($value) : $value,
                        date('Y-m-d H:i:s'),
                        date('Y-m-d H:i:s'),
                    ]
                );
            }
        }
    }

    public static function getData($model, $module)
    {
        $customFields = self::where('module', $module)->get();
        $data = [];

        foreach ($customFields as $field) {
            $value = \App\Models\CustomFieldValue::where('field_id', $field->id)
                        ->where('record_id', $model->id)
                        ->value('value');

            if (!is_null($value)) {
                if (in_array($field->type, ['checkbox', 'radio']) && is_string($value)) {
                    $decoded = json_decode($value, true);
                    $data[$field->id] = $decoded ?: $value;
                } else {
                    $data[$field->id] = $value;
                }
            }
        }

        return $data;
    }
}