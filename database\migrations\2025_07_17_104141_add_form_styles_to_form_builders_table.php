<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('form_builders', function (Blueprint $table) {
            $table->json('form_styles')->nullable()->after('is_lead_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('form_builders', function (Blueprint $table) {
            $table->dropColumn('form_styles');
        });
    }
};
