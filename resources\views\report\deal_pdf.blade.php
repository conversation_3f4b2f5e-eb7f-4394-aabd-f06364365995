@extends('layouts.admin')

@section('page-title')
    {{ __('Deal Report') }}
@endsection

@section('content')
<style>
    .pdf-container {
        background: white;
        padding: 30px;
        margin: 20px auto;
        max-width: 1200px;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    .pdf-header {
        text-align: center;
        margin-bottom: 30px;
        border-bottom: 2px solid #007bff;
        padding-bottom: 20px;
    }
    .pdf-title {
        font-size: 28px;
        font-weight: bold;
        color: #333;
        margin-bottom: 10px;
    }
    .pdf-subtitle {
        font-size: 16px;
        color: #666;
        margin-bottom: 5px;
    }
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    .stat-card {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border-left: 4px solid #28a745;
    }
    .stat-title {
        font-size: 14px;
        color: #666;
        margin-bottom: 5px;
    }
    .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #333;
    }
    .chart-section {
        margin-bottom: 30px;
        page-break-inside: avoid;
    }
    .chart-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 15px;
        color: #333;
    }
    .data-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 15px;
    }
    .data-table th,
    .data-table td {
        padding: 8px 12px;
        border: 1px solid #ddd;
        text-align: left;
    }
    .data-table th {
        background-color: #f8f9fa;
        font-weight: bold;
    }
    .currency {
        color: #28a745;
        font-weight: bold;
    }
    @media print {
        .pdf-container {
            box-shadow: none;
            margin: 0;
            padding: 20px;
        }
    }
</style>

<div class="pdf-container">
    <div class="pdf-header">
        <div class="pdf-title">{{ __('Deal Report') }}</div>
        <div class="pdf-subtitle">{{ __('Generated on') }}: {{ date('F j, Y \a\t g:i A') }}</div>
        <div class="pdf-subtitle">{{ __('Report Period') }}: {{ date('Y') }}</div>
    </div>

    <!-- Summary Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-title">{{ __('Total Deals This Week') }}</div>
            <div class="stat-value">{{ array_sum($devicearray['data']) }}</div>
        </div>
        <div class="stat-card">
            <div class="stat-title">{{ __('Deal Sources') }}</div>
            <div class="stat-value">{{ count($dealsourceName) }}</div>
        </div>
        <div class="stat-card">
            <div class="stat-title">{{ __('Active Pipelines') }}</div>
            <div class="stat-value">{{ count($dealpipelineName) }}</div>
        </div>
        <div class="stat-card">
            <div class="stat-title">{{ __('Total Deal Value') }}</div>
            <div class="stat-value currency">${{ number_format(array_sum($dealValuesData), 2) }}</div>
        </div>
    </div>

    <!-- Weekly Deal Distribution -->
    <div class="chart-section">
        <div class="chart-title">{{ __('Weekly Deal Distribution') }}</div>
        <table class="data-table">
            <thead>
                <tr>
                    <th>{{ __('Date') }}</th>
                    <th>{{ __('Deals Count') }}</th>
                </tr>
            </thead>
            <tbody>
                @foreach($devicearray['label'] as $index => $date)
                <tr>
                    <td>{{ date('M j, Y', strtotime($date)) }}</td>
                    <td>{{ $devicearray['data'][$index] ?? 0 }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Deal Sources Analysis -->
    <div class="chart-section">
        <div class="chart-title">{{ __('Deal Sources Analysis') }}</div>
        <table class="data-table">
            <thead>
                <tr>
                    <th>{{ __('Source') }}</th>
                    <th>{{ __('Deals Count') }}</th>
                    <th>{{ __('Percentage') }}</th>
                </tr>
            </thead>
            <tbody>
                @php $totalSourceDeals = array_sum($dealsourceeData); @endphp
                @foreach($dealsourceName as $index => $source)
                <tr>
                    <td>{{ $source }}</td>
                    <td>{{ $dealsourceeData[$index] ?? 0 }}</td>
                    <td>{{ $totalSourceDeals > 0 ? round(($dealsourceeData[$index] ?? 0) / $totalSourceDeals * 100, 1) : 0 }}%</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Team Performance -->
    <div class="chart-section">
        <div class="chart-title">{{ __('Team Performance') }}</div>
        <table class="data-table">
            <thead>
                <tr>
                    <th>{{ __('Team Member') }}</th>
                    <th>{{ __('Deals Handled') }}</th>
                    <th>{{ __('Performance') }}</th>
                </tr>
            </thead>
            <tbody>
                @php $totalUserDeals = array_sum($dealUserData); @endphp
                @foreach($dealUserName as $index => $user)
                <tr>
                    <td>{{ $user }}</td>
                    <td>{{ $dealUserData[$index] ?? 0 }}</td>
                    <td>
                        @php 
                            $percentage = $totalUserDeals > 0 ? round(($dealUserData[$index] ?? 0) / $totalUserDeals * 100, 1) : 0;
                        @endphp
                        {{ $percentage }}%
                        @if($percentage >= 25)
                            <span style="color: #28a745;">{{ __('Excellent') }}</span>
                        @elseif($percentage >= 15)
                            <span style="color: #ffc107;">{{ __('Good') }}</span>
                        @else
                            <span style="color: #dc3545;">{{ __('Needs Improvement') }}</span>
                        @endif
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Pipeline Distribution -->
    <div class="chart-section">
        <div class="chart-title">{{ __('Pipeline Distribution') }}</div>
        <table class="data-table">
            <thead>
                <tr>
                    <th>{{ __('Pipeline') }}</th>
                    <th>{{ __('Deals Count') }}</th>
                    <th>{{ __('Distribution') }}</th>
                </tr>
            </thead>
            <tbody>
                @php $totalPipelineDeals = array_sum($dealpipelineeData); @endphp
                @foreach($dealpipelineName as $index => $pipeline)
                <tr>
                    <td>{{ $pipeline }}</td>
                    <td>{{ $dealpipelineeData[$index] ?? 0 }}</td>
                    <td>{{ $totalPipelineDeals > 0 ? round(($dealpipelineeData[$index] ?? 0) / $totalPipelineDeals * 100, 1) : 0 }}%</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Client Analysis -->
    <div class="chart-section">
        <div class="chart-title">{{ __('Client Analysis') }}</div>
        <table class="data-table">
            <thead>
                <tr>
                    <th>{{ __('Client') }}</th>
                    <th>{{ __('Deals Count') }}</th>
                    <th>{{ __('Share') }}</th>
                </tr>
            </thead>
            <tbody>
                @php $totalClientDeals = array_sum($dealClientData); @endphp
                @foreach($dealClientName as $index => $client)
                <tr>
                    <td>{{ $client }}</td>
                    <td>{{ $dealClientData[$index] ?? 0 }}</td>
                    <td>{{ $totalClientDeals > 0 ? round(($dealClientData[$index] ?? 0) / $totalClientDeals * 100, 1) : 0 }}%</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Deal Values Analysis -->
    <div class="chart-section">
        <div class="chart-title">{{ __('Deal Values Analysis') }}</div>
        <table class="data-table">
            <thead>
                <tr>
                    <th>{{ __('Deal') }}</th>
                    <th>{{ __('Value') }}</th>
                    <th>{{ __('Percentage of Total') }}</th>
                </tr>
            </thead>
            <tbody>
                @php $totalValue = array_sum($dealValuesData); @endphp
                @foreach($dealValuesLabels as $index => $deal)
                <tr>
                    <td>{{ $deal }}</td>
                    <td class="currency">${{ number_format($dealValuesData[$index] ?? 0, 2) }}</td>
                    <td>{{ $totalValue > 0 ? round(($dealValuesData[$index] ?? 0) / $totalValue * 100, 1) : 0 }}%</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Monthly Trend -->
    <div class="chart-section">
        <div class="chart-title">{{ __('Monthly Deal Trend') }}</div>
        <table class="data-table">
            <thead>
                <tr>
                    <th>{{ __('Month') }}</th>
                    <th>{{ __('Deals Count') }}</th>
                    <th>{{ __('Growth') }}</th>
                </tr>
            </thead>
            <tbody>
                @foreach($labels as $index => $month)
                <tr>
                    <td>{{ $month }}</td>
                    <td>{{ $data[$index] ?? 0 }}</td>
                    <td>
                        @if($index > 0)
                            @php 
                                $current = $data[$index] ?? 0;
                                $previous = $data[$index - 1] ?? 0;
                                $growth = $previous > 0 ? round(($current - $previous) / $previous * 100, 1) : 0;
                            @endphp
                            @if($growth > 0)
                                <span style="color: #28a745;">+{{ $growth }}%</span>
                            @elseif($growth < 0)
                                <span style="color: #dc3545;">{{ $growth }}%</span>
                            @else
                                <span style="color: #6c757d;">0%</span>
                            @endif
                        @else
                            <span style="color: #6c757d;">-</span>
                        @endif
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div style="margin-top: 40px; text-align: center; color: #666; font-size: 12px;">
        {{ __('Report generated by') }} {{ config('app.name') }} {{ __('on') }} {{ date('Y-m-d H:i:s') }}
    </div>
</div>

<!-- Hidden filename input for PDF generation -->
<input type="hidden" id="filename" value="Deal_Report_{{ date('Y-m-d_H-i-s') }}">

<script src="{{ asset('js/jquery.min.js') }}"></script>
<script type="text/javascript" src="{{ asset('js/html2pdf.bundle.min.js') }}"></script>
<script>
    function closeScript() {
        setTimeout(function () {
            window.open(window.location, '_self').close();
        }, 1000);
    }

    $(window).on('load', function () {
        var element = document.getElementsByClassName('pdf-container')[0];
        var filename = $('#filename').val();

        var opt = {
            margin: [0.5, 0, 0.5, 0],
            filename: filename,
            image: {type: 'jpeg', quality: 1},
            html2canvas: {scale: 2, dpi: 72, letterRendering: true},
            jsPDF: {unit: 'in', format: 'A4'}
        };

        html2pdf().set(opt).from(element).save().then(closeScript);
    });
</script>

@endsection
