<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Plan;

class AddPersonalTasksToPlansTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('plans', function (Blueprint $table) {
            if (!Schema::hasColumn('plans', 'personal_tasks')) {
                $table->integer('personal_tasks')->default(1)->after('project');
            }
        });

        // Update all existing plans to enable personal tasks
        $plans = Plan::all();
        foreach ($plans as $plan) {
            $plan->personal_tasks = 1;
            $plan->save();
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('plans', function (Blueprint $table) {
            if (Schema::hasColumn('plans', 'personal_tasks')) {
                $table->dropColumn('personal_tasks');
            }
        });
    }
}
