<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\ModuleIntegration;
use App\Http\Controllers\ModuleIntegrationController;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AutomatishIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test Automatish module integration
        ModuleIntegration::create([
            'name' => 'Automatish',
            'base_url' => 'https://automatish.example.com',
            'sync_endpoint' => '/external-signup/user',
            'sso_endpoint' => '/sso-login',
            'api_token' => 'test-token-123',
            'enabled' => true
        ]);
    }

    /** @test */
    public function it_can_sync_user_to_automatish()
    {
        // Mock HTTP response
        Http::fake([
            'automatish.example.com/*' => Http::response(['success' => true], 200)
        ]);

        // Create a test user
        $user = User::factory()->create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>'
        ]);

        // Test the sync method
        $controller = new ModuleIntegrationController();
        $result = $controller->syncUserToAutomatish($user, 'testPassword123');

        $this->assertTrue($result);

        // Verify the HTTP request was made with correct data
        Http::assertSent(function ($request) {
            return $request->url() === 'https://automatish.example.com/external-signup/user' &&
                   $request['fullName'] === 'John Doe' &&
                   $request['email'] === '<EMAIL>' &&
                   $request['password'] === 'testPassword123';
        });
    }

    /** @test */
    public function it_handles_automatish_sync_failure_gracefully()
    {
        // Mock HTTP failure response
        Http::fake([
            'automatish.example.com/*' => Http::response(['error' => 'User already exists'], 400)
        ]);

        $user = User::factory()->create([
            'name' => 'Jane Doe',
            'email' => '<EMAIL>'
        ]);

        $controller = new ModuleIntegrationController();
        $result = $controller->syncUserToAutomatish($user, 'testPassword123');

        $this->assertFalse($result);
    }

    /** @test */
    public function it_shows_automatish_in_sidebar_when_enabled()
    {
        // Create a test user and authenticate
        $user = User::factory()->create(['type' => 'company']);
        $this->actingAs($user);

        // Make a request to a page that includes the sidebar
        $response = $this->get('/dashboard');

        // Check if Automatish appears in the sidebar
        $response->assertSee('Automatish');
        $response->assertSee('ti-robot');
    }

    /** @test */
    public function it_does_not_show_automatish_when_disabled()
    {
        // Disable the Automatish module
        $module = ModuleIntegration::where('name', 'Automatish')->first();
        $module->update(['enabled' => false]);

        $user = User::factory()->create(['type' => 'company']);
        $this->actingAs($user);

        $response = $this->get('/dashboard');

        // Automatish should not appear in the sidebar when disabled
        $response->assertDontSee('Automatish');
    }
}
