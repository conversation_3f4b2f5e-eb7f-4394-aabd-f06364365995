<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\PricingPlan;
use App\Models\ModuleIntegration;
use Illuminate\Support\Facades\Gate;

// Initialize Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Automatish Permission Test ===\n\n";

try {
    // Test 1: Check if Automatish module is enabled
    echo "1. Checking Automatish module status...\n";
    $automatishModule = ModuleIntegration::where('name', 'Automatish')->where('enabled', true)->first();
    if ($automatishModule) {
        echo "✅ Automatish module is enabled\n";
        echo "   - Base URL: {$automatishModule->base_url}\n";
        echo "   - SSO Endpoint: {$automatishModule->sso_endpoint}\n";
    } else {
        echo "❌ Automatish module is not enabled or not found\n";
    }
    echo "\n";

    // Test 2: Check company user pricing plan permissions
    echo "2. Checking company user pricing plan permissions...\n";
    $companyUser = User::where('type', 'company')->first();
    if ($companyUser) {
        echo "✅ Found company user: {$companyUser->name} (ID: {$companyUser->id})\n";
        echo "   - Plan ID: {$companyUser->plan}\n";

        $pricingPlan = $companyUser->pricingPlan;
        if ($pricingPlan) {
            echo "✅ User has pricing plan: {$pricingPlan->name} (ID: {$pricingPlan->id})\n";
            echo "   - Module permissions: " . json_encode($pricingPlan->module_permissions) . "\n";

            // Test hasModulePermission method
            $hasAutomatishAccess = $companyUser->hasModulePermission('automatish', 'access automatish');
            echo "   - hasModulePermission('automatish', 'access automatish'): " . ($hasAutomatishAccess ? 'Yes' : 'No') . "\n";

            // Test Gate permission
            if (Gate::forUser($companyUser)->check('access automatish')) {
                echo "✅ User has 'access automatish' permission via Gate\n";
            } else {
                echo "❌ User does NOT have 'access automatish' permission via Gate\n";
            }
        } else {
            echo "❌ User does not have a pricing plan assigned (new system)\n";

            // Check old plan system
            $oldPlan = $companyUser->currentPlan;
            if ($oldPlan) {
                echo "✅ User has old plan: {$oldPlan->name} (ID: {$oldPlan->id})\n";
                echo "   - Automatish enabled: " . ($oldPlan->automatish == 1 ? 'Yes' : 'No') . "\n";

                // Test hasModulePermission method (should fallback to old system)
                $hasAutomatishAccess = $companyUser->hasModulePermission('automatish', 'access automatish');
                echo "   - hasModulePermission('automatish', 'access automatish') [fallback]: " . ($hasAutomatishAccess ? 'Yes' : 'No') . "\n";

                // Test Gate permission
                if (Gate::forUser($companyUser)->check('access automatish')) {
                    echo "✅ User has 'access automatish' permission via Gate (using fallback)\n";
                } else {
                    echo "❌ User does NOT have 'access automatish' permission via Gate\n";
                }
            } else {
                echo "❌ User does not have any plan assigned\n";
            }
        }
    } else {
        echo "❌ No company user found\n";
    }
    echo "\n";

    // Test 3: Check if automatish should appear in sidebar
    echo "3. Sidebar visibility test...\n";
    if ($companyUser && $automatishModule) {
        $shouldShowInSidebar = Gate::forUser($companyUser)->check('access automatish') && 
                              $automatishModule->enabled && 
                              $automatishModule->sso_endpoint;
        
        if ($shouldShowInSidebar) {
            echo "✅ Automatish should appear in sidebar for this user\n";
            echo "   - Route: " . ($companyUser->type === 'company' ? 'company.modules.automatish' : 'module-integration.sso-login') . "\n";
        } else {
            echo "❌ Automatish should NOT appear in sidebar for this user\n";
            echo "   - Gate check: " . (Gate::forUser($companyUser)->check('access automatish') ? 'Pass' : 'Fail') . "\n";
            echo "   - Module enabled: " . ($automatishModule->enabled ? 'Yes' : 'No') . "\n";
            echo "   - SSO endpoint: " . ($automatishModule->sso_endpoint ? 'Yes' : 'No') . "\n";
        }
    } else {
        echo "❌ Cannot test sidebar visibility - missing user or module\n";
    }
    echo "\n";

    // Test 4: Test different pricing plan scenarios
    echo "4. Testing different pricing plan scenarios...\n";
    $pricingPlans = PricingPlan::all();
    foreach ($pricingPlans as $plan) {
        $hasAutomatish = isset($plan->module_permissions['automatish']) &&
                        in_array('access automatish', $plan->module_permissions['automatish']);
        echo "Pricing Plan: {$plan->name} - Automatish: " . ($hasAutomatish ? 'Enabled' : 'Disabled') . "\n";
    }
    echo "\n";

    // Test 5: Test super admin and system admin access
    echo "5. Testing admin access...\n";
    $superAdmin = User::where('type', 'super admin')->first();
    if ($superAdmin) {
        echo "Super Admin: {$superAdmin->name} (ID: {$superAdmin->id})\n";
        echo "   - Module permissions: " . json_encode($superAdmin->module_permissions) . "\n";

        $hasModulePermission = $superAdmin->hasModulePermission('automatish', 'access automatish');
        echo "   - hasModulePermission('automatish', 'access automatish'): " . ($hasModulePermission ? 'Yes' : 'No') . "\n";

        $hasAccess = Gate::forUser($superAdmin)->check('access automatish');
        echo "   - Gate access: " . ($hasAccess ? 'Granted' : 'Denied') . "\n";
    }

    $systemAdmin = User::where('type', 'system admin')->first();
    if ($systemAdmin) {
        echo "System Admin: {$systemAdmin->name} (ID: {$systemAdmin->id})\n";
        $hasAccess = Gate::forUser($systemAdmin)->check('access automatish');
        echo "   - Gate access: " . ($hasAccess ? 'Granted (always)' : 'Denied') . "\n";
    }

} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Test Complete ===\n";
