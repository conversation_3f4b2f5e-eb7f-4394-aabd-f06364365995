{{ Form::open(['route' => ['personal-tasks.store', $stage_id], 'method' => 'POST', 'enctype' => 'multipart/form-data', 'class' => 'modern-task-form']) }}

<style>
.modern-task-form {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.modern-form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.modern-form-label {
    font-weight: 600;
    font-size: 0.875rem;
    color: #374151;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modern-form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: #ffffff;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.modern-form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
}

.modern-form-control::placeholder {
    color: #9ca3af;
}

.modern-textarea {
    resize: vertical;
    min-height: 100px;
}

.priority-selector {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.priority-option {
    position: relative;
}

.priority-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    cursor: pointer;
}

.priority-label {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 0.5rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #ffffff;
}

.priority-option input[type="radio"]:checked + .priority-label {
    border-color: var(--priority-color);
    background: var(--priority-bg);
    color: var(--priority-text);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.priority-critical { --priority-color: #dc2626; --priority-bg: #fef2f2; --priority-text: #dc2626; }
.priority-high { --priority-color: #f59e0b; --priority-bg: #fffbeb; --priority-text: #f59e0b; }
.priority-medium { --priority-color: #3b82f6; --priority-bg: #eff6ff; --priority-text: #3b82f6; }
.priority-low { --priority-color: #10b981; --priority-bg: #f0fdf4; --priority-text: #10b981; }

.date-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.modern-select-wrapper {
    position: relative;
}

.modern-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

.form-section {
    background: #f8fafc;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid #e2e8f0;
}

.section-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #475569;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modern-modal-body {
    padding: 2rem;
    max-height: 70vh;
    overflow-y: auto;
}

.modern-modal-footer {
    padding: 1.5rem 2rem;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

.modern-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.modern-btn-cancel {
    background: #ffffff;
    color: #6b7280;
    border: 2px solid #e5e7eb;
}

.modern-btn-cancel:hover {
    background: #f9fafb;
    border-color: #d1d5db;
}

.modern-btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.modern-btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.user-select-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
    margin-top: 0.75rem;
}

.user-option {
    position: relative;
}

.user-option input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    cursor: pointer;
}

.user-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #ffffff;
}

.user-option input[type="checkbox"]:checked + .user-label {
    border-color: #3b82f6;
    background: #eff6ff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.75rem;
}

.user-info {
    flex: 1;
}

.user-name {
    font-weight: 600;
    font-size: 0.875rem;
    color: #374151;
    line-height: 1.2;
}

.user-check-indicator {
    opacity: 0;
    transition: all 0.2s ease;
    color: #10b981;
    font-size: 1rem;
}

.user-option input[type="checkbox"]:checked + .user-label .user-check-indicator {
    opacity: 1;
    transform: scale(1.1);
}

.assignment-info {
    background: #f0f9ff;
    border: 1px solid #e0f2fe;
    border-radius: 0.5rem;
    padding: 0.75rem;
}

.selected-users-summary {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 0.75rem;
}

.selected-user-tag {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    background: #eff6ff;
    color: #3b82f6;
    border: 1px solid #bfdbfe;
    border-radius: 0.375rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    margin: 0.125rem;
}

.input-icon {
    color: #6b7280;
    font-size: 1rem;
}
</style>

<div class="modern-modal-body">
    <!-- Task Basic Info Section -->
    <div class="form-section">
        <div class="section-title">
            <i class="ti ti-clipboard input-icon"></i>
            {{ __('Task Information') }}
        </div>

        <div class="modern-form-group">
            <label class="modern-form-label">
                <i class="ti ti-edit input-icon"></i>
                {{ __('Task Name') }}
            </label>
            {{ Form::text('name', null, [
                'class' => 'modern-form-control',
                'required' => 'required',
                'placeholder' => __('What needs to be done?'),
                'autocomplete' => 'off'
            ]) }}
        </div>

        <div class="modern-form-group">
            <label class="modern-form-label">
                <i class="ti ti-file-text input-icon"></i>
                {{ __('Description') }}
            </label>
            {{ Form::textarea('description', null, [
                'class' => 'modern-form-control modern-textarea',
                'placeholder' => __('Add more details about this task...'),
                'rows' => 3
            ]) }}
        </div>
    </div>

    <!-- Priority Section -->
    <div class="form-section">
        <div class="section-title">
            <i class="ti ti-flag input-icon"></i>
            {{ __('Priority Level') }}
        </div>

        <div class="priority-selector">
            @foreach(\App\Models\PersonalTask::$priority as $key => $value)
                <div class="priority-option">
                    <input type="radio" name="priority" value="{{ $key }}" id="priority_{{ $key }}"
                           {{ $key == 'medium' ? 'checked' : '' }} required>
                    <label for="priority_{{ $key }}" class="priority-label priority-{{ $key }}">
                        {{ $value }}
                    </label>
                </div>
            @endforeach
        </div>
    </div>

    <!-- Timeline Section -->
    <div class="form-section">
        <div class="section-title">
            <i class="ti ti-calendar input-icon"></i>
            {{ __('Timeline & Estimation') }}
        </div>

        <div class="modern-form-group">
            <label class="modern-form-label">
                <i class="ti ti-clock input-icon"></i>
                {{ __('Estimated Hours') }}
            </label>
            {{ Form::number('estimated_hrs', 0, [
                'class' => 'modern-form-control',
                'min' => '0',
                'step' => '0.5',
                'placeholder' => '0'
            ]) }}
        </div>

        <div class="date-grid">
            <div class="modern-form-group">
                <label class="modern-form-label">
                    <i class="ti ti-calendar-event input-icon"></i>
                    {{ __('Start Date') }}
                </label>
                {{ Form::date('start_date', null, ['class' => 'modern-form-control']) }}
            </div>

            <div class="modern-form-group">
                <label class="modern-form-label">
                    <i class="ti ti-calendar-due input-icon"></i>
                    {{ __('Due Date') }}
                </label>
                {{ Form::date('end_date', null, ['class' => 'modern-form-control']) }}
            </div>
        </div>
    </div>

    <!-- Assignment Section -->
    <div class="form-section">
        <div class="section-title">
            <i class="ti ti-users input-icon"></i>
            {{ __('Task Assignment') }}
        </div>

        <div class="assignment-info mb-3">
            <small class="text-muted">
                <i class="ti ti-info-circle me-1"></i>
                {{ __('Below users are from your company. Select team members to assign this task.') }}
                <br>
                <strong>{{ __('Available Users') }}: {{ count($users) }}</strong>
            </small>
        </div>

        @if(count($users) > 0)
            <div class="user-select-grid">
                @foreach($users as $user)
                    <div class="user-option">
                        <input type="checkbox" name="assign_to[]" value="{{ $user->id }}"
                               id="user_{{ $user->id }}"
                               {{ $user->id == Auth::user()->id ? 'checked' : '' }}
                               data-user-name="{{ $user->name }}"
                               data-user-email="{{ $user->email }}"
                               data-user-type="{{ $user->type }}">
                        <label for="user_{{ $user->id }}" class="user-label">
                            <div class="user-avatar">
                                {{ strtoupper(substr($user->name, 0, 1)) }}
                            </div>
                            <div class="user-info">
                                <div class="user-name">{{ $user->name }}</div>
                                <small class="text-muted">
                                    @if($user->id == Auth::user()->id)
                                        ({{ __('You') }})
                                    @else
                                        {{ $user->email }}
                                    @endif
                                    @if($user->type == 'company')
                                        <span class="badge bg-primary ms-1" style="font-size: 0.6rem;">{{ __('Owner') }}</span>
                                    @endif
                                </small>
                            </div>
                            <div class="user-check-indicator">
                                <i class="ti ti-check"></i>
                            </div>
                        </label>
                    </div>
                @endforeach
            </div>
        @else
            <div class="alert alert-warning">
                <i class="ti ti-alert-triangle me-2"></i>
                {{ __('No company users found. Please contact your administrator.') }}
            </div>
        @endif

        <div class="selected-users-summary mt-3">
            <small class="text-muted">
                <span id="selected-count">1</span> {{ __('user(s) selected') }}
            </small>
            <div id="selected-users-list" class="mt-2"></div>
        </div>
    </div>
</div>

<div class="modern-modal-footer">
    <button type="button" class="modern-btn modern-btn-cancel" data-bs-dismiss="modal">
        <i class="ti ti-x"></i>
        {{ __('Cancel') }}
    </button>
    <button type="submit" class="modern-btn modern-btn-primary">
        <i class="ti ti-plus"></i>
        {{ __('Create Task') }}
    </button>
</div>

{{ Form::close() }}

<script>
$(document).ready(function() {
    // Auto-resize textarea
    $('.modern-textarea').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    // Smooth animations for form interactions
    $('.modern-form-control').on('focus', function() {
        $(this).closest('.modern-form-group').addClass('focused');
    }).on('blur', function() {
        $(this).closest('.modern-form-group').removeClass('focused');
    });

    // Priority selection animation
    $('.priority-option input[type="radio"]').on('change', function() {
        $('.priority-label').removeClass('selected');
        $(this).next('.priority-label').addClass('selected');
    });

    // User selection feedback and tracking
    function updateSelectedUsers() {
        const selectedUsers = [];
        $('.user-option input[type="checkbox"]:checked').each(function() {
            const userName = $(this).data('user-name');
            const userId = $(this).val();
            selectedUsers.push({id: userId, name: userName});
        });

        // Update count
        $('#selected-count').text(selectedUsers.length);

        // Update selected users list
        const usersList = $('#selected-users-list');
        usersList.empty();

        if (selectedUsers.length > 0) {
            selectedUsers.forEach(function(user) {
                const userTag = $(`
                    <span class="selected-user-tag">
                        <i class="ti ti-user"></i>
                        ${user.name}
                    </span>
                `);
                usersList.append(userTag);
            });
        } else {
            usersList.html('<small class="text-muted fst-italic">{{ __("No users selected") }}</small>');
        }
    }

    // Initialize selected users display
    updateSelectedUsers();

    $('.user-option input[type="checkbox"]').on('change', function() {
        const label = $(this).next('.user-label');
        if (this.checked) {
            label.addClass('selected');
        } else {
            label.removeClass('selected');
        }

        // Update selected users display
        updateSelectedUsers();
    });

    // Form validation enhancement - only for non-AJAX forms
    $('form:not([data-ajax-form])').on('submit', function(e) {
        // Skip validation if this is being handled by AJAX
        if ($(this).closest('#commonModal').length > 0) {
            return true; // Let AJAX handler take over
        }

        const taskName = $('input[name="name"]').val().trim();
        if (!taskName) {
            e.preventDefault();
            $('input[name="name"]').focus().addClass('error');
            return false;
        }

        // Check if at least one user is selected
        const selectedUsers = $('.user-option input[type="checkbox"]:checked').length;
        if (selectedUsers === 0) {
            e.preventDefault();
            alert('{{ __("Please select at least one user to assign this task.") }}');
            return false;
        }
    });
});
</script>
