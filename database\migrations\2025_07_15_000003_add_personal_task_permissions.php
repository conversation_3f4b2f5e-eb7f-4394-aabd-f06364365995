<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class AddPersonalTaskPermissions extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $permissions = [
            'manage personal task',
            'create personal task',
            'edit personal task',
            'delete personal task',
            'view personal task',
            'create personal task comment',
            'edit personal task comment',
            'delete personal task comment',
            'create personal task file',
            'delete personal task file',
            'create personal task checklist',
            'edit personal task checklist',
            'delete personal task checklist',
            'manage personal task time tracking',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Assign permissions to company role by default
        $companyRole = Role::where('name', 'company')->first();
        if ($companyRole) {
            $companyRole->givePermissionTo($permissions);
        }

        // Assign basic permissions to employee role
        $employeeRole = Role::where('name', 'employee')->first();
        if ($employeeRole) {
            $employeePermissions = [
                'manage personal task',
                'create personal task',
                'edit personal task',
                'view personal task',
                'create personal task comment',
                'create personal task file',
                'create personal task checklist',
                'edit personal task checklist',
                'manage personal task time tracking',
            ];
            $employeeRole->givePermissionTo($employeePermissions);
        }

        // Assign basic permissions to hr role
        $hrRole = Role::where('name', 'hr')->first();
        if ($hrRole) {
            $hrPermissions = [
                'manage personal task',
                'create personal task',
                'edit personal task',
                'view personal task',
                'create personal task comment',
                'create personal task file',
                'create personal task checklist',
                'edit personal task checklist',
                'manage personal task time tracking',
            ];
            $hrRole->givePermissionTo($hrPermissions);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        $permissions = [
            'manage personal task',
            'create personal task',
            'edit personal task',
            'delete personal task',
            'view personal task',
            'create personal task comment',
            'edit personal task comment',
            'delete personal task comment',
            'create personal task file',
            'delete personal task file',
            'create personal task checklist',
            'edit personal task checklist',
            'delete personal task checklist',
            'manage personal task time tracking',
        ];

        foreach ($permissions as $permission) {
            Permission::where('name', $permission)->delete();
        }
    }
}
