<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Drop the existing foreign key constraint
            $table->dropForeign(['event_id']);
            
            // Add the correct foreign key constraint to calendar_events table
            $table->foreign('event_id')->references('id')->on('calendar_events')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            // Drop the calendar_events foreign key
            $table->dropForeign(['event_id']);
            
            // Restore the original events foreign key
            $table->foreign('event_id')->references('id')->on('events')->onDelete('cascade');
        });
    }
};
