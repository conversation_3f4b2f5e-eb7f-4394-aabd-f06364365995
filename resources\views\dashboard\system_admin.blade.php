@extends('layouts.admin')
@section('page-title')
    {{ __('System Admin Dashboard') }}
@endsection

@push('css-page')
    <style>
        .apexcharts-yaxis {
            transform: translate(20px, 0px) !important;
        }
    </style>
@endpush

@push('theme-script')
    <script src="{{ asset('assets/libs/apexcharts/dist/apexcharts.min.js') }}"></script>
@endpush

@section('content')

<!-- New Sub Accounts Section -->
<div class="row mb-4 gy-4">
    <div class="col-xxl-6 col-md-6 col-12">
        <div class="card h-100 shadow-sm">
            <div class="card-header d-flex justify-content-between align-items-center" style="background-color: #d4edda;">
                <h5 class="mb-0">
                    <i class="fas fa-gem" style="color: #006400; margin-left: 0.75rem;"></i> {{ __('Sub Accounts') }}
                </h5>
                <span class="h4 mb-0 text-primary">{{ $totalCompaniesCount }}</span>
            </div>
            <div class="card-body p-0">
                <table class="table mb-0">
                    <tbody>
                        <tr>
                            <td>All</td>
                            <td class="text-end text-primary">{{ $totalCompaniesCount }}</td>
                        </tr>
                        <tr>
                            <td>Active</td>
                            <td class="text-end text-primary">2</td>
                        </tr>
                        <tr>
                            <td>Cancelled</td>
                            <td class="text-end text-primary">5</td>
                        </tr>
                        <tr>
                            <td>Overdue</td>
                            <td class="text-end text-primary">5</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="col-xxl-6 col-md-6 col-12">
        <div class="card h-100 shadow-sm">
            <div class="card-header d-flex justify-content-between align-items-center" style="background-color: #d4edda;">
                <h5 class="mb-0">
                    <i class="fas fa-gem" style="color: #006400; margin-left: 0.75rem;"></i> {{ __('White Label Sub Accounts') }}
                </h5>
                <h3 class="mb-0">{{ $user['total_super_admins'] }}</h3>
            </div>
            <div class="card-body p-0">
                <table class="table mb-0">
                    <tbody>
                        <tr>
                            <td>All</td>
                            <td class="text-end text-primary">{{ $user['total_super_admins'] }}</td>
                        </tr>
                        <tr>
                            <td>Active</td>
                            <td class="text-end text-primary">0</td>
                        </tr>
                        <tr>
                            <td>Cancelled</td>
                            <td class="text-end text-primary">0</td>
                        </tr>
                        <tr>
                            <td>Overdue</td>
                            <td class="text-end text-primary">0</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<!-- Stats Section -->
<div class="row mb-4 gy-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-chart-bar" style="color: #006400; margin-right: 0.5rem;"></i>
                {{ __('Stats') }}
            </h5>
            </div>
            <div class="card-body p-0">
                <table class="table mb-0 text-center align-middle">
                    <thead>
                        <tr>
                            <th></th>
                            <th>{{ __('Sellers') }}</th>
                            <th>{{ __('MRR') }}</th>
                            <th>{{ __('ARR') }}</th>
                            <th>{{ __('Cancelled') }}</th>
                            <th>{{ __('Churn') }}</th>
                            <th>{{ __('Total Revenue') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @php
                            // Sub Accounts data
                            $subAccounts = \App\Models\User::where('type', 'sub_account')->get();
                            $subAccountCount = $subAccounts->count();
                            $subAccountMRR = 0;
                            $subAccountARR = 0;
                            $subAccountRevenue = 0;
                            foreach ($subAccounts as $sub) {
                                $plan = $sub->getPlan;
                                if ($plan && $plan->duration == 'month') {
                                    $subAccountMRR += $plan->price;
                                } elseif ($plan && $plan->duration == 'year') {
                                    $subAccountMRR += $plan->price / 12;
                                }
                                $subAccountRevenue += $plan ? $plan->price : 0;
                            }
                            $subAccountARR = $subAccountMRR * 12;
                        @endphp
                        <tr style="background: #2196f3; color: #fff;">
                            <td class="fw-bold text-start" style="padding-left: 2rem;">
                                <span style="vertical-align: middle;"><i class="fas fa-gem me-2" style="color: #b3e5fc;"></i>{{ __('Sub Accounts') }}</span>
                            </td>
                            <td>{{ $totalCompaniesCount }}</td>
                            <td>{{ Auth::user()->priceFormat($subAccountMRR) }}</td>
                            <td>{{ Auth::user()->priceFormat($subAccountARR) }}</td>
                            <td>0</td>
                            <td>0%</td>
                            <td>{{ Auth::user()->priceFormat($subAccountRevenue) }}</td>
                        </tr>
                        <tr style="background: #111; color: #fff;">
                            <td class="fw-bold text-start" style="padding-left: 2rem;">
                                <span style="vertical-align: middle;"><i class="fas fa-crown me-2" style="color: #ffd700;"></i>{{ __('White Label') }}</span>
                            </td>
                            <td>{{ $user['total_super_admins'] }}</td>
                            <td>{{ isset($whiteLabelMRR) ? number_format($whiteLabelMRR, 2) : '0.00' }}</td>
                            <td>{{ isset($whiteLabelARR) ? number_format($whiteLabelARR, 2) : '0.00' }}</td>
                            <td>0</td>
                            <td>0%</td>
                            <td>{{ isset($whiteLabelMRR) ? number_format($whiteLabelMRR, 2) : '0.00' }}</td>
                        </tr>
                        @php 
                        $total = $totalCompaniesCount + $user['total_super_admins'];
                        @endphp
                        <tr style="font-weight: bold; background: #f8f9fa; color: #111;">
                            <td class="text-start" style="padding-left: 2rem;">&nbsp;</td>
                            <td>{{ $total }}</td>
                            <td>0.00</td>
                            <td>0.00</td>
                            <td>0.00</td>
                            <td>0%</td>
                            <td>0.00</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Expenses and Net Profit Section (Modern UI) -->
@php
    $categories = [
        'Marketing',
        'Overheads',
        'Affiliates',
        'Others',
    ];
    $expenseAmounts = [];
    $totalExpense = 0;
    foreach ($categories as $catName) {
        $cat = \App\Models\ProductServiceCategory::where('created_by', Auth::user()->creatorId())
            ->where('type', 'expense')
            ->where('name', $catName)
            ->first();
        $amount = $cat ? $cat->expenseCategoryAmount() : 0;
        $expenseAmounts[$catName] = $amount;
        $totalExpense += $amount;
    }
    // Calculate total income
    $incomeCategories = \App\Models\ProductServiceCategory::where('created_by', Auth::user()->creatorId())
        ->where('type', 'income')
        ->get();
    $totalIncome = 0;
    foreach ($incomeCategories as $cat) {
        $totalIncome += $cat->incomeCategoryRevenueAmount();
    }
    $netProfit = $totalIncome - $totalExpense;
@endphp
<div class="row g-4 mt-2">
    <!-- Expenses Card -->
    <div class="col-12 col-xl-8">
        <div class="card shadow-sm border-0 h-100">
            <div class="card-header d-flex justify-content-between align-items-center bg-white border-0">
                <h5 class="mb-0">
                    <i class="fas fa-wallet" style="color: #006400; margin-right: 0.5rem;"></i>
                    {{ __('Expenses') }}
                </h5>
                <span class="h5 mb-0 text-primary">{{ Auth::user()->priceFormat($totalExpense) }}</span>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    @foreach($categories as $catName)
                        <div class="col-12 col-md-6 col-lg-3">
                            <div class="p-3 rounded bg-light h-100">
                                <div class="text-primary fw-semibold mb-1" style="font-size: 1.1rem;">{{ $catName }}</div>
                                <div class="fw-bold">{{ Auth::user()->priceFormat($expenseAmounts[$catName]) }}</div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- Net Profit Card -->
    <div class="col-12 col-xl-4">
        <div class="card shadow-sm border-0 h-100">
            <div class="card-header bg-white border-0">
                <h5 class="mb-0">
                    <i class="fas fa-coins" style="color: #006400; margin-right: 0.5rem;"></i>
                    {{ __('Net Profit') }}
                </h5>
            </div>
            <div class="card-body d-flex flex-column align-items-center justify-content-center">
                <div class="h2 fw-bold text-center mb-3">{{ Auth::user()->priceFormat($netProfit) }}</div>
                <div class="d-flex align-items-center mb-3 w-100 justify-content-center">
                    <span class="fw-bold text-success me-2" style="font-size: 1.2rem;">Good</span>
                    <!-- Simple Gauge Representation (SVG) -->
                    <svg width="80" height="40" viewBox="0 0 80 40">
                        <path d="M10,40 A30,30 0 0,1 70,40" fill="none" stroke="#eee" stroke-width="10"/>
                        <path d="M10,40 A30,30 0 0,1 55,15" fill="none" stroke="#ffc107" stroke-width="10"/>
                        <path d="M55,15 A30,30 0 0,1 70,40" fill="none" stroke="#4caf50" stroke-width="10"/>
                        <circle cx="55" cy="15" r="5" fill="#4caf50"/>
                    </svg>
                </div>
                <div class="w-100">
                    <div class="d-flex align-items-center justify-content-between small mb-1">
                        <span class="d-inline-block rounded-circle me-1" style="width:12px;height:12px;background:#f44336;"></span>
                        <span>0-20%</span>
                        <span>Low</span>
                    </div>
                    <div class="d-flex align-items-center justify-content-between small mb-1">
                        <span class="d-inline-block rounded-circle me-1" style="width:12px;height:12px;background:#ffc107;"></span>
                        <span>20% - 40%</span>
                        <span>Average</span>
                    </div>
                    <div class="d-flex align-items-center justify-content-between small mb-1">
                        <span class="d-inline-block rounded-circle me-1" style="width:12px;height:12px;background:#4caf50;"></span>
                        <span>40% - 60%</span>
                        <span>Good</span>
                    </div>
                    <div class="d-flex align-items-center justify-content-between small">
                        <span class="d-inline-block rounded-circle me-1" style="width:12px;height:12px;background:#2196f3;"></span>
                        <span>&gt;60%</span>
                        <span>Excellent</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection
