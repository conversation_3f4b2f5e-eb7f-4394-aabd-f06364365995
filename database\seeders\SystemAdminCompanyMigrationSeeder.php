<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\SystemAdminCompany;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class SystemAdminCompanyMigrationSeeder extends Seeder
{
    /**
     * Run the database seeder.
     * This seeder is specifically designed to migrate existing system admins to the new company structure.
     */
    public function run(): void
    {
        $this->command->info('Starting System Admin Company Migration...');

        // Check if required tables exist
        if (!Schema::hasTable('system_admin_companies')) {
            $this->command->error('system_admin_companies table does not exist. Please run the migration first.');
            return;
        }

        if (!Schema::hasColumn('users', 'system_admin_company_id')) {
            $this->command->error('system_admin_company_id column does not exist in users table. Please run the migration first.');
            return;
        }

        // Start transaction for data integrity
        DB::beginTransaction();

        try {
            $this->migrateSystemAdmins();
            $this->migrateSuperAdmins();
            $this->validateMigration();

            DB::commit();
            $this->command->info('Migration completed successfully!');
        } catch (\Exception $e) {
            DB::rollBack();
            $this->command->error('Migration failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Migrate existing system admins to have companies
     */
    private function migrateSystemAdmins()
    {
        $systemAdmins = User::where('type', 'system admin')
                           ->whereNull('system_admin_company_id')
                           ->get();

        $this->command->info('Found ' . $systemAdmins->count() . ' system admin(s) without companies.');

        foreach ($systemAdmins as $systemAdmin) {
            $companyName = $this->generateCompanyName($systemAdmin);
            
            // Check if a company with this name already exists for this system admin
            $existingCompany = SystemAdminCompany::where('created_by', $systemAdmin->id)
                                                ->where('name', $companyName)
                                                ->first();

            if ($existingCompany) {
                $company = $existingCompany;
                $this->command->info('Using existing company "' . $company->name . '" for system admin "' . $systemAdmin->name . '"');
            } else {
                // Create new company
                $company = SystemAdminCompany::create([
                    'name' => $companyName,
                    'description' => $this->generateCompanyDescription($systemAdmin),
                    'email' => $systemAdmin->email,
                    'phone' => null,
                    'address' => null,
                    'logo' => null,
                    'is_active' => true,
                    'created_by' => $systemAdmin->id,
                ]);

                $this->command->info('Created company "' . $company->name . '" for system admin "' . $systemAdmin->name . '"');
            }

            // Associate system admin with company
            $systemAdmin->update(['system_admin_company_id' => $company->id]);
        }
    }

    /**
     * Migrate existing super admins to be part of their creator's company
     */
    private function migrateSuperAdmins()
    {
        $superAdmins = User::where('type', 'super admin')
                          ->whereNull('system_admin_company_id')
                          ->whereNotNull('created_by')
                          ->with('creator')
                          ->get();

        $this->command->info('Found ' . $superAdmins->count() . ' super admin(s) without companies.');

        foreach ($superAdmins as $superAdmin) {
            $creator = $superAdmin->creator;
            
            if (!$creator || $creator->type !== 'system admin') {
                $this->command->warn('Super admin "' . $superAdmin->name . '" has no valid system admin creator. Skipping...');
                continue;
            }

            if (!$creator->system_admin_company_id) {
                $this->command->warn('System admin creator "' . $creator->name . '" has no company. Skipping super admin "' . $superAdmin->name . '"...');
                continue;
            }

            // Associate super admin with creator's company
            $superAdmin->update(['system_admin_company_id' => $creator->system_admin_company_id]);
            
            $company = SystemAdminCompany::find($creator->system_admin_company_id);
            $this->command->info('Added super admin "' . $superAdmin->name . '" to company "' . $company->name . '"');
        }
    }

    /**
     * Validate the migration results
     */
    private function validateMigration()
    {
        $this->command->info('Validating migration results...');

        // Check system admins
        $systemAdminsWithoutCompany = User::where('type', 'system admin')
                                         ->whereNull('system_admin_company_id')
                                         ->count();

        if ($systemAdminsWithoutCompany > 0) {
            $this->command->warn($systemAdminsWithoutCompany . ' system admin(s) still without companies.');
        }

        // Check super admins
        $superAdminsWithoutCompany = User::where('type', 'super admin')
                                        ->whereNull('system_admin_company_id')
                                        ->count();

        if ($superAdminsWithoutCompany > 0) {
            $this->command->warn($superAdminsWithoutCompany . ' super admin(s) still without companies.');
        }

        // Display final statistics
        $totalCompanies = SystemAdminCompany::count();
        $systemAdminsWithCompanies = User::where('type', 'system admin')
                                        ->whereNotNull('system_admin_company_id')
                                        ->count();
        $superAdminsWithCompanies = User::where('type', 'super admin')
                                       ->whereNotNull('system_admin_company_id')
                                       ->count();

        $this->command->info('Final Statistics:');
        $this->command->info('- Total companies: ' . $totalCompanies);
        $this->command->info('- System admins with companies: ' . $systemAdminsWithCompanies);
        $this->command->info('- Super admins with companies: ' . $superAdminsWithCompanies);
    }

    /**
     * Generate a company name for a system admin
     */
    private function generateCompanyName(User $systemAdmin): string
    {
        // Use existing company_name field if available
        if (!empty($systemAdmin->company_name)) {
            return $systemAdmin->company_name;
        }

        // Generate from user name
        $baseName = $systemAdmin->name . ' Company';
        
        // Ensure uniqueness
        $counter = 1;
        $companyName = $baseName;
        
        while (SystemAdminCompany::where('name', $companyName)->exists()) {
            $companyName = $baseName . ' ' . $counter;
            $counter++;
        }

        return $companyName;
    }

    /**
     * Generate a company description for a system admin
     */
    private function generateCompanyDescription(User $systemAdmin): string
    {
        // Use existing company_description field if available
        if (!empty($systemAdmin->company_description)) {
            return $systemAdmin->company_description;
        }

        return 'Company managed by ' . $systemAdmin->name . ' (System Administrator)';
    }
}
