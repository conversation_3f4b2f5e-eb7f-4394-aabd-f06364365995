<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PersonalTaskFile extends Model
{
    protected $fillable = [
        'task_id',
        'file',
        'name',
        'extension',
        'file_size',
        'created_by',
    ];

    /**
     * Get the task that owns the file
     */
    public function task()
    {
        return $this->belongsTo('App\Models\PersonalTask', 'task_id');
    }

    /**
     * Get the user that created the file
     */
    public function user()
    {
        return $this->belongsTo('App\Models\User', 'created_by');
    }
}
