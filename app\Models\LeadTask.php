<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;

class LeadTask extends Model
{
    protected $fillable = [
        'lead_id','name','date','time','priority','status','assign_to'
    ];

    public static $priorities = [
        1 => 'Low',
        2 => 'Medium',
        3 => 'High',
    ];
    public static $status = [
        0 => 'On Going',
        1 => 'Completed'
    ];

    public function assignedUser()
    {
        return $this->belongsTo(User::class, 'assign_to');
    }
}
