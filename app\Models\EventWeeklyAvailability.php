<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EventWeeklyAvailability extends Model
{
    protected $table ='event_weekly_availability';
    protected $fillable = [
        'calendar_event_id',
        'day_of_week',
        'start_time',
        'end_time'
    ];

    protected $casts = [
    'start_time' => 'datetime',
    'end_time' => 'datetime',
];


    public function calendarEvent(): BelongsTo
    {
        return $this->belongsTo(CalendarEvent::class);
    }

    public static $daysOfWeek = [
        'monday' => 'Monday',
        'tuesday' => 'Tuesday',
        'wednesday' => 'Wednesday',
        'thursday' => 'Thursday',
        'friday' => 'Friday',
        'saturday' => 'Saturday',
        'sunday' => 'Sunday'
    ];

    public function getDayNameAttribute()
    {
        return self::$daysOfWeek[$this->day_of_week] ?? $this->day_of_week;
    }
}