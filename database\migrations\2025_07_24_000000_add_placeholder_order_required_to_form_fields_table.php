<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('form_fields', function (Blueprint $table) {
            if (!Schema::hasColumn('form_fields', 'placeholder')) {
                $table->string('placeholder')->nullable()->after('type');
            }
            if (!Schema::hasColumn('form_fields', 'required')) {
                $table->boolean('required')->default(false)->after('placeholder');
            }
            if (!Schema::hasColumn('form_fields', 'order')) {
                $table->integer('order')->default(0)->after('required');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('form_fields', function (Blueprint $table) {
            if (Schema::hasColumn('form_fields', 'placeholder')) {
                $table->dropColumn('placeholder');
            }
            if (Schema::hasColumn('form_fields', 'required')) {
                $table->dropColumn('required');
            }
            if (Schema::hasColumn('form_fields', 'order')) {
                $table->dropColumn('order');
            }
        });
    }
};
