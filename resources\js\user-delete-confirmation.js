// Wait for both DOM and all scripts to be loaded
window.addEventListener('load', function() {
    console.log('Force delete script loaded');
    
    // Handle regular delete confirmation
    document.querySelectorAll('.bs-pass-para').forEach(function (el) {
        el.addEventListener('click', function (e) {
            e.preventDefault();
            var username = el.getAttribute('data-username');
            var form = el.closest('form');
            if (username) {
                // Show prompt to type the username
                var input = prompt('Please type the super admin name to confirm deletion:');
                if (input === username) {
                    form.submit();
                } else if (input !== null) {
                    alert('The name you typed does not match. Deletion cancelled.');
                }
            } else {
                // For other users, confirm normally
                if (confirm('Are you sure you want to delete this user?')) {
                    form.submit();
                }
            }
        });
    });

    // Handle force delete confirmation with modal
    document.addEventListener('click', function(e) {
        if (e.target.closest('.bs-pass-para-force')) {
            console.log('Force delete button clicked');
            e.preventDefault();
            e.stopPropagation();
            
            var el = e.target.closest('.bs-pass-para-force');
            var form = el.closest('form');
            var confirmText = el.getAttribute('data-confirm-text') || 'This will force delete the user even if modules have dependency errors. Are you sure?';
            
            console.log('Form found:', form);
            console.log('Modal element:', document.getElementById('forceDeleteModal'));
            
            // Check if modal exists
            var modalElement = document.getElementById('forceDeleteModal');
            if (!modalElement) {
                console.error('Modal element not found!');
                alert('Modal not found. Please refresh the page and try again.');
                return;
            }
            
            // Set the confirmation text in the modal
            var confirmTextElement = document.getElementById('forceDeleteConfirmText');
            if (confirmTextElement) {
                confirmTextElement.textContent = confirmText;
            }
            
            // Clear any previous input and error states
            var input = document.getElementById('forceDeleteInput');
            var errorMsg = document.getElementById('forceDeleteError');
            var confirmBtn = document.getElementById('forceDeleteConfirmBtn');
            
            if (input) {
                input.value = '';
                input.classList.remove('is-invalid', 'is-valid');
            }
            if (errorMsg) {
                errorMsg.style.display = 'none';
            }
            if (confirmBtn) {
                confirmBtn.disabled = true;
            }
            
            // Store the form reference for later submission
            modalElement.setAttribute('data-form-id', form.id);
            
            // Show the modal using jQuery if Bootstrap is not available
            if (typeof bootstrap !== 'undefined') {
                console.log('Using Bootstrap 5 modal');
                var modal = new bootstrap.Modal(modalElement);
                modal.show();
                
                // Focus on the input field after modal is shown
                modalElement.addEventListener('shown.bs.modal', function() {
                    if (input) input.focus();
                }, { once: true });
            } else if (typeof $ !== 'undefined') {
                console.log('Using jQuery modal');
                $(modalElement).modal('show');
                $(modalElement).on('shown.bs.modal', function() {
                    if (input) input.focus();
                });
            } else {
                console.error('Neither Bootstrap nor jQuery found');
                alert('Modal library not found. Please refresh the page and try again.');
            }
        }
    });

    // Handle input validation in the force delete modal
    var inputElement = document.getElementById('forceDeleteInput');
    if (inputElement) {
        inputElement.addEventListener('input', function () {
            var input = this;
            var confirmBtn = document.getElementById('forceDeleteConfirmBtn');
            var errorMsg = document.getElementById('forceDeleteError');
            
            if (input.value === 'DELETE') {
                input.classList.remove('is-invalid');
                input.classList.add('is-valid');
                if (errorMsg) errorMsg.style.display = 'none';
                if (confirmBtn) confirmBtn.disabled = false;
            } else {
                input.classList.remove('is-valid');
                if (input.value.length > 0) {
                    input.classList.add('is-invalid');
                    if (errorMsg) errorMsg.style.display = 'block';
                } else {
                    input.classList.remove('is-invalid');
                    if (errorMsg) errorMsg.style.display = 'none';
                }
                if (confirmBtn) confirmBtn.disabled = true;
            }
        });
    }

    // Handle force delete confirmation button click
    var confirmBtnElement = document.getElementById('forceDeleteConfirmBtn');
    if (confirmBtnElement) {
        confirmBtnElement.addEventListener('click', function () {
            var input = document.getElementById('forceDeleteInput');
            if (input && input.value === 'DELETE') {
                var modalElement = document.getElementById('forceDeleteModal');
                var formId = modalElement.getAttribute('data-form-id');
                var form = document.getElementById(formId);
                
                console.log('Submitting form:', formId, form);
                
                // Hide modal
                if (typeof bootstrap !== 'undefined') {
                    var modal = bootstrap.Modal.getInstance(modalElement);
                    if (modal) modal.hide();
                } else if (typeof $ !== 'undefined') {
                    $(modalElement).modal('hide');
                }
                
                // Submit form
                if (form) {
                    form.submit();
                } else {
                    console.error('Form not found:', formId);
                }
            }
        });
    }
});
