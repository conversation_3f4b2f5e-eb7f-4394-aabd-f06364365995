<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectFolder extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'project_id',
        'parent_folder_id',
        'icon',
        'color',
        'description',
        'created_by'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'total_files_count',
        'folder_size'
    ];

    /**
     * Get the project that owns the folder
     */
    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the parent folder
     */
    public function parentFolder()
    {
        return $this->belongsTo(ProjectFolder::class, 'parent_folder_id');
    }

    /**
     * Get the child folders
     */
    public function childFolders()
    {
        return $this->hasMany(ProjectFolder::class, 'parent_folder_id');
    }

    /**
     * Get all files in this folder
     */
    public function files()
    {
        return $this->hasMany(ProjectFile::class, 'folder_id');
    }

    /**
     * Get the user who created the folder
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the folder path (breadcrumb)
     */
    public function getPathAttribute()
    {
        $path = [];
        $folder = $this;
        
        while ($folder) {
            array_unshift($path, $folder);
            $folder = $folder->parentFolder;
        }
        
        return $path;
    }

    /**
     * Get total files count (including subfolders)
     */
    public function getTotalFilesCountAttribute()
    {
        // Direct files in this folder
        $count = $this->files()->count();

        // Files in all subfolders (recursive)
        $subfolderIds = $this->getAllSubfolderIds();
        if (!empty($subfolderIds)) {
            $count += ProjectFile::whereIn('folder_id', $subfolderIds)->count();
        }

        return $count;
    }

    /**
     * Get all subfolder IDs recursively
     */
    private function getAllSubfolderIds()
    {
        $ids = [];
        $this->collectSubfolderIds($this->id, $ids);
        return $ids;
    }

    /**
     * Recursively collect subfolder IDs
     */
    private function collectSubfolderIds($folderId, &$ids)
    {
        $subfolders = ProjectFolder::where('parent_folder_id', $folderId)->pluck('id');
        foreach ($subfolders as $subfolderId) {
            $ids[] = $subfolderId;
            $this->collectSubfolderIds($subfolderId, $ids);
        }
    }

    /**
     * Get folder size in bytes
     */
    public function getFolderSizeAttribute()
    {
        $size = $this->files()->sum('file_size');
        
        foreach ($this->childFolders as $childFolder) {
            $size += $childFolder->folder_size;
        }
        
        return $size;
    }

    /**
     * Check if user can access this folder
     */
    public function canAccess($user)
    {
        // Check if user has access to the project
        return $this->project->users()->where('user_id', $user->id)->exists() || 
               $this->project->client_id == $user->id ||
               $user->type == 'super admin' ||
               $user->type == 'company';
    }
}
