<?php

/**
 * Booking & Appointment API Test Script
 * 
 * This script tests the Booking & Appointment API endpoints with SSO token authentication.
 * Run this script from the command line or browser to test the API.
 */

// Configuration
$appUrl = $_ENV['APP_URL'] ?? 'http://localhost:8000';
$baseUrl = $appUrl . '/api'; // Uses APP_URL environment variable
$testEmail = '<EMAIL>'; // Adjust this to a valid user email
$testPassword = '<EMAIL>'; // Adjust this to the user's password

echo "=== Booking & Appointment API Test Script ===\n\n";

// Step 1: Login to get authentication token
echo "Step 1: Logging in to get authentication token...\n";
$loginData = [
    'email' => $testEmail,
    'password' => $testPassword
];

$loginResponse = makeRequest('POST', $baseUrl . '/login', $loginData);
if (!$loginResponse || !isset($loginResponse['data']['token'])) {
    echo "❌ Login failed. Please check your credentials.\n";
    if ($loginResponse) {
        echo "Response: " . json_encode($loginResponse, JSON_PRETTY_PRINT) . "\n";
    }
    exit(1);
}

$authToken = $loginResponse['data']['token'];
echo "✅ Login successful. Auth token obtained.\n\n";

// Step 2: Generate SSO token
echo "Step 2: Generating SSO token...\n";
$ssoResponse = makeRequest('POST', $baseUrl . '/generate-sso-token', [], [
    'Authorization: Bearer ' . $authToken,
    'Content-Type: application/json'
]);

if (!$ssoResponse || !isset($ssoResponse['token'])) {
    echo "❌ SSO token generation failed.\n";
    if ($ssoResponse) {
        echo "Response: " . json_encode($ssoResponse, JSON_PRETTY_PRINT) . "\n";
    }
    exit(1);
}

$ssoToken = $ssoResponse['token'];
echo "✅ SSO token generated successfully.\n\n";

// Step 3: Test Booking & Appointment API endpoints
echo "Step 3: Testing Booking & Appointment API endpoints...\n\n";

$headers = [
    'Authorization: Bearer ' . $ssoToken,
    'Content-Type: application/json'
];

// Test endpoints
$testEndpoints = [
    ['GET', '/booking/bookings', null, 'Get all bookings'],
    ['GET', '/booking/appointment-bookings', null, 'Get all appointment bookings'],
    ['GET', '/booking/calendar-events', null, 'Get all calendar events'],
];

foreach ($testEndpoints as $test) {
    [$method, $endpoint, $data, $description] = $test;
    
    echo "Testing: {$description}\n";
    echo "Endpoint: {$method} {$endpoint}\n";
    
    $response = makeRequest($method, $baseUrl . $endpoint, $data, $headers);
    
    if ($response && isset($response['success']) && $response['success']) {
        echo "✅ Success: " . ($response['message'] ?? 'Request completed') . "\n";
        if (isset($response['data'])) {
            echo "   Data count: " . (is_array($response['data']) ? count($response['data']) : 'N/A') . "\n";
        }
    } else {
        echo "❌ Failed: " . ($response['error'] ?? $response['message'] ?? 'Unknown error') . "\n";
    }
    echo "\n";
}

// Step 4: Test creating a new calendar event
echo "Step 4: Testing calendar event creation...\n";

$eventData = [
    'title' => 'Test Event from API',
    'start_date' => '2024-01-01',
    'end_date' => '2024-12-31',
    'duration' => 30,
    'booking_per_slot' => 1,
    'minimum_notice' => 60,
    'description' => 'This is a test event created via API',
    'location' => 'Online',
    'meet_link' => 'https://meet.google.com/test',
    'require_name' => true,
    'require_email' => true,
    'require_phone' => false,
    'weekly_availability' => [
        'monday' => ['09:00-17:00'],
        'tuesday' => ['09:00-17:00'],
        'wednesday' => ['09:00-17:00'],
        'thursday' => ['09:00-17:00'],
        'friday' => ['09:00-17:00']
    ]
];

$createResponse = makeRequest('POST', $baseUrl . '/booking/calendar-events', $eventData, $headers);

if ($createResponse && isset($createResponse['success']) && $createResponse['success']) {
    echo "✅ Calendar event created successfully!\n";
    echo "   Event ID: " . $createResponse['data']['id'] . "\n";
    echo "   Event Title: " . $createResponse['data']['title'] . "\n";
    
    $eventId = $createResponse['data']['id'];
    
    // Step 5: Test creating a booking for this event
    echo "\nStep 5: Testing booking creation...\n";
    
    $bookingData = [
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'phone' => '+1234567890',
        'event_id' => $eventId,
        'date' => '2024-02-15',
        'time' => '10:00',
        'custom_fields' => ['notes'],
        'custom_fields_value' => ['This is a test booking via API']
    ];
    
    $bookingResponse = makeRequest('POST', $baseUrl . '/booking/bookings', $bookingData, $headers);
    
    if ($bookingResponse && isset($bookingResponse['success']) && $bookingResponse['success']) {
        echo "✅ Booking created successfully!\n";
        echo "   Booking ID: " . $bookingResponse['data']['id'] . "\n";
        echo "   Booking Name: " . $bookingResponse['data']['name'] . "\n";
    } else {
        echo "❌ Booking creation failed: " . ($bookingResponse['error'] ?? 'Unknown error') . "\n";
    }
    
    // Step 6: Test creating an appointment booking for this event
    echo "\nStep 6: Testing appointment booking creation...\n";
    
    $appointmentBookingData = [
        'event_id' => $eventId,
        'event_location' => 'Online Meeting',
        'event_location_value' => 'https://meet.google.com/test-meeting',
        'event_date' => '2024-02-20',
        'time_zone' => 'UTC',
        'time_slots' => '14:00-14:30'
    ];
    
    $appointmentResponse = makeRequest('POST', $baseUrl . '/booking/appointment-bookings', $appointmentBookingData, $headers);
    
    if ($appointmentResponse && isset($appointmentResponse['success']) && $appointmentResponse['success']) {
        echo "✅ Appointment booking created successfully!\n";
        echo "   Appointment Booking ID: " . $appointmentResponse['data']['id'] . "\n";
        echo "   Event Date: " . $appointmentResponse['data']['event_date'] . "\n";
    } else {
        echo "❌ Appointment booking creation failed: " . ($appointmentResponse['error'] ?? 'Unknown error') . "\n";
    }
    
} else {
    echo "❌ Calendar event creation failed: " . ($createResponse['error'] ?? 'Unknown error') . "\n";
    echo "Skipping booking and appointment booking tests.\n";
}

echo "\n=== Test completed ===\n";

/**
 * Make HTTP request
 */
function makeRequest($method, $url, $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    if (!empty($headers)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    }
    
    switch (strtoupper($method)) {
        case 'POST':
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
            break;
        case 'PUT':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
            break;
        case 'DELETE':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
            break;
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_error($ch)) {
        echo "cURL Error: " . curl_error($ch) . "\n";
        curl_close($ch);
        return null;
    }
    
    curl_close($ch);
    
    $decodedResponse = json_decode($response, true);
    
    if ($httpCode >= 400) {
        echo "HTTP Error {$httpCode}: " . ($decodedResponse['message'] ?? $response) . "\n";
        if ($decodedResponse) {
            echo "Full response: " . json_encode($decodedResponse, JSON_PRETTY_PRINT) . "\n";
        }
    }
    
    return $decodedResponse;
}
