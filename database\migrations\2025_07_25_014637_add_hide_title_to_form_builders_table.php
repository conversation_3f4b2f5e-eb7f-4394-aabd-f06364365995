<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('form_builders', function (Blueprint $table) {
            $table->boolean('hide_title')->default(false)->after('form_styles');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('form_builders', function (Blueprint $table) {
            $table->dropColumn('hide_title');
        });
    }
};
