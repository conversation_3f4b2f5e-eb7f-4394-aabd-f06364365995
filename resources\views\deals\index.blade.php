@extends('layouts.admin')
@section('page-title')
    {{ __('Manage Deals') }} @if ($pipeline)
        - {{ $pipeline->name }}
    @endif
@endsection

@push('css-page')
    <link rel="stylesheet" href="{{ asset('css/summernote/summernote-bs4.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/plugins/dragula.min.css') }}" id="main-style-link">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
            --kanban-bg: rgba(255,255,255,0.7);
            --kanban-blur: 16px;
            --kanban-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.10);
            --kanban-border: 1px solid rgba(255,255,255,0.18);
            --kanban-radius: 18px;
            --kanban-gradient: linear-gradient(135deg, rgba(245,247,250,0.8) 0%, rgba(230,240,255,0.7) 100%);
            --kanban-label-radius: 12px;
            --kanban-label-font: 13px;
            --kanban-label-padding: 3px 12px;
        }
        
        /* Smoother scrolling for the kanban board */
        html {
            scroll-behavior: smooth;
        }
        
        /* Container for the kanban board */
        .horizontal-scroll-cards {
            position: relative;
            margin: 0 -12px;
            padding: 12px 0;
        }
        .kanban-wrapper {
            display: flex;
            gap: 24px;
            overflow-x: auto;
            padding-bottom: 16px;
            padding: 24px;
            margin: -12px;
            scrollbar-width: thin;
            -webkit-overflow-scrolling: touch;
        }
        .kanban-wrapper::-webkit-scrollbar {
            height: 8px;
        }
        .kanban-wrapper::-webkit-scrollbar-thumb {
            background: #e0e7ef;
            border-radius: 8px;
        }
        .kanban-col {
            min-width: 320px;
            width: 320px;
            flex: 0 0 320px;
            background: var(--kanban-gradient);
            border-radius: var(--kanban-radius);
            box-shadow: var(--kanban-shadow);
            border: var(--kanban-border);
            backdrop-filter: blur(var(--kanban-blur));
            padding: 0 0 12px 0;
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        .kanban-header {
            padding: 20px 20px 10px 20px;
            border-bottom: 1px solid #f0f0f0;
            background: transparent;
            border-radius: var(--kanban-radius) var(--kanban-radius) 0 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .kanban-header h4 {
            font-size: 1.1rem;
            font-weight: 700;
            margin: 0;
        }
        .kanban-header .count {
            background: #e3e9f7;
            color: #3a3a3a;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1rem;
        }
        .sales-item-wrp {
            padding: 16px 12px 0 12px;
            min-height: 80px;
            max-height: 480px; /* Show ~3 cards, then scroll */
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: #e0e7ef #f8fafc;
            transition: max-height 0.2s;
        }
        .sales-item-wrp::-webkit-scrollbar {
            width: 8px;
        }
        .sales-item-wrp::-webkit-scrollbar-thumb {
            background: #e0e7ef;
            border-radius: 8px;
        }
        .sales-item-wrp::-webkit-scrollbar-track {
            background: #f8fafc;
            border-radius: 8px;
        }
        .kanban-card {
            background: var(--kanban-bg);
            border-radius: var(--kanban-radius);
            box-shadow: 0 2px 12px 0 rgba(31, 38, 135, 0.08);
            border: var(--kanban-border);
            margin-bottom: 18px;
            padding: 18px 16px 12px 16px;
            position: relative;
            transition: box-shadow 0.2s, transform 0.2s;
            cursor: grab;
            backdrop-filter: blur(var(--kanban-blur));
        }
        .kanban-card.dragging {
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.18);
            transform: scale(1.03);
            z-index: 10;
        }
        .kanban-card .card-top {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
        }
        .kanban-card .deal-title {
            font-size: 1.05rem;
            font-weight: 600;
            color: #222;
            margin-bottom: 2px;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        .kanban-card .deal-title i {
            color: #6c63ff;
            font-size: 1rem;
        }
        .kanban-card .badge-wrp {
            margin-top: 6px;
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }
        .kanban-label {
            border-radius: var(--kanban-label-radius);
            font-size: var(--kanban-label-font);
            padding: var(--kanban-label-padding);
            font-weight: 500;
            color: #fff;
            background: #6c63ff;
            opacity: 0.95;
        }
        .kanban-label.bg-light-success { background: #28a745; }
        .kanban-label.bg-light-danger { background: #dc3545; }
        .kanban-label.bg-light-warning { background: #ffc107; color: #222; }
        .kanban-label.bg-light-info { background: #17a2b8; }
        .kanban-label.bg-light-primary { background: #6c63ff; }
        .kanban-label.bg-light-secondary { background: #6c757d; }
        .kanban-label.bg-light-brown { background: #a17a69; }
        .kanban-label.bg-light-blue { background: #007bff; }
        .kanban-label.bg-light-purple { background: #6f42c1; }
        .kanban-card .contact-info {
            margin-top: 10px;
            display: flex;
            flex-direction: column;
            gap: 6px;
        }
        .kanban-card .contact-item {
            display: flex;
            align-items: center;
            gap: 7px;
            font-size: 14px;
            color: #555;
        }
        .kanban-card .contact-item i {
            color: #888;
        }
        .kanban-card .card-bottom {
            margin-top: 14px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .kanban-card .communication-buttons {
            display: flex;
            gap: 8px;
        }
        .kanban-card .communication-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 5px rgba(0,0,0,0.12);
            background: #6c63ff;
        }
        .kanban-card .communication-btn.call { background: #28a745; }
        .kanban-card .communication-btn.sms { background: #17a2b8; }
        .kanban-card .communication-btn.email { background: #6f42c1; }
        .kanban-card .communication-btn i {
            font-size: 14px;
        }
        .kanban-card .user-group {
            display: flex;
            gap: 4px;
        }
        .kanban-card .user-group i {
            font-size: 22px;
            color: #6c63ff;
        }
        .kanban-card .drag-handle {
            cursor: grab;
            color: #bdbdbd;
            font-size: 18px;
            margin-right: 8px;
            transition: color 0.2s;
        }
        .kanban-card .drag-handle:hover {
            color: #6c63ff;
        }
        /* Responsive */
        @media (max-width: 900px) {
            .kanban-wrapper {
                gap: 16px;
                padding: 16px;
            }
            .kanban-col {
                min-width: 280px;
                width: 280px;
                flex: 0 0 280px;
            }
            .sales-item-wrp {
                max-height: 340px;
            }
        }
        @media (max-width: 600px) {
            .kanban-wrapper {
                gap: 12px;
                padding: 12px;
            }
            .kanban-col {
                min-width: 260px;
                width: 260px;
                flex: 0 0 260px;
                padding: 0;
            }
            .kanban-header {
                padding: 14px 10px 8px 10px;
            }
            .sales-item-wrp {
                max-height: 220px;
                padding: 8px 4px 0 4px;
            }
            .kanban-card {
                padding: 12px 8px 8px 8px;
            }
        }
    </style>
    <style>
        .modern-comm-modal {
            background: rgba(255,255,255,0.85);
            border-radius: 20px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.18);
            border: 1px solid rgba(255,255,255,0.18);
            backdrop-filter: blur(12px);
        }
        .comm-deal-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6c63ff 0%, #17a2b8 100%);
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 700;
            box-shadow: 0 2px 8px rgba(31,38,135,0.10);
        }
        .comm-deal-name {
            font-weight: 600;
            font-size: 1.1rem;
        }
        .comm-deal-contact {
            font-size: 0.95rem;
        }
        .communication-options-list {
            display: flex;
            flex-direction: column;
            gap: 14px;
            margin-top: 10px;
        }
        .communication-option-item {
            display: flex;
            align-items: center;
            padding: 14px 18px;
            border-radius: 12px;
            background: rgba(245,247,250,0.95);
            color: #333;
            text-decoration: none;
            font-size: 1.08rem;
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(31,38,135,0.06);
            transition: all 0.2s;
            border: 1px solid #f0f0f0;
        }
        .communication-option-item:hover {
            background: linear-gradient(135deg, #e3e9f7 0%, #f8fafc 100%);
            transform: translateX(5px) scale(1.03);
            box-shadow: 0 4px 16px rgba(31,38,135,0.10);
        }
        .communication-option-item i {
            font-size: 1.5rem;
            margin-right: 18px;
            width: 32px;
            text-align: center;
        }
        #whatsapp-option i { color: #25D366; }
        #default-email-option i { color: #007BFF; }
        #cloud-email-option i { color: #6f42c1; }
        #sms-option i { color: #17a2b8; }
        @media (max-width: 600px) {
            .modern-comm-modal {
                border-radius: 10px;
            }
            .communication-option-item {
                padding: 12px 10px;
                font-size: 1rem;
            }
            .comm-deal-avatar {
                width: 38px;
                height: 38px;
                font-size: 1.1rem;
            }
        }
    </style>
    <style>
.deal-summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}
.deal-summary-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 24px 0 rgba(31, 38, 135, 0.10);
  padding: 1rem 0.7rem 0.7rem 0.7rem;
  position: relative;
  transition: box-shadow 0.2s, transform 0.2s;
  min-height: 130px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  overflow: hidden;
}
.deal-summary-card::after {
  content: "";
  position: absolute;
  right: 0; bottom: 0;
  width: 60px; height: 60px;
  background: #f3f4fa;
  border-radius: 50% 0 0 0;
  z-index: 0;
}
.deal-summary-icon {
  width: 44px; height: 44px;
  display: flex; align-items: center; justify-content: center;
  background: linear-gradient(135deg, #6c63ff 0%, #17a2b8 100%);
  border-radius: 12px;
  margin-bottom: 0.4rem;
  z-index: 1;
}
.deal-summary-title {
  top: 1.2rem;
  font-size: 0.92rem;
  font-weight: 700;
  color: #222;
  margin-bottom: 0.3rem;
  z-index: 1;
}
.deal-summary-value {
  font-size: 1.1rem;
  font-weight: 800;
  color: #6c63ff;
  margin-bottom: 0.5rem;
  z-index: 1;
}
.deal-summary-desc {
  font-size: 1rem;
  color: #888;
  z-index: 1;
}
.deal-summary-amount {
  position: absolute;
  top: 1.2rem;
  right: 1.5rem;
  font-size: 0.85rem;
  font-weight: 700;
  color: #28a745;
  background: #eafbe7;
  padding: 0.13em 0.5em;
  border-radius: 1.2em;
  z-index: 2;
  box-shadow: 0 2px 8px rgba(31, 135, 38, 0.06);
}
@media (max-width: 600px) {
  .deal-summary-card {
    padding: 1.2rem 1rem;
    min-height: 160px;
  }
  .deal-summary-icon {
    width: 36px; height: 36px;
    margin-bottom: 0.7rem;
  }
  .deal-summary-title {
    font-size: 1rem;
  }
  .deal-summary-value {
    font-size: 1.4rem;
  }
  .deal-summary-amount {
    top: 0.7rem;
    right: 1rem;
    font-size: 0.95rem;
    padding: 0.2em 0.7em;
  }
}
</style>
@push('script-page')
    <script src="{{ asset('css/summernote/summernote-bs4.js') }}"></script>
    <script src="{{ asset('assets/js/plugins/dragula.min.js') }}"></script>
    <script>
        ! function(a) {
            "use strict";
            var t = function() {
                this.$body = a("body");
                this.dragulaInstance = null;
                this.dragTimeout = null;
            };

            t.prototype.init = function() {
                var self = this;

                a('[data-plugin="dragula"]').each(function() {
                    var $wrapper = a(this);
                    var containers = $wrapper.data("containers");
                    var containerElements = [];

                    if (containers) {
                        for (var i = 0; i < containers.length; i++) {
                            var element = a("#" + containers[i])[0];
                            if (element) containerElements.push(element);
                        }
                    } else {
                        containerElements = [$wrapper[0]];
                    }

                    var handleClass = $wrapper.data("handleclass");

                    // Destroy existing instance if it exists
                    if (self.dragulaInstance) {
                        self.dragulaInstance.destroy();
                    }

                    // Create optimized dragula instance
                    self.dragulaInstance = dragula(containerElements, {
                        moves: function(el, container, handle) {
                            if (handleClass) {
                                return handle.classList.contains(handleClass);
                            }
                            // Allow dragging on card but not on interactive elements
                            return !a(handle).closest('a, button, .btn, .dropdown-menu, input, textarea, select').length;
                        },
                        accepts: function(el, target, source, sibling) {
                            return a(target).hasClass('sales-item-wrp');
                        },
                        removeOnSpill: false,
                        revertOnSpill: true,
                        direction: 'vertical',
                        mirrorContainer: document.body
                    });

                    // Add optimized event handlers
                    self.dragulaInstance.on('drag', function(el, source) {
                        a(el).addClass('gu-transit-optimized');
                        a('body').addClass('dragging-deals');

                        // Disable text selection during drag
                        a('body').css('user-select', 'none');
                    });

                    self.dragulaInstance.on('dragend', function(el) {
                        a(el).removeClass('gu-transit-optimized');
                        a('body').removeClass('dragging-deals');

                        // Re-enable text selection
                        a('body').css('user-select', '');
                    });

                    self.dragulaInstance.on('over', function(el, container, source) {
                        a(container).addClass('drag-over-highlight');
                    });

                    self.dragulaInstance.on('out', function(el, container, source) {
                        a(container).removeClass('drag-over-highlight');
                    });

                    self.dragulaInstance.on('drop', function(el, target, source, sibling) {
                        // Clear any existing timeout
                        if (self.dragTimeout) {
                            clearTimeout(self.dragTimeout);
                        }

                        // Remove drag classes immediately for better UX
                        a(el).removeClass('gu-transit-optimized');
                        a(target).removeClass('drag-over-highlight');
                        a('body').removeClass('dragging-deals');
                        a('body').css('user-select', '');

                        // Get data efficiently
                        var dealId = a(el).attr('data-id');
                        var sourceId = a(source).attr('id');
                        var targetId = a(target).attr('id');
                        var stageId = a(target).attr('data-id');
                        var oldStatus = a(source).data('status');
                        var newStatus = a(target).data('status');
                        var pipelineId = '{{ $pipeline->id }}';

                        // Update counts immediately for better UX
                        var sourceCount = a("#" + sourceId + " > div").length;
                        var targetCount = a("#" + targetId + " > div").length;

                        a("#" + sourceId).parent().find('.count').text(sourceCount);
                        a("#" + targetId).parent().find('.count').text(targetCount);

                        // Collect order efficiently
                        var order = [];
                        a("#" + targetId + " > div").each(function(index) {
                            var dataId = a(this).attr('data-id');
                            if (dataId) order.push(dataId);
                        });

                        // Add loading state
                        a(el).addClass('updating-deal');

                        // Debounced AJAX call
                        self.dragTimeout = setTimeout(function() {
                            a.ajax({
                                url: '{{ route('deals.order') }}',
                                type: 'POST',
                                data: {
                                    deal_id: dealId,
                                    stage_id: stageId,
                                    order: order,
                                    new_status: newStatus,
                                    old_status: oldStatus,
                                    pipeline_id: pipelineId,
                                    "_token": a('meta[name="csrf-token"]').attr('content')
                                },
                                success: function(data) {
                                    a(el).removeClass('updating-deal');
                                    if (data.status === 'success') {
                                        show_toastr('success', data.message, 'success');
                                    } else {
                                        show_toastr(data.status, data.message, data.status);
                                    }
                                },
                                error: function(xhr) {
                                    a(el).removeClass('updating-deal');
                                    var data = xhr.responseJSON || {};
                                    show_toastr('error', data.message || 'An error occurred', 'error');

                                    // Revert the move on error
                                    if (source !== target) {
                                        a(source).append(el);
                                        // Restore counts
                                        a("#" + sourceId).parent().find('.count').text(a("#" + sourceId + " > div").length);
                                        a("#" + targetId).parent().find('.count').text(a("#" + targetId + " > div").length);
                                    }
                                }
                            });
                        }, 100); // Small delay to prevent rapid fire requests
                    });
                });
            };

            a.Dragula = new t;
            a.Dragula.Constructor = t;
        }(window.jQuery),
        function(a) {
            "use strict";

            // Initialize dragula with proper timing
            a(document).ready(function() {
                // Small delay to ensure DOM is fully rendered
                setTimeout(function() {
                    a.Dragula.init();
                }, 100);
            });

            // Reinitialize on dynamic content updates
            a(document).on('contentUpdated', function() {
                setTimeout(function() {
                    a.Dragula.init();
                }, 100);
            });

        }(window.jQuery);
    </script>
    <script>
        $(document).on("change", "#default_pipeline_id", function() {
            $('#change-pipeline').submit();
        });

        function openSourcesModal(sources) {
            const sourcesList = document.getElementById('sources-list');
            sourcesList.innerHTML = ''; // Clear previous sources

            if (sources && sources.length > 0) {
                sources.forEach(source => {
                    const listItem = document.createElement('li');
                    listItem.className = 'list-group-item';
                    listItem.textContent = source.name;
                    sourcesList.appendChild(listItem);
                });
            } else {
                const listItem = document.createElement('li');
                listItem.className = 'list-group-item';
                listItem.textContent = '{{ __('No sources found for this deal.') }}';
                sourcesList.appendChild(listItem);
            }

            const modal = new bootstrap.Modal(document.getElementById('sources-modal'));
            modal.show();
        }
    </script>
    <script>
        $(document).on('click', '.email-popup, .sms-popup', function(e) {
            e.preventDefault();
            var modal = $('#communication-modal');
            var title = modal.find('.modal-title');
            var body = modal.find('.modal-body .list-group');
            body.empty();

            var clientPhone = $(this).data('client-phone');
            var clientEmail = $(this).data('client-email');
            var whatsappPhone = String(clientPhone).replace(/[^\d]/g, '');

            if ($(this).hasClass('sms-popup')) {
                title.text('{{ __('Send Message') }}');
            } else {
                title.text('{{ __('Send Email') }}');
            }

            if(clientPhone || clientEmail) {
                if(clientPhone) {
                    body.append('<li class="list-group-item" style="cursor: pointer;" onclick="window.open(\'https://wa.me/' + whatsappPhone + '\', \'_blank\')"><span class="d-flex align-items-center"><i class="fab fa-whatsapp me-2" style="color: #25D366;"></i> {{ __('WhatsApp') }}</span></li>');
                    body.append('<li class="list-group-item" style="cursor: pointer;" onclick="window.open(\'sms:' + clientPhone + '\')"><span class="d-flex align-items-center"><i class="ti ti-message-circle me-2" style="color: #007bff;"></i> {{ __('Default SMS App') }}</span></li>');
                } else {
                     body.append('<li class="list-group-item text-muted d-flex align-items-center" style="cursor: not-allowed;"><i class="fab fa-whatsapp me-2" style="color: #25D366;"></i> {{ __('WhatsApp (No phone number)') }}</li>');
                     body.append('<li class="list-group-item text-muted d-flex align-items-center" style="cursor: not-allowed;"><i class="ti ti-message-circle me-2" style="color: #007bff;"></i> {{ __('Default SMS App (No phone number)') }}</li>');
                }
                if(clientEmail) {
                    body.append('<li class="list-group-item" style="cursor: pointer;" onclick="window.open(\'https://mail.google.com/\', \'_blank\')"><span class="d-flex align-items-center"><i class="ti ti-mail me-2" style="color: #46a5e0;"></i> {{ __('Gmail') }}</span></li>');
                } else {
                    body.append('<li class="list-group-item text-muted d-flex align-items-center" style="cursor: not-allowed;"><i class="ti ti-mail me-2" style="color: #46a5e0;"></i> {{ __('Gmail (No email)') }}</li>');
                }

                body.append('<li class="list-group-item"><a href="#" class="d-flex align-items-center"><i class="ti ti-cloud me-2"></i> {{ __('Cloud Email Service') }}</a></li>');

            } else {
                body.append('<li class="list-group-item text-center">{{ __('No contact information available.') }}</li>');
            }

            modal.modal('show');
        });
    </script>
@endpush
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('Deal') }}</li>
@endsection


@section('action-btn')
    <div class="float-end">
        {{ Form::open(['route' => 'deals.change.pipeline', 'id' => 'change-pipeline', 'class' => 'btn btn-sm']) }}
        {{ Form::select('default_pipeline_id', $pipelines, $pipeline->id, ['class' => 'form-control select me-4', 'id' => 'default_pipeline_id']) }}
        {{ Form::close() }}
        <a href="{{ route('deals.list') }}"
            data-size="lg"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            title="{{ __('List View') }}"
            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                    border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;margin-right:0.25rem;"
            onmouseover="this.style.transform='scale(1.1)'"
            onmouseout="this.style.transform='scale(1)'">
            <i class="ti ti-list" style="font-size: 16px;"></i>
        </a>
        <!-- Import Button -->
        <a href="#"
        data-size="md"
        data-bs-toggle="tooltip"
        data-bs-placement="top"
        title="{{ __('Import') }}"
        data-url="{{ route('deals.import') }}"
        data-ajax-popup="true"
        data-title="{{ __('Import Deal CSV file') }}"
        style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                    border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;margin-right:0.25rem;"
        onmouseover="this.style.transform='scale(1.1)'"
        onmouseout="this.style.transform='scale(1)'">
        <i class="fas fa-file-import" style="font-size:16px;"></i>
        </a>

        <!-- Export Button -->
        <a href="{{ route('deals.export') }}"
        data-bs-toggle="tooltip"
        data-bs-placement="top"
        title="{{ __('Export') }}"
        style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                    border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;margin-right:0.25rem;"
        onmouseover="this.style.transform='scale(1.1)'"
        onmouseout="this.style.transform='scale(1)'">
        <i class="fas fa-file-export" style="font-size:16px;"></i>
        </a>

        @can('create deal')
        <a href="#"
            data-size="lg"
            data-url="{{ route('deals.create') }}"
            data-ajax-popup="true"
            data-bs-toggle="tooltip"
            data-bs-placement="top"
            title="{{ __('Create New Deal') }}"
            data-title="{{ __('Create Deal') }}"
            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                    border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;margin-right:0.25rem;"
            onmouseover="this.style.transform='scale(1.1)'"
            onmouseout="this.style.transform='scale(1)'">
            <i class="ti ti-plus" style="font-size:16px;"></i>
        </a>

        @endcan
    </div>
@endsection


@section('content')
<div class="deal-summary-grid">
    <div class="deal-summary-card">
        <span class="deal-summary-amount">{{ $cnt_deal['total'] }}</span>
        <div class="deal-summary-icon">
            <i class="fas fa-layer-group" style="color:#fff;font-size:1.5rem;"></i>
        </div>
        <div class="deal-summary-title">{{ __('Total Deals') }}</div>
    </div>
    <div class="deal-summary-card">
        <span class="deal-summary-amount">{{ $cnt_deal['this_month'] }}</span>
        <div class="deal-summary-icon">
            <i class="fas fa-coins" style="color:#fff;font-size:1.5rem;"></i>
        </div>
        <div class="deal-summary-title">{{ __('This Month Total Deals') }}</div>
    </div>
    <div class="deal-summary-card">
        <span class="deal-summary-amount">{{ $cnt_deal['this_week'] }}</span>
        <div class="deal-summary-icon">
            <i class="fas fa-piggy-bank" style="color:#fff;font-size:1.5rem;"></i>
        </div>
        <div class="deal-summary-title">{{ __('This Week Total Deals') }}</div>
    </div>
    <div class="deal-summary-card">
        <span class="deal-summary-amount">{{ $cnt_deal['last_30days'] }}</span>
        <div class="deal-summary-icon">
            <i class="fas fa-comments-dollar" style="color:#fff;font-size:1.5rem;"></i>
        </div>
        <div class="deal-summary-title">{{ __('Last 30 Days Total Deals') }}</div>
    </div>
</div>
    <div class="row">
        @php
            $stages = $pipeline->stages;
            $json = [];
            foreach ($stages as $stage) {
                $json[] = 'task-list-' . $stage->id;
            }
        @endphp
        <div class="kanban-wrapper horizontal-scroll-cards" data-containers='{!! json_encode($json) !!}'
            data-plugin="dragula">
            @foreach ($stages as $stage)
                @php($deals = $stage->deals())
                <div class="kanban-col crm-sales-card">
                    <div class="kanban-header">
                        <h4>{{ $stage->name }}</h4>
                        <span class="count f-w-600">{{ count($deals) }}</span>
                    </div>
                    <div class="sales-item-wrp" id="task-list-{{ $stage->id }}" data-id="{{ $stage->id }}" data-status="{{ $stage->id }}">
                        @foreach ($deals as $deal)
                            <div class="kanban-card sales-item" data-id="{{ $deal->id }}" style="border-top: 4px solid #2e7d32;">
                                <div class="card-top">
                                    <div class="deal-title">
                                        <a href="@can('view deal')@if ($deal->is_active){{ route('deals.show', $deal->id) }}@else#@endif @else#@endcan" class="dashboard-link">
                                        <i class="fas fa-handshake me-2"></i>{{ $deal->name }}
                                        </a>
                                    </div>
                                    @if (Auth::user()->type != 'client')
                                        <div class="btn-group card-option">
                                            <button type="button" class="btn p-0 border-0" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                <i class="ti ti-dots-vertical"></i>
                                            </button>
                                            <div class="dropdown-menu icon-dropdown dropdown-menu-end">
                                                @can('edit deal')
                                                    <a href="#" data-size="md" data-url="{{ URL::to('deals/' . $deal->id . '/labels') }}" data-ajax-popup="true" class="dropdown-item" data-bs-original-title="{{ __('Add Labels') }}">
                                                        <i class="ti ti-bookmark"></i>
                                                        <span>{{ __('Labels') }}</span>
                                                    </a>
                                                    <a href="#" data-size="lg" data-url="{{ URL::to('deals/' . $deal->id . '/edit') }}" data-ajax-popup="true" class="dropdown-item" data-bs-original-title="{{ __('Edit Deal') }}">
                                                        <i class="ti ti-pencil"></i>
                                                        <span>{{ __('Edit') }}</span>
                                                    </a>
                                                @endcan
                                                @can('delete deal')
                                                    {!! Form::open([
                                                        'method' => 'DELETE',
                                                        'route' => ['deals.destroy', $deal->id],
                                                        'id' => 'delete-form-' . $deal->id,
                                                    ]) !!}
                                                    <a href="#" class="dropdown-item bs-pass-para">
                                                        <i class="ti ti-trash"></i>
                                                        <span> {{ __('Delete') }} </span>
                                                    </a>
                                                    {!! Form::close() !!}
                                                @endcan
                                            </div>
                                        </div>
                                    @endif
                                </div>
                                <div class="badge-wrp">
                                    @php($labels = $deal->labels())
                                    @if ($labels)
                                        @foreach ($labels as $label)
                                            <span class="kanban-label bg-light-{{ $label->color }}">{{ $label->name }}</span>
                                        @endforeach
                                    @endif
                                </div>
                                <?php
                                $products = $deal->products();
                                $sources = $deal->sources();
                                ?>
                                <div class="contact-info position-relative">
                                    <button type="button" class="position-absolute top-0 end-0 d-inline-flex align-items-center gap-1 p-1 px-2 border rounded-1 bg-light" style="margin-right: 17px;" data-bs-toggle="tooltip" title="{{ __('Source') }}" onclick="openSourcesModal({{ json_encode($sources) }})">
                                        <i class="f-16 ti ti-social"></i>
                                        <!-- {{ count($sources) }} -->
                                    </button>
                                    <div class="contact-item text-success">
                                        <i class="fas fa-phone-volume me-1"></i>
                                        <span>{{ $deal->phone ?? 'N/A' }}</span>
                                    </div>
                                    <!-- <div class="contact-item text-success">
                                        <i class="fas fa-envelope me-1"></i>
                                        <span>{{ $deal->email ?? 'N/A' }}</span>
                                    </div> -->
                                    <div class="contact-item text-info">
                                        <i class="fas fa-dollar-sign me-1"></i>
                                        <span>{{ \Auth::user()->priceFormat($deal->price) }}</span>
                                    </div>
                                    <!-- <div class="contact-item text-warning">
                                        <i class="fas fa-tasks me-1"></i>
                                        <span>{{ count($deal->tasks) }}/{{ count($deal->complete_tasks) }} {{ __('Tasks') }}</span>
                                    </div> -->
                                </div>
                                <div class="card-bottom">
                                    <div class="communication-buttons">
                                        <button class="communication-btn call" data-bs-toggle="tooltip" title="Call">
                                            <i class="fas fa-phone-alt"></i>
                                        </button>
                                        <button class="communication-btn sms" data-bs-toggle="tooltip" title="SMS">
                                            <i class="fas fa-comment-dots"></i>
                                        </button>
                                        <button class="communication-btn email" data-bs-toggle="tooltip" title="Email" onclick="openCommunicationModal({{ json_encode(['name' => $deal->name, 'phone' => $deal->phone, 'email' => $deal->email]) }})">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                    </div>
                                    <div class="user-group">
                                        @foreach ($deal->users as $user)
                                            <i class="fas fa-user-circle" data-bs-toggle="tooltip" title="{{ $user->name }}"></i>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endforeach
        </div>
    </div>

<!-- Communication Modal -->
<div class="modal fade" id="communication-modal" tabindex="-1" aria-labelledby="communication-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modern-comm-modal">
            <div class="modal-header">
                <div class="d-flex align-items-center gap-3">
                    <div id="comm-deal-avatar" class="comm-deal-avatar"></div>
                    <div>
                        <h5 class="modal-title mb-0" id="communication-modal-label"></h5>
                        <div class="comm-deal-name" id="comm-deal-name"></div>
                        <div class="comm-deal-contact text-muted small" id="comm-deal-contact"></div>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="communication-options-list">
                    <a href="#" class="communication-option-item" id="whatsapp-option" target="_blank">
                        <i class="fab fa-whatsapp"></i>
                        <span>WhatsApp</span>
                    </a>
                    <a href="#" class="communication-option-item" id="default-email-option">
                        <i class="fas fa-envelope-open-text"></i>
                        <span>Default Email App</span>
                    </a>
                    <a href="#" class="communication-option-item" id="cloud-email-option">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <span>Cloud Email Service</span>
                    </a>
                    <a href="#" class="communication-option-item" id="sms-option">
                        <i class="fas fa-sms"></i>
                        <span>Send SMS</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sources Modal -->
<div class="modal fade" id="sources-modal" tabindex="-1" aria-labelledby="sources-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sources-modal-label">{{ __('Deal Sources') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <ul class="list-group" id="sources-list">
                    <!-- Sources will be dynamically inserted here -->
                </ul>
            </div>
        </div>
    </div>
</div>

@endsection

<script>
    // Modern openCommunicationModal function
    function openCommunicationModal(deal) {
        const modalEl = document.getElementById('communication-modal');
        const modal = new bootstrap.Modal(modalEl);
        const title = document.getElementById('communication-modal-label');
        const name = document.getElementById('comm-deal-name');
        const contact = document.getElementById('comm-deal-contact');
        const avatar = document.getElementById('comm-deal-avatar');

        // Set modal title
        title.textContent = 'Contact Deal';
        name.textContent = deal.name || '';
        contact.innerHTML =
            (deal.phone ? `<i class='fas fa-phone-alt'></i> ${deal.phone}` : '') +
            (deal.email ? ` &nbsp; <i class='fas fa-envelope'></i> ${deal.email}` : '');
        // Avatar: initials
        if (deal.name) {
            const initials = deal.name.split(' ').map(w => w[0]).join('').substring(0,2).toUpperCase();
            avatar.textContent = initials;
        } else {
            avatar.textContent = '?';
        }
        // WhatsApp
        const whatsapp = document.getElementById('whatsapp-option');
        if (deal.phone) {
            whatsapp.href = `https://wa.me/${deal.phone.replace(/[^\d]/g, '')}`;
            whatsapp.classList.remove('disabled');
        } else {
            whatsapp.href = '#';
            whatsapp.classList.add('disabled');
        }
        // Email (default app)
        const email = document.getElementById('default-email-option');
        if (deal.email) {
            email.href = `mailto:${deal.email}`;
            email.classList.remove('disabled');
        } else {
            email.href = '#';
            email.classList.add('disabled');
        }
        // Cloud email (customize as needed)
        const cloud = document.getElementById('cloud-email-option');
        if (deal.email) {
            cloud.href = `mailto:${deal.email}`;
            cloud.classList.remove('disabled');
        } else {
            cloud.href = '#';
            cloud.classList.add('disabled');
        }
        // SMS
        const sms = document.getElementById('sms-option');
        if (deal.phone) {
            sms.href = `sms:${deal.phone}`;
            sms.classList.remove('disabled');
        } else {
            sms.href = '#';
            sms.classList.add('disabled');
        }

        modal.show();
    }
</script>
