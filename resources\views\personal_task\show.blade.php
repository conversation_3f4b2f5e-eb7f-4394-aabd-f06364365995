<style>
.assignment-info {
    background: #f0f9ff;
    border: 1px solid #e0f2fe;
    border-radius: 0.5rem;
    padding: 0.5rem 0.75rem;
}

.assigned-user-card {
    transition: all 0.2s ease;
    background: #f8fafc !important;
    border: 1px solid #e2e8f0 !important;
}

.assigned-user-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    background: #ffffff !important;
}

.user-details {
    line-height: 1.2;
}

.avatar-placeholder {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
}
</style>

<div class="modal-body">
    <div class="row">
        <div class="col-12">
            <div class="card border-0">
                <div class="card-header bg-transparent border-0 pb-0">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h5 class="mb-1">{{ $task->name }}</h5>
                            <div class="d-flex align-items-center gap-2 mb-2">
                                <span class="badge bg-{{ $task->priority_color }}">{{ ucfirst($task->priority) }}</span>
                                @if($task->stage)
                                    <span class="badge bg-secondary">{{ $task->stage->name }}</span>
                                @endif
                                @if($task->is_complete)
                                    <span class="badge bg-success">{{ __('Completed') }}</span>
                                @endif
                                @if($task->is_favourite)
                                    <i class="ti ti-star-filled text-warning"></i>
                                @endif
                            </div>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">{{ __('Created by') }}: {{ $task->createdBy->name ?? 'Unknown' }}</small><br>
                            <small class="text-muted">{{ $task->created_at->format('M d, Y H:i') }}</small>
                        </div>
                    </div>
                </div>
                
                <div class="card-body pt-2">
                    @if($task->description)
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">{{ __('Description') }}</h6>
                            <p class="mb-0">{{ $task->description }}</p>
                        </div>
                    @endif
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">{{ __('Progress') }}</h6>
                            <div class="progress mb-1" style="height: 8px;">
                                <div class="progress-bar" role="progressbar" 
                                     style="width: {{ $task->progress }}%;" 
                                     aria-valuenow="{{ $task->progress }}" 
                                     aria-valuemin="0" aria-valuemax="100">
                                </div>
                            </div>
                            <small class="text-muted">{{ $task->progress }}% {{ __('Complete') }}</small>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">{{ __('Time Information') }}</h6>
                            @if($task->estimated_hrs > 0)
                                <small class="d-block">{{ __('Estimated') }}: {{ $task->estimated_hrs }} {{ __('hours') }}</small>
                            @endif
                            @if($task->start_date)
                                <small class="d-block">{{ __('Start Date') }}: {{ \Carbon\Carbon::parse($task->start_date)->format('M d, Y') }}</small>
                            @endif
                            @if($task->end_date)
                                <small class="d-block text-{{ \Carbon\Carbon::parse($task->end_date)->isPast() ? 'danger' : 'muted' }}">
                                    {{ __('Due Date') }}: {{ \Carbon\Carbon::parse($task->end_date)->format('M d, Y') }}
                                </small>
                            @endif
                        </div>
                    </div>
                    
                    @php
                        $assignedUsers = $task->users();
                    @endphp
                    @if($assignedUsers->count() > 0)
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">
                                <i class="ti ti-users me-1"></i>
                                {{ __('Task Members') }}
                                <small class="text-muted">({{ $assignedUsers->count() }} {{ __('assigned') }})</small>
                            </h6>
                            <div class="assignment-info mb-2">
                                <small class="text-muted">
                                    <i class="ti ti-info-circle me-1"></i>
                                    {{ __('Below users are assigned to this task.') }}
                                </small>
                            </div>
                            <div class="d-flex flex-wrap gap-2">
                                @foreach($assignedUsers as $user)
                                    <div class="assigned-user-card d-flex align-items-center bg-light rounded-3 px-3 py-2 border">
                                        <div class="user-avatar-small me-2">
                                            @if($user->avatar && file_exists(storage_path('app/public/uploads/avatar/' . $user->avatar)))
                                                <img src="{{ asset(Storage::url('uploads/avatar/' . $user->avatar)) }}"
                                                     alt="{{ $user->name }}" class="rounded-circle" width="28" height="28">
                                            @else
                                                <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center"
                                                     style="width: 28px; height: 28px; background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; font-size: 0.75rem; font-weight: 600;">
                                                    {{ strtoupper(substr($user->name, 0, 1)) }}
                                                </div>
                                            @endif
                                        </div>
                                        <div class="user-details">
                                            <div class="user-name fw-medium" style="font-size: 0.875rem;">
                                                {{ $user->name }}
                                                @if($user->id == Auth::user()->id)
                                                    <small class="text-primary">({{ __('You') }})</small>
                                                @endif
                                            </div>
                                            @if($user->email)
                                                <small class="text-muted">{{ $user->email }}</small>
                                            @endif
                                        </div>
                                        @if($task->created_by == $user->id)
                                            <span class="badge bg-primary ms-2" style="font-size: 0.65rem;">
                                                {{ __('Creator') }}
                                            </span>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @else
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">
                                <i class="ti ti-users me-1"></i>
                                {{ __('Task Members') }}
                            </h6>
                            <div class="alert alert-warning py-2">
                                <small>
                                    <i class="ti ti-alert-triangle me-1"></i>
                                    {{ __('No users are assigned to this task.') }}
                                </small>
                            </div>
                        </div>
                    @endif
                    
                    @if($task->taskCheckList()->count() > 0)
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">{{ __('Checklist') }}</h6>
                            <div class="list-group list-group-flush">
                                @foreach($task->taskCheckList as $checklist)
                                    <div class="list-group-item px-0 py-2 border-0">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" 
                                                   {{ $checklist->status ? 'checked' : '' }} disabled>
                                            <label class="form-check-label {{ $checklist->status ? 'text-decoration-line-through text-muted' : '' }}">
                                                {{ $checklist->name }}
                                            </label>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                    
                    @if($task->taskFiles()->count() > 0)
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">{{ __('Files') }}</h6>
                            <div class="list-group list-group-flush">
                                @foreach($task->taskFiles as $file)
                                    <div class="list-group-item px-0 py-2 border-0 d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-file me-2"></i>
                                            <div>
                                                <span>{{ $file->name }}</span>
                                                <small class="text-muted d-block">{{ $file->file_size }} bytes</small>
                                            </div>
                                        </div>
                                        <a href="{{ asset(Storage::url('uploads/personal_tasks/' . $file->file)) }}" 
                                           target="_blank" class="btn btn-sm btn-outline-primary">
                                            <i class="ti ti-download"></i>
                                        </a>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                    
                    @if($task->comments()->count() > 0)
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">{{ __('Recent Comments') }}</h6>
                            <div class="list-group list-group-flush">
                                @foreach($task->comments()->take(3) as $comment)
                                    <div class="list-group-item px-0 py-2 border-0">
                                        <div class="d-flex">
                                            <img src="{{ asset(Storage::url('uploads/avatar/' . $comment->user->avatar)) }}" 
                                                 alt="{{ $comment->user->name }}" class="rounded-circle me-2" width="32" height="32">
                                            <div class="flex-1">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <strong class="small">{{ $comment->user->name }}</strong>
                                                    <small class="text-muted">{{ $comment->created_at->diffForHumans() }}</small>
                                                </div>
                                                <p class="mb-0 small">{{ $comment->comment }}</p>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                                @if($task->comments()->count() > 3)
                                    <div class="text-center">
                                        <small class="text-muted">{{ __('and') }} {{ $task->comments()->count() - 3 }} {{ __('more comments') }}</small>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <button type="button" class="btn btn-light" data-bs-dismiss="modal">{{ __('Close') }}</button>
    @can('edit personal task')
        @if($task->created_by == Auth::user()->id || $task->isAssignedTo(Auth::user()->id))
            <a href="#" data-url="{{ route('personal-tasks.edit', $task->id) }}" 
               data-ajax-popup="true" data-size="lg" class="btn btn-primary">
                <i class="ti ti-edit"></i> {{ __('Edit Task') }}
            </a>
        @endif
    @endcan
</div>
