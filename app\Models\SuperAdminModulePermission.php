<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SuperAdminModulePermission extends Model
{
    protected $fillable = [
        'super_admin_id',
        'module_id',
        'has_access',
        'permissions',
        'granted_by'
    ];

    protected $casts = [
        'has_access' => 'boolean',
        'permissions' => 'array',
    ];

    /**
     * Get the super admin user
     */
    public function superAdmin()
    {
        return $this->belongsTo(User::class, 'super_admin_id');
    }

    /**
     * Get the module
     */
    public function module()
    {
        return $this->belongsTo(SystemAdminModule::class, 'module_id');
    }

    /**
     * Get the system admin who granted the permission
     */
    public function grantedBy()
    {
        return $this->belongsTo(User::class, 'granted_by');
    }

    /**
     * Check if super admin has specific permission for this module
     */
    public function hasPermission($permission)
    {
        if (!$this->has_access) {
            return false;
        }

        if (empty($this->permissions)) {
            return false;
        }

        return in_array($permission, $this->permissions);
    }

    /**
     * Grant specific permissions to super admin for this module
     */
    public function grantPermissions(array $permissions)
    {
        $currentPermissions = $this->permissions ?? [];
        $newPermissions = array_unique(array_merge($currentPermissions, $permissions));
        
        $this->update([
            'permissions' => $newPermissions,
            'has_access' => true
        ]);
    }

    /**
     * Revoke specific permissions from super admin for this module
     */
    public function revokePermissions(array $permissions)
    {
        $currentPermissions = $this->permissions ?? [];
        $newPermissions = array_diff($currentPermissions, $permissions);
        
        $this->update([
            'permissions' => $newPermissions,
            'has_access' => !empty($newPermissions)
        ]);
    }
}
