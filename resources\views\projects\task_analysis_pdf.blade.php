@extends('layouts.admin')

@section('page-title')
    {{ __('Task Analysis Report') }}
@endsection

@section('content')
<style>
    .pdf-container {
        background: white;
        padding: 30px;
        margin: 20px auto;
        max-width: 1200px;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    .pdf-header {
        text-align: center;
        margin-bottom: 30px;
        border-bottom: 2px solid #007bff;
        padding-bottom: 20px;
    }
    .pdf-title {
        font-size: 28px;
        font-weight: bold;
        color: #333;
        margin-bottom: 10px;
    }
    .pdf-subtitle {
        font-size: 16px;
        color: #666;
        margin-bottom: 5px;
    }
    .pdf-date {
        font-size: 14px;
        color: #888;
    }
    .pdf-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
        font-size: 12px;
    }
    .pdf-table th {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        padding: 12px 8px;
        text-align: left;
        font-weight: bold;
        color: #495057;
    }
    .pdf-table td {
        border: 1px solid #dee2e6;
        padding: 10px 8px;
        vertical-align: top;
    }
    .pdf-table tr:nth-child(even) {
        background-color: #f8f9fa;
    }
    .deadline-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 11px;
        font-weight: bold;
        text-transform: uppercase;
    }
    .deadline-ontime { background-color: #d4edda; color: #155724; }
    .deadline-soon { background-color: #fff3cd; color: #856404; }
    .deadline-overdue { background-color: #f8d7da; color: #721c24; }
    .pdf-footer {
        margin-top: 30px;
        text-align: center;
        font-size: 12px;
        color: #666;
        border-top: 1px solid #dee2e6;
        padding-top: 20px;
    }
    @media print {
        .pdf-container {
            box-shadow: none;
            margin: 0;
            max-width: none;
        }
    }
</style>

<div id="printableArea">
    <div class="pdf-container">
        <div class="pdf-header">
            <div class="pdf-title">Task Analysis Report</div>
            <div class="pdf-subtitle">Task Deadline and Status Analysis</div>
            <div class="pdf-date">Generated on: {{ date('F d, Y \a\t g:i A') }}</div>
            <div class="pdf-date">Total Records: {{ $tasks->count() }}</div>
        </div>

        <table class="pdf-table">
            <thead>
                <tr>
                    <th style="width: 5%;">#</th>
                    <th style="width: 25%;">Task Name</th>
                    <th style="width: 20%;">Assigned To</th>
                    <th style="width: 12%;">Start Date</th>
                    <th style="width: 12%;">Due Date</th>
                    <th style="width: 13%;">Status</th>
                    <th style="width: 13%;">Deadline Status</th>
                </tr>
            </thead>
            <tbody>
                @forelse($tasks as $i => $task)
                    <tr>
                        <td>{{ $i + 1 }}</td>
                        <td><strong>{{ $task->name }}</strong></td>
                        <td>
                            @if(!empty($task->assign_to))
                                @php
                                    $userIds = explode(',', $task->assign_to);
                                    $users = App\Models\User::whereIn('id', $userIds)->get();
                                @endphp
                                @foreach($users as $user)
                                    {{ $user->name }}@if(!$loop->last), @endif
                                @endforeach
                            @else
                                -
                            @endif
                        </td>
                        <td>{{ $task->start_date ?? '-' }}</td>
                        <td>{{ $task->end_date ?? '-' }}</td>
                        <td>{{ optional($task->stage)->name ?? '-' }}</td>
                        <td>
                            @php
                                $deadlineStatus = 'On Time';
                                $deadlineClass = 'ontime';
                                if ($task->end_date) {
                                    $dueDate = \Carbon\Carbon::parse($task->end_date);
                                    $today = \Carbon\Carbon::today();
                                    
                                    if ($dueDate->isPast()) {
                                        $deadlineStatus = 'Overdue';
                                        $deadlineClass = 'overdue';
                                    } elseif ($dueDate->diffInDays($today) <= 3) {
                                        $deadlineStatus = 'Due Soon';
                                        $deadlineClass = 'soon';
                                    }
                                }
                            @endphp
                            <span class="deadline-badge deadline-{{ $deadlineClass }}">
                                {{ $deadlineStatus }}
                            </span>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 20px;">No tasks found.</td>
                    </tr>
                @endforelse
            </tbody>
        </table>

        <div class="pdf-footer">
            <p>This report was generated automatically by the Project Management System.</p>
            <p>© {{ date('Y') }} {{ config('app.name', 'Laravel') }}. All rights reserved.</p>
        </div>
    </div>
</div>

<input type="hidden" id="filename" value="task_analysis_{{ date('Y-m-d_H-i-s') }}">
@endsection

@push('script-page')
<script type="text/javascript" src="{{ asset('js/html2pdf.bundle.min.js') }}"></script>
<script>
    function closeScript() {
        setTimeout(function () {
            window.close();
        }, 1000);
    }

    $(window).on('load', function () {
        var element = document.getElementById('printableArea');
        var filename = $('#filename').val();
        var opt = {
            margin: [0.5, 0.5, 0.5, 0.5],
            filename: filename,
            image: {type: 'jpeg', quality: 1},
            html2canvas: {scale: 2, dpi: 72, letterRendering: true},
            jsPDF: {unit: 'in', format: 'A4', orientation: 'landscape'}
        };
        html2pdf().set(opt).from(element).save().then(closeScript);
    });
</script>
@endpush
