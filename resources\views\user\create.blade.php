{{ Form::open(['url' => 'users', 'method' => 'post', 'class'=>'needs-validation', 'novalidate']) }}
<div class="modal-body">
    <div class="row">
        @if (\Auth::user()->type == 'super admin')
            <div class="col-md-6">
                <div class="form-group">
                    {{ Form::label('name', __('Name'), ['class' => 'form-label']) }}<x-required></x-required>
                    {{ Form::text('name', null, ['class' => 'form-control', 'placeholder' => __('Enter Company Name'), 'required' => 'required']) }}
                    @error('name')
                        <small class="invalid-name" role="alert">
                            <strong class="text-danger">{{ $message }}</strong>
                        </small>
                    @enderror
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    {{ Form::label('email', __('Email'), ['class' => 'form-label']) }}<x-required></x-required>
                    {{ Form::email('email', null, ['class' => 'form-control', 'placeholder' => __('Enter Company Email'), 'required' => 'required']) }}
                    @error('email')
                        <small class="invalid-email" role="alert">
                            <strong class="text-danger">{{ $message }}</strong>
                        </small>
                    @enderror
                </div>
            </div>


            {!! Form::hidden('role', 'company', null, ['class' => 'form-control select2', 'required' => 'required']) !!}
            <div class="col-md-6 mb-3 form-group mt-4">
                <label for="password_switch">{{ __('Login is enable') }}</label>
                <div class="form-check form-switch custom-switch-v1 float-end">
                    <input type="checkbox" name="password_switch" class="form-check-input input-primary pointer" value="on" id="password_switch">
                    <label class="form-check-label" for="password_switch"></label>
                </div>
            </div>
            <div class="col-md-6 ps_div d-none">
                <div class="form-group">
                    {{ Form::label('password', __('Password'), ['class' => 'form-label']) }}<x-required></x-required>
                    {{ Form::password('password', ['class' => 'form-control', 'placeholder' => __('Enter Company Password'), 'minlength' => '6']) }}
                    @error('password')
                        <small class="invalid-password" role="alert">
                            <strong class="text-danger">{{ $message }}</strong>
                        </small>
                    @enderror
                </div>
            </div>
        @else
            <div class="col-md-6">
                <div class="form-group">
                    {{ Form::label('name', __('Name'), ['class' => 'form-label']) }}<x-required></x-required>
                    {{ Form::text('name', null, ['class' => 'form-control', 'placeholder' => __('Enter User Name'), 'required' => 'required']) }}
                    @error('name')
                        <small class="invalid-name" role="alert">
                            <strong class="text-danger">{{ $message }}</strong>
                        </small>
                    @enderror
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    {{ Form::label('email', __('Email'), ['class' => 'form-label']) }}<x-required></x-required>
                    {{ Form::email('email', null, ['class' => 'form-control', 'placeholder' => __('Enter User Email'), 'required' => 'required']) }}
                    @error('email')
                        <small class="invalid-email" role="alert">
                            <strong class="text-danger">{{ $message }}</strong>
                        </small>
                    @enderror
                </div>
            </div>
            <!-- Remove role selection for company users -->
            @if(\Auth::user()->type == 'company')
                <!-- Hidden input to set role as 'Employee' -->
                {!! Form::hidden('role', 'Employee') !!}
                <div class="col-md-5 mb-3 form-group mt-4">
                    <label for="password_switch">{{ __('Login is enable') }}</label>
                    <div class="form-check form-switch custom-switch-v1 float-end">
                        <input type="checkbox" name="password_switch" class="form-check-input input-primary pointer" value="on" id="password_switch">
                        <label class="form-check-label" for="password_switch"></label>
                    </div>
                </div>
                <div class="col-md-6 ps_div d-none">
                    <div class="form-group">
                        {{ Form::label('password', __('Password'), ['class' => 'form-label']) }}<x-required></x-required>
                        {{ Form::password('password', ['class' => 'form-control', 'placeholder' => __('Enter User Password'), 'minlength' => '6']) }}
                        @error('password')
                            <small class="invalid-password" role="alert">
                                <strong class="text-danger">{{ $message }}</strong>
                            </small>
                        @enderror
                    </div>
                </div>
                <!-- Module/Permission selector for company users (pricing plan style) -->
                @if(!empty($availableModules))
                    <div class="col-12 mt-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5>{{ __('Module Permissions') }}</h5>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-primary me-2" id="selectAllModules">{{ __('Select All Modules') }}</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="deselectAllModules">{{ __('Deselect All') }}</button>
                            </div>
                        </div>
                        <hr>
                        @foreach($availableModules as $moduleKey => $moduleData)
                            @php
                                $createPermissions = [];
                                $readPermissions = [];
                                $deletePermissions = [];
                                $otherPermissions = [];
                                if (is_array($moduleData) && isset($moduleData['permissions'])) {
                                    foreach($moduleData['permissions'] as $permission) {
                                        if (str_contains($permission, 'create')) {
                                            $createPermissions[] = $permission;
                                        } elseif (str_contains($permission, 'view') || str_contains($permission, 'show') || str_contains($permission, 'dashboard')) {
                                            $readPermissions[] = $permission;
                                        } elseif (str_contains($permission, 'delete')) {
                                            $deletePermissions[] = $permission;
                                        } else {
                                            $otherPermissions[] = $permission;
                                        }
                                    }
                                }
                            @endphp
                            <div class="card mb-3">
                                <div class="card-header">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="form-check">
                                            <input class="form-check-input module-checkbox" type="checkbox" id="module_{{ $moduleKey }}" name="modules[{{ $moduleKey }}]" data-module="{{ $moduleKey }}">
                                            <label class="form-check-label fw-bold" for="module_{{ $moduleKey }}">
                                                {{ is_array($moduleData) && isset($moduleData['name']) ? $moduleData['name'] : $moduleData }}
                                            </label>
                                        </div>
                                        <div class="btn-group" role="group" style="display: none;" id="selectButtons_{{ $moduleKey }}">
                                            <button type="button" class="btn btn-sm btn-outline-success select-all-module" data-module="{{ $moduleKey }}">{{ __('Select All') }}</button>
                                            <button type="button" class="btn btn-sm btn-outline-warning select-create" data-module="{{ $moduleKey }}">{{ __('Create') }}</button>
                                            <button type="button" class="btn btn-sm btn-outline-info select-read" data-module="{{ $moduleKey }}">{{ __('Read') }}</button>
                                            <button type="button" class="btn btn-sm btn-outline-danger select-delete" data-module="{{ $moduleKey }}">{{ __('Delete') }}</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body permissions-section" id="permissions_{{ $moduleKey }}" style="display: none;">
                                    @if(count($createPermissions) > 0)
                                        <div class="permission-group mb-4">
                                            <h6 class="text-success mb-2"><i class="fas fa-plus-circle"></i> {{ __('Create Permissions') }}</h6>
                                            <div class="row">
                                                @foreach($createPermissions as $permission)
                                                    <div class="col-md-4 mb-2">
                                                        <div class="form-check">
                                                            <input class="form-check-input permission-checkbox create-permission" type="checkbox" id="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}" name="permissions[{{ $moduleKey }}][]" value="{{ $permission }}" data-module="{{ $moduleKey }}" data-type="create">
                                                            <label class="form-check-label" for="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}">{{ ucwords(str_replace('_', ' ', $permission)) }}</label>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                    @if(count($readPermissions) > 0)
                                        <div class="permission-group mb-4">
                                            <h6 class="text-info mb-2"><i class="fas fa-eye"></i> {{ __('Read/View Permissions') }}</h6>
                                            <div class="row">
                                                @foreach($readPermissions as $permission)
                                                    <div class="col-md-4 mb-2">
                                                        <div class="form-check">
                                                            <input class="form-check-input permission-checkbox read-permission" type="checkbox" id="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}" name="permissions[{{ $moduleKey }}][]" value="{{ $permission }}" data-module="{{ $moduleKey }}" data-type="read">
                                                            <label class="form-check-label" for="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}">{{ ucwords(str_replace('_', ' ', $permission)) }}</label>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                    @if(count($deletePermissions) > 0)
                                        <div class="permission-group mb-4">
                                            <h6 class="text-danger mb-2"><i class="fas fa-trash"></i> {{ __('Delete Permissions') }}</h6>
                                            <div class="row">
                                                @foreach($deletePermissions as $permission)
                                                    <div class="col-md-4 mb-2">
                                                        <div class="form-check">
                                                            <input class="form-check-input permission-checkbox delete-permission" type="checkbox" id="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}" name="permissions[{{ $moduleKey }}][]" value="{{ $permission }}" data-module="{{ $moduleKey }}" data-type="delete">
                                                            <label class="form-check-label" for="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}">{{ ucwords(str_replace('_', ' ', $permission)) }}</label>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                    @if(count($otherPermissions) > 0)
                                        <div class="permission-group mb-4">
                                            <h6 class="text-secondary mb-2"><i class="fas fa-cog"></i> {{ __('Other Permissions') }}</h6>
                                            <div class="row">
                                                @foreach($otherPermissions as $permission)
                                                    <div class="col-md-4 mb-2">
                                                        <div class="form-check">
                                                            <input class="form-check-input permission-checkbox other-permission" type="checkbox" id="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}" name="permissions[{{ $moduleKey }}][]" value="{{ $permission }}" data-module="{{ $moduleKey }}" data-type="other">
                                                            <label class="form-check-label" for="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}">{{ ucwords(str_replace('_', ' ', $permission)) }}</label>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                @endif
            @endif
        @endif
        
@if((\Auth::user()->type == 'system admin' || \Auth::user()->type == 'super admin') && !empty($availableModules))
    @if(!empty($omxFlowEnabledViaPlan))
        <!-- OMX Flow is already enabled through pricing plan -->
        <div class="col-12 mt-4">
            <div class="alert alert-info border-0 shadow-sm">
                <div class="d-flex align-items-center">
                    <i class="ti ti-info-circle me-2 text-info"></i>
                    <div>
                        <strong>{{ __('OMX Flow Integration Enabled') }}</strong>
                        <p class="mb-0 small">{{ __('OMX Flow integration is automatically enabled through your pricing plan. Users will be synced automatically.') }}</p>
                    </div>
                </div>
            </div>
        </div>
    @else
        <div class="col-12 mt-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light border-0 py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1 text-dark">
                                <i class="ti ti-settings me-2 text-primary"></i>{{ __('OMX Flow Integration') }}
                            </h6>
                            <small class="text-muted">{{ __('Configure module access and company details') }}</small>
                        </div>
                        <div class="form-check form-switch">
                            <input type="checkbox" name="module_switch" class="form-check-input" value="on" id="module_switch">
                            <label class="form-check-label fw-medium" for="module_switch">{{ __('Enable') }}</label>
                        </div>
                    </div>
                </div>
            
            <div class="card-body omx_flow_content d-none">
                <!-- Module Permissions Section -->
                <div class="border-top pt-4">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h6 class="text-dark mb-1">
                                <i class="ti ti-shield-check me-2 text-success"></i>{{ __('Module Permissions') }}
                            </h6>
                            <small class="text-muted">{{ __('Select modules this super admin can access') }}</small>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-outline-primary me-1" id="select_all_modules">
                                <i class="ti ti-check-all me-1"></i>{{ __('All') }}
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="clear_all_modules">
                                <i class="ti ti-x me-1"></i>{{ __('None') }}
                            </button>
                        </div>
                    </div>
                    
                    <div class="row g-3">
                        @foreach($availableModules as $moduleKey => $moduleLabel)
                            <div class="col-md-6">
                                <div class="form-check module-check-item">
                                    <input class="form-check-input" type="checkbox" name="module_permissions[]" value="{{ $moduleKey }}" id="module_{{ $moduleKey }}">
                                    <label class="form-check-label w-100" for="module_{{ $moduleKey }}">
                                        <div class="d-flex align-items-center p-3 border rounded hover-shadow">
                                            <div class="me-3">
                                                @switch($moduleKey)
                                                    @case('whatsapp_flows')
                                                        <div class="icon-circle bg-success-light">
                                                            <i class="ti ti-brand-whatsapp text-success"></i>
                                                        </div>
                                                        @break
                                                    @case('whatsapp_orders')
                                                        <div class="icon-circle bg-info-light">
                                                            <i class="ti ti-shopping-cart text-info"></i>
                                                        </div>
                                                        @break
                                                    @case('campaigns')
                                                        <div class="icon-circle bg-warning-light">
                                                            <i class="ti ti-speakerphone text-warning"></i>
                                                        </div>
                                                        @break
                                                    @case('templates')
                                                        <div class="icon-circle bg-primary-light">
                                                            <i class="ti ti-template text-primary"></i>
                                                        </div>
                                                        @break
                                                    @case('chatbot')
                                                        <div class="icon-circle bg-secondary-light">
                                                            <i class="ti ti-robot text-secondary"></i>
                                                        </div>
                                                        @break
                                                    @default
                                                        <div class="icon-circle bg-light">
                                                            <i class="ti ti-apps text-muted"></i>
                                                        </div>
                                                @endswitch
                                            </div>
                                            <div class="flex-grow-1">
                                                <div class="fw-medium text-dark">{{ $moduleLabel }}</div>
                                                <small class="text-muted">
                                                    @switch($moduleKey)
                                                        @case('whatsapp_flows')
                                                            {{ __('Automation & workflows') }}
                                                            @break
                                                        @case('whatsapp_orders')
                                                            {{ __('Order management') }}
                                                            @break
                                                        @case('campaigns')
                                                            {{ __('Marketing campaigns') }}
                                                            @break
                                                        @case('templates')
                                                            {{ __('Message templates') }}
                                                            @break
                                                        @case('chatbot')
                                                            {{ __('Bot responses') }}
                                                            @break
                                                        @default
                                                            {{ __('Module access') }}
                                                    @endswitch
                                                </small>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    
                    <div class="mt-3 pt-3 border-top">
                        <div class="d-flex align-items-center text-muted">
                            <i class="ti ti-info-circle me-2"></i>
                            <small>{{ __('Selected permissions will be synced to OMX Flow module automatically') }}</small>
                        </div>
                    </div>
                </div>
                <!-- Company Info Section -->
                <div class="border-top pt-4 mt-4">
                    <h6 class="text-dark mb-3">{{ __('Company Information') }}</h6>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ Form::label('company_name', __('Company Name'), ['class' => 'form-label']) }}<x-required></x-required>
                                {{ Form::text('company_name', null, ['class' => 'form-control', 'placeholder' => __('Enter Company Name')]) }}
                                @error('company_name')
                                    <small class="text-danger">{{ $message }}</small>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ Form::label('company_description', __('Company Description'), ['class' => 'form-label']) }}<x-required></x-required>
                                {{ Form::textarea('company_description', null, ['class' => 'form-control', 'rows' => 3, 'placeholder' => __('Enter Company Description')]) }}
                                @error('company_description')
                                    <small class="text-danger">{{ $message }}</small>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
@endif
        
        @if (!$customFields->isEmpty())
                    @include('customFields.formBuilder')
        @endif
    </div>

</div>

<div class="modal-footer">
    <input type="button" value="{{ __('Cancel') }}" class="btn  btn-secondary" data-bs-dismiss="modal">
    <input type="submit" value="{{ __('Create') }}" class="btn  btn-primary">
</div>

{{ Form::close() }}

<style>
    .icon-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
    }
    
    .bg-success-light {
        background-color: rgba(25, 135, 84, 0.1);
    }
    
    .bg-info-light {
        background-color: rgba(13, 202, 240, 0.1);
    }
    
    .bg-warning-light {
        background-color: rgba(255, 193, 7, 0.1);
    }
    
    .bg-primary-light {
        background-color: rgba(13, 110, 253, 0.1);
    }
    
    .bg-secondary-light {
        background-color: rgba(108, 117, 125, 0.1);
    }
    
    .module-check-item .form-check-input {
        position: absolute;
        top: 15px;
        right: 15px;
        z-index: 2;
        transform: scale(1.2);
    }
    
    .module-check-item .form-check-label {
        cursor: pointer;
        margin-bottom: 0;
    }
    
    .hover-shadow {
        transition: all 0.3s ease;
        border-color: #e9ecef !important;
    }
    
    .hover-shadow:hover {
        border-color: #007bff !important;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 123, 255, 0.075);
        transform: translateY(-1px);
    }
    
    .form-check-input:checked + .form-check-label .hover-shadow {
        border-color: #007bff !important;
        background-color: rgba(13, 110, 253, 0.05);
        box-shadow: 0 0.25rem 0.5rem rgba(13, 110, 253, 0.15);
    }
    
    .input-group-text {
        border-color: #dee2e6;
    }
    
    .border-start-0 {
        border-left: 0 !important;
    }
    
    .border-end-0 {
        border-right: 0 !important;
    }
</style>

<script>
    $(document).ready(function () {
        // Handle module switch toggle
        $(document).on('change', '#module_switch', function () {
            if ($(this).is(':checked')) {
                $('.omx_flow_content').removeClass('d-none');
            } else {
                $('.omx_flow_content').addClass('d-none');
                // Uncheck all module permissions when hiding
                $('input[name="module_permissions[]"]').prop('checked', false);
                // Clear additional fields when hiding
                $('#company_name').val('');
                $('#company_description').val('');
            }
        });

        // Select all modules
        $(document).on('click', '#select_all_modules', function () {
            $('input[name="module_permissions[]"]').prop('checked', true);
        });

        // Clear all modules
        $(document).on('click', '#clear_all_modules', function () {
            $('input[name="module_permissions[]"]').prop('checked', false);
        });

        // Existing password switch functionality
        $(document).on('change', '#password_switch', function () {
            if ($(this).is(':checked')) {
                $('.ps_div').removeClass('d-none');
            } else {
                $('.ps_div').addClass('d-none');
            }
        });
    });
</script>

@push('script-page')
    <script>
        $(document).ready(function() {
            // Module checkbox toggle
            $('.module-checkbox').on('change', function() {
                var module = $(this).data('module');
                var permissionsSection = $('#permissions_' + module);
                var permissionCheckboxes = permissionsSection.find('.permission-checkbox');
                var selectButtons = $('#selectButtons_' + module);
                
                if ($(this).is(':checked')) {
                    permissionsSection.show();
                    selectButtons.show();
                } else {
                    permissionsSection.hide();
                    selectButtons.hide();
                    permissionCheckboxes.prop('checked', false);
                }
            });

            // If any permission is checked, ensure module is checked
            $('.permission-checkbox').on('change', function() {
                var module = $(this).data('module');
                var moduleCheckbox = $('#module_' + module);
                var permissionCheckboxes = $('input[name="permissions[' + module + '][]"]');
                var checkedPermissions = permissionCheckboxes.filter(':checked');
                var selectButtons = $('#selectButtons_' + module);
                
                if (checkedPermissions.length > 0) {
                    moduleCheckbox.prop('checked', true);
                    $('#permissions_' + module).show();
                    selectButtons.show();
                } else {
                    moduleCheckbox.prop('checked', false);
                    $('#permissions_' + module).hide();
                    selectButtons.hide();
                }
            });

            // Select All Modules
            $('#selectAllModules').on('click', function() {
                $('.module-checkbox').prop('checked', true).trigger('change');
            });

            // Deselect All Modules
            $('#deselectAllModules').on('click', function() {
                $('.module-checkbox').prop('checked', false).trigger('change');
            });

            // Select All permissions for a specific module
            $('.select-all-module').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .permission-checkbox').prop('checked', true);
            });

            // Select Create permissions for a specific module
            $('.select-create').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .create-permission').prop('checked', true);
            });

            // Select Read permissions for a specific module
            $('.select-read').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .read-permission').prop('checked', true);
            });

            // Select Delete permissions for a specific module
            $('.select-delete').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .delete-permission').prop('checked', true);
            });
        });
    </script>
@endpush
