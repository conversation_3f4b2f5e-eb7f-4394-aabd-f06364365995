<table class="table table-striped align-middle mb-0">
    <thead class="table-light">
        <tr>
            <th>{{ __('Company Name') }}</th>
            <th>{{ __('Email') }}</th>
            <th>{{ __('Plan') }}</th>
            <th>{{ __('Last Login') }}</th>
            <th>{{ __('Users') }}</th>
            <th>{{ __('Customers') }}</th>
            <th>{{ __('Vendors') }}</th>
            <th>{{ __('Status') }}</th>
            <th>{{ __('Actions') }}</th>
        </tr>
    </thead>
    <tbody>
        @foreach ($companies as $company)
            <tr>
                <td class="fw-semibold">
                    <div class="d-flex align-items-center gap-2">
                        <img src="{{ !empty($company->avatar) ? Utility::get_file('uploads/avatar/') . $company->avatar : asset(Storage::url('uploads/avatar/avatar.png')) }}" alt="user-image" class="rounded-circle border border-primary" style="width:36px;height:36px;object-fit:cover;">
                        <span>{{ $company->name }}</span>
                    </div>
                </td>
                <td class="text-break">{{ $company->email }}</td>
                <td>
                    <span class="badge bg-primary">
                        {{ !empty($company->plan) && is_object($company->plan) ? $company->plan->name : (!empty($company->plan) ? 'Plan ID: ' . $company->plan : __('No Plan')) }}
                    </span>
                </td>
                <td>
                    @if($company->last_login_at)
                        {{ \Carbon\Carbon::parse($company->last_login_at)->format('M d, Y \a\t H:i') }}
                    @else
                        <span class="text-muted">{{ __('Never logged in') }}</span>
                    @endif
                </td>
                <td>{{ $company->totalCompanyUser($company->id) }}</td>
                <td>{{ $company->totalCompanyCustomer($company->id) }}</td>
                <td>{{ $company->totalCompanyVender($company->id) }}</td>
                <td>
                    @if ($company->delete_status == 0)
                        <span class="badge bg-danger">{{ __('Soft Deleted') }}</span>
                    @else
                        <span class="badge bg-success">{{ __('Active') }}</span>
                    @endif
                </td>
                <td>
                    <div class="btn-group">
                        @if (\Auth::user()->type == 'system admin' || (\Auth::user()->type == 'staff' && \Auth::user()->can('manage sub accounts')))
                        <a href="{{ route('login.with.company', $company->id) }}"
                            data-bs-toggle="tooltip"
                            title="{{ __('Login') }}"
                            style="display:inline-flex;align-items:center;gap:8px;padding:0 16px;height:40px;border-radius:999px;background:linear-gradient(to right, #065f46, #0f766e);color:white;font-weight:500;box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
                            onmouseover="this.style.transform='scale(1.05)'"
                            onmouseout="this.style.transform='scale(1)'">
                            <i class="ti ti-replace" style="font-size:16px;"></i>
                            <span>{{ __('Login') }}</span>
                        </a>
                        @endif
                        @if (\Auth::user()->type == 'system admin' || (\Auth::user()->type == 'staff' && \Auth::user()->can('edit sub accounts')))
                        <a href="{{ route('system-admin.companies.edit', $company->id) }}"
                            data-bs-toggle="tooltip"
                            title="{{ __('Edit') }}"
                            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;border-radius:50%;
                                    background:linear-gradient(to right, #065f46, #0f766e);color:white;
                                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out; margin-left:5px;"
                            onmouseover="this.style.transform='scale(1.1)'"
                            onmouseout="this.style.transform='scale(1)'">
                            <i class="ti ti-pencil"></i>
                        </a>
                        @endif
                        @if (\Auth::user()->type == 'system admin' || (\Auth::user()->type == 'staff' && \Auth::user()->can('delete sub accounts')))
                            {!! Form::open(['method' => 'DELETE', 'route' => ['system-admin.companies.destroy', $company['id']], 'id' => 'delete-form-' . $company['id'], 'style' => 'display:inline-block']) !!}
                            <button type="button"
                                data-bs-toggle="tooltip"
                                title="{{ __('Delete Company') }}"
                                class="bs-pass-para-delete"
                                data-confirm-text="{{ __('Are you sure you want to delete this company?') }}"
                                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                                        border-radius:50%;background:white;color:#dc2626;border:1.5px solid #dc2626;
                                        box-shadow:0 4px 6px rgba(0,0,0,0.05);transition:transform 0.2s ease-in-out; margin-left:5px;"
                                onmouseover="this.style.transform='scale(1.1)'"
                                onmouseout="this.style.transform='scale(1)'">
                                <i class="ti ti-trash"></i>
                            </button>
                            {!! Form::close() !!}
                            @if ($company->delete_status != 0)
                                {!! Form::open(['method' => 'DELETE', 'route' => ['system-admin.companies.force-destroy', $company['id']], 'id' => 'force-delete-form-' . $company['id'], 'style' => 'display:inline-block']) !!}
                                <button type="button"
                                    class="bs-pass-para-force"
                                    data-confirm-text="This will force delete the company even if modules have dependency errors. Are you sure?"
                                    title="{{ __('Force Delete') }}"
                                    data-bs-toggle="tooltip"
                                    style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                                            border-radius:50%;background:white;color:#dc2626;border:1.5px solid #dc2626;
                                            box-shadow:0 4px 6px rgba(0,0,0,0.05);transition:transform 0.2s ease-in-out; margin-left:5px;"
                                    onmouseover="this.style.transform='scale(1.1)'"
                                    onmouseout="this.style.transform='scale(1)'">
                                    <i class="ti ti-alert-triangle"></i>
                                </button>
                                {!! Form::close() !!}
                            @endif
                        @endif
                        @if ($company->is_enable_login == 1)
                        <a href="{{ route('system-admin.companies.login', \Crypt::encrypt($company->id)) }}"
                            title="{{ __('Login Disable') }}"
                            data-bs-toggle="tooltip"
                            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                                    border-radius:50%;background:white;color:#eab308;border:1.5px solid #eab308;
                                    box-shadow:0 4px 6px rgba(0,0,0,0.05);transition:transform 0.2s ease-in-out; margin-left:5px;"
                            onmouseover="this.style.transform='scale(1.1)'"
                            onmouseout="this.style.transform='scale(1)'">
                            <i class="ti ti-road-sign"></i>
                        </a>
                        @elseif ($company->is_enable_login == 0 && $company->password == null)
                        <a href="#"
                            data-url="{{ route('system-admin.companies.reset', \Crypt::encrypt($company->id)) }}"
                            data-ajax-popup="true"
                            data-size="md"
                            data-bs-toggle="tooltip"
                            title="{{ __('New Password') }}"
                            class="login_enable"
                            style="display:inline-flex;align-items:center;gap:8px;padding:0 16px;height:40px;border-radius:999px;
                                    background:linear-gradient(to right, #065f46, #0f766e);color:white;font-weight:500;
                                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
                            onmouseover="this.style.transform='scale(1.05)'"
                            onmouseout="this.style.transform='scale(1)'">
                            <i class="ti ti-road-sign" style="font-size:16px;"></i>
                        </a>
                        @else
                        <a href="{{ route('system-admin.companies.login', \Crypt::encrypt($company->id)) }}"
                            data-bs-toggle="tooltip"
                            title="{{ __('Login Enable') }}"
                            style="display:inline-flex;align-items:center;gap:8px;padding:0 16px;height:40px;border-radius:999px;
                                    background:linear-gradient(to right, #065f46, #0f766e);color:white;font-weight:500;
                                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out; margin-left:5px;"
                            onmouseover="this.style.transform='scale(1.05)'"
                            onmouseout="this.style.transform='scale(1)'">
                            <i class="ti ti-road-sign" style="font-size:16px;"></i>
                        </a>
                        @endif
                    </div>
                </td>
            </tr>
        @endforeach
    </tbody>
</table>
