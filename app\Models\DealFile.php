<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class DealFile extends Model
{
    protected $fillable = [
        'deal_id',
        'file_name',
        'original_name',
        'file_path',
        'file_type',
        'mime_type',
        'file_size',
        'uploaded_by'
    ];

    protected $casts = [
        'file_size' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the deal that owns the file
     */
    public function deal()
    {
        return $this->belongsTo(Deal::class);
    }

    /**
     * Get the user who uploaded the file
     */
    public function uploader()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get formatted file size
     */
    public function getFormattedSizeAttribute()
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get file icon based on file type
     */
    public function getIconAttribute()
    {
        $extension = strtolower($this->file_type ?? pathinfo($this->file_name, PATHINFO_EXTENSION));

        $icons = [
            // Images
            'jpg' => 'ti-photo',
            'jpeg' => 'ti-photo',
            'png' => 'ti-photo',
            'gif' => 'ti-photo',
            'svg' => 'ti-photo',
            'webp' => 'ti-photo',

            // Documents
            'pdf' => 'ti-file-text',
            'doc' => 'ti-file-description',
            'docx' => 'ti-file-description',
            'txt' => 'ti-file',
            'rtf' => 'ti-file-description',

            // Spreadsheets
            'xls' => 'ti-file-spreadsheet',
            'xlsx' => 'ti-file-spreadsheet',
            'csv' => 'ti-file-spreadsheet',

            // Presentations
            'ppt' => 'ti-presentation',
            'pptx' => 'ti-presentation',

            // Archives
            'zip' => 'ti-file-zip',
            'rar' => 'ti-file-zip',
            '7z' => 'ti-file-zip',
            'tar' => 'ti-file-zip',
            'gz' => 'ti-file-zip',

            // Code
            'html' => 'ti-code',
            'css' => 'ti-code',
            'js' => 'ti-code',
            'php' => 'ti-code',
            'py' => 'ti-code',
            'java' => 'ti-code',
            'cpp' => 'ti-code',
            'c' => 'ti-code',

            // Video
            'mp4' => 'ti-video',
            'avi' => 'ti-video',
            'mov' => 'ti-video',
            'wmv' => 'ti-video',
            'flv' => 'ti-video',
            'webm' => 'ti-video',

            // Audio
            'mp3' => 'ti-music',
            'wav' => 'ti-music',
            'flac' => 'ti-music',
        ];

        return $icons[$extension] ?? 'ti-file';
    }

    /**
     * Get file URL
     */
    public function getUrlAttribute()
    {
        return Storage::disk('public')->url($this->file_path);
    }

    /**
     * Check if file is an image
     */
    public function getIsImageAttribute()
    {
        $imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'];
        return in_array(strtolower($this->file_type), $imageTypes);
    }
}
