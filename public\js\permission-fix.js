/**
 * Permission Fix JavaScript
 * Handles module visibility issues after POST requests
 */

(function() {
    'use strict';

    // Track if we need to refresh permissions
    let permissionRefreshNeeded = false;

    // Function to refresh module visibility
    function refreshModuleVisibility() {
        // Check if we're on a page with modules
        const moduleElements = document.querySelectorAll('[data-module]');
        
        if (moduleElements.length > 0) {
            // Make an AJAX request to refresh module permissions
            fetch('/api/refresh-permissions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('Permissions refreshed successfully');
                    
                    // Optionally reload the page to ensure all modules are visible
                    if (data.reload_required) {
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    }
                }
            })
            .catch(error => {
                console.warn('Failed to refresh permissions:', error);
            });
        }
    }

    // Function to handle form submissions
    function handleFormSubmission(event) {
        const form = event.target;
        
        // Check if this is a user creation or update form
        if (form.action.includes('/users') || 
            form.action.includes('/user') || 
            form.action.includes('/create') ||
            form.action.includes('/store') ||
            form.action.includes('/update')) {
            
            permissionRefreshNeeded = true;
        }
    }

    // Function to handle successful form submissions
    function handleSuccessfulSubmission() {
        if (permissionRefreshNeeded) {
            setTimeout(() => {
                refreshModuleVisibility();
                permissionRefreshNeeded = false;
            }, 500);
        }
    }

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        // Listen for form submissions
        document.addEventListener('submit', handleFormSubmission);

        // Listen for successful AJAX responses (if using jQuery)
        if (typeof $ !== 'undefined') {
            $(document).ajaxSuccess(function(event, xhr, settings) {
                if (settings.type === 'POST' && permissionRefreshNeeded) {
                    handleSuccessfulSubmission();
                }
            });
        }

        // Listen for page visibility changes (when user returns to tab)
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden && permissionRefreshNeeded) {
                handleSuccessfulSubmission();
            }
        });

        // Auto-refresh permissions every 30 seconds if needed
        setInterval(function() {
            if (permissionRefreshNeeded) {
                refreshModuleVisibility();
                permissionRefreshNeeded = false;
            }
        }, 30000);
    });

    // Expose functions globally for manual use
    window.PermissionFix = {
        refresh: refreshModuleVisibility,
        markForRefresh: function() {
            permissionRefreshNeeded = true;
        }
    };

})();
