<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\PricingPlan;
use Illuminate\Support\Facades\Gate;

// Initialize Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== OMX Flow Permission Test ===\n\n";

try {
    // Test 1: Test the <NAME_EMAIL> for OMX Flow access
    echo "1. <NAME_EMAIL> for OMX Flow access...\n";
    $user = User::where('email', '<EMAIL>')->first();
    
    if ($user) {
        echo "✅ Found user: {$user->name} ({$user->email})\n";
        echo "   - User Type: {$user->type}\n";
        echo "   - Plan ID: {$user->plan}\n";
        
        $pricingPlan = $user->pricingPlan;
        if ($pricingPlan) {
            echo "✅ User has pricing plan: {$pricingPlan->name}\n";
            
            $hasOmxFlowInPlan = isset($pricingPlan->module_permissions['omx_flow']);
            echo "   - Has omx_flow module: " . ($hasOmxFlowInPlan ? 'YES' : 'NO') . "\n";
            
            if ($hasOmxFlowInPlan) {
                $omxFlowPermissions = $pricingPlan->module_permissions['omx_flow'];
                echo "   - OMX Flow permissions: " . json_encode($omxFlowPermissions) . "\n";
                $hasAccessPermission = in_array('access omx flow', $omxFlowPermissions);
                echo "   - Has 'access omx flow' permission: " . ($hasAccessPermission ? 'YES' : 'NO') . "\n";
            }
        }
        
        // Test hasModulePermission method
        $hasModulePermission = $user->hasModulePermission('omx_flow', 'access omx flow');
        echo "   - hasModulePermission('omx_flow', 'access omx flow'): " . ($hasModulePermission ? 'YES' : 'NO') . "\n";
        
        // Test Gate permission
        $hasGateAccess = Gate::forUser($user)->check('access omx flow');
        echo "   - Gate::check('access omx flow'): " . ($hasGateAccess ? 'YES' : 'NO') . "\n";
        
        if ($hasGateAccess) {
            echo "✅ User SHOULD see OMX Flow in sidebar\n";
        } else {
            echo "❌ User should NOT see OMX Flow in sidebar\n";
        }
    } else {
        echo "❌ User not found\n";
    }
    echo "\n";

    // Test 2: Create test plans to verify permission system
    echo "2. Creating test pricing plans...\n";
    
    // Plan WITHOUT OMX Flow
    $planWithoutOmxFlow = PricingPlan::create([
        'name' => 'Test Plan Without OMX Flow',
        'description' => 'Test plan without OMX Flow access',
        'price' => 0.00,
        'status' => 'active',
        'duration' => 'monthly',
        'module_permissions' => [
            'crm' => ['manage lead', 'view lead'],
            'automatish' => ['access automatish']
            // Note: NO omx_flow module
        ]
    ]);
    echo "✅ Created plan without OMX Flow: {$planWithoutOmxFlow->name}\n";

    // Plan WITH OMX Flow
    $planWithOmxFlow = PricingPlan::create([
        'name' => 'Test Plan With OMX Flow',
        'description' => 'Test plan with OMX Flow access',
        'price' => 15.00,
        'status' => 'active',
        'duration' => 'monthly',
        'module_permissions' => [
            'crm' => ['manage lead', 'view lead'],
            'omx_flow' => ['access omx flow', 'whatsapp_orders', 'campaigns', 'templates', 'chatbot']
        ]
    ]);
    echo "✅ Created plan with OMX Flow: {$planWithOmxFlow->name}\n\n";

    // Test 3: Create test users
    echo "3. Creating test users...\n";
    $userWithoutOmxFlow = User::create([
        'name' => 'Test User Without OMX Flow',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
        'type' => 'company',
        'plan' => $planWithoutOmxFlow->id
    ]);
    echo "✅ Created user without OMX Flow: {$userWithoutOmxFlow->name}\n";

    $userWithOmxFlow = User::create([
        'name' => 'Test User With OMX Flow',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
        'type' => 'company',
        'plan' => $planWithOmxFlow->id
    ]);
    echo "✅ Created user with OMX Flow: {$userWithOmxFlow->name}\n\n";

    // Test 4: Test permissions
    echo "4. Testing permissions...\n";
    
    // User WITHOUT OMX Flow
    $hasPermission = $userWithoutOmxFlow->hasModulePermission('omx_flow', 'access omx flow');
    $hasGateAccess = Gate::forUser($userWithoutOmxFlow)->check('access omx flow');
    echo "User WITHOUT OMX Flow:\n";
    echo "   - hasModulePermission: " . ($hasPermission ? 'YES' : 'NO') . "\n";
    echo "   - Gate access: " . ($hasGateAccess ? 'YES' : 'NO') . "\n";
    echo "   - Should show in sidebar: " . (!$hasPermission && !$hasGateAccess ? 'NO ✅' : 'YES ❌') . "\n\n";
    
    // User WITH OMX Flow
    $hasPermission = $userWithOmxFlow->hasModulePermission('omx_flow', 'access omx flow');
    $hasGateAccess = Gate::forUser($userWithOmxFlow)->check('access omx flow');
    echo "User WITH OMX Flow:\n";
    echo "   - hasModulePermission: " . ($hasPermission ? 'YES' : 'NO') . "\n";
    echo "   - Gate access: " . ($hasGateAccess ? 'YES' : 'NO') . "\n";
    echo "   - Should show in sidebar: " . ($hasPermission && $hasGateAccess ? 'YES ✅' : 'NO ❌') . "\n\n";

    // Cleanup
    echo "5. Cleaning up test data...\n";
    $userWithoutOmxFlow->delete();
    $userWithOmxFlow->delete();
    $planWithoutOmxFlow->delete();
    $planWithOmxFlow->delete();
    echo "✅ Test data cleaned up\n";

} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Test Complete ===\n";
