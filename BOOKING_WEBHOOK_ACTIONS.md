# Booking System Webhook Actions

## Overview

The booking system webhook actions have been successfully integrated into the existing CRM webhook system. These actions allow the booking system to trigger webhooks to integrated modules when booking-related events occur.

## Added Webhook Actions

### 1. Appointment Scheduled
- **Action**: `booking.appointment_scheduled`
- **Constant**: `CrmWebhookActions::APPOINTMENT_SCHEDULED`
- **Dispatcher Method**: `dispatchAppointmentScheduled($appointment, $additionalData = [])`
- **Triggers**: When a new appointment is scheduled

### 2. Appointment Rescheduled
- **Action**: `booking.appointment_rescheduled`
- **Constant**: `CrmWebhookActions::APPOINTMENT_RESCHEDULED`
- **Dispatcher Method**: `dispatchAppointmentRescheduled($appointment, $oldDateTime, $newDateTime, $additionalData = [])`
- **Triggers**: When an existing appointment is rescheduled

### 3. Appointment Cancelled
- **Action**: `booking.appointment_cancelled`
- **Constant**: `CrmWebhookActions::APPOINTMENT_CANCELLED`
- **Dispatcher Method**: `dispatchAppointmentCancelled($appointment, $reason = null, $additionalData = [])`
- **Triggers**: When an appointment is cancelled

### 4. Appointment Reminder Time Reached
- **Action**: `booking.appointment_reminder_time_reached`
- **Constant**: `CrmWebhookActions::APPOINTMENT_REMINDER_TIME_REACHED`
- **Dispatcher Method**: `dispatchAppointmentReminderTimeReached($appointment, $reminderType, $additionalData = [])`
- **Triggers**: When it's time to send appointment reminders

### 5. Event Created
- **Action**: `booking.event_created`
- **Constant**: `CrmWebhookActions::EVENT_CREATED`
- **Dispatcher Method**: `dispatchEventCreated($event, $additionalData = [])`
- **Triggers**: When a new event is created

### 6. Booking Form Submitted
- **Action**: `booking.booking_form_submitted`
- **Constant**: `CrmWebhookActions::BOOKING_FORM_SUBMITTED`
- **Dispatcher Method**: `dispatchBookingFormSubmitted($booking, $formData = [], $additionalData = [])`
- **Triggers**: When a booking form is submitted

### 7. Date Override Added
- **Action**: `booking.date_override_added`
- **Constant**: `CrmWebhookActions::DATE_OVERRIDE_ADDED`
- **Dispatcher Method**: `dispatchDateOverrideAdded($dateOverride, $additionalData = [])`
- **Triggers**: When a date override is added to the system

### 8. Recurring Appointment Created
- **Action**: `booking.recurring_appointment_created`
- **Constant**: `CrmWebhookActions::RECURRING_APPOINTMENT_CREATED`
- **Dispatcher Method**: `dispatchRecurringAppointmentCreated($recurringAppointment, $additionalData = [])`
- **Triggers**: When a recurring appointment series is created

### 9. Appointment Location Changed
- **Action**: `booking.appointment_location_changed`
- **Constant**: `CrmWebhookActions::APPOINTMENT_LOCATION_CHANGED`
- **Dispatcher Method**: `dispatchAppointmentLocationChanged($appointment, $oldLocation, $newLocation, $additionalData = [])`
- **Triggers**: When an appointment's location is changed

## Usage Examples

### Basic Usage in Controllers

```php
use App\Services\CrmWebhookDispatcher;

// In your booking controller
$webhookDispatcher = new CrmWebhookDispatcher();

// When scheduling an appointment
$webhookDispatcher->dispatchAppointmentScheduled($appointment);

// When rescheduling with date change tracking
$webhookDispatcher->dispatchAppointmentRescheduled(
    $appointment, 
    $oldDateTime, 
    $newDateTime
);

// When cancelling with reason
$webhookDispatcher->dispatchAppointmentCancelled(
    $appointment, 
    'Customer requested cancellation'
);

// When processing booking form
$webhookDispatcher->dispatchBookingFormSubmitted($booking, $formData);
```

### Error Handling Pattern

```php
try {
    // Your booking logic here
    $appointment = createAppointment($data);
    
    // Dispatch webhook (wrapped in try-catch as per user preference)
    try {
        $webhookDispatcher = new CrmWebhookDispatcher();
        $webhookDispatcher->dispatchAppointmentScheduled($appointment);
    } catch (\Exception $e) {
        \Log::error('Webhook dispatch failed: ' . $e->getMessage());
        // Don't fail the main operation due to webhook issues
    }
    
    return response()->json(['success' => true]);
    
} catch (\Exception $e) {
    \Log::error('Appointment creation failed: ' . $e->getMessage());
    return response()->json(['success' => false], 500);
}
```

## Webhook Payload Examples

### Appointment Scheduled Payload
```json
{
    "action": "booking.appointment_scheduled",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "data": {
        "id": 789,
        "title": "Consultation Meeting",
        "start_time": "2024-01-20T14:00:00.000Z",
        "end_time": "2024-01-20T15:00:00.000Z",
        "location": "Conference Room A",
        "client_name": "Jane Smith",
        "client_email": "<EMAIL>",
        "status": "confirmed",
        "service": {
            "id": 5,
            "name": "Business Consultation"
        }
    },
    "user_id": 456,
    "source": {
        "system": "CRM",
        "version": "1.0",
        "url": "https://your-crm.com"
    }
}
```

### Appointment Rescheduled Payload
```json
{
    "action": "booking.appointment_rescheduled",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "data": {
        "id": 789,
        "title": "Rescheduled Meeting",
        "old_date_time": "2024-01-20T14:00:00.000Z",
        "new_date_time": "2024-01-21T16:00:00.000Z",
        "client_name": "Jane Smith",
        "reason": "Client requested change"
    },
    "user_id": 456,
    "source": {
        "system": "CRM",
        "version": "1.0",
        "url": "https://your-crm.com"
    }
}
```

## Files Modified

### Core Webhook System Files
1. **`app/Constants/CrmWebhookActions.php`** - Added booking system action constants
2. **`app/Services/CrmWebhookDispatcher.php`** - Added booking system dispatch methods
3. **`app/Http/Controllers/WebhookTestController.php`** - Added test data for booking actions
4. **`CRM_WEBHOOK_SYSTEM.md`** - Updated documentation with booking system examples
5. **`app/Http/Controllers/BookingWebhookExampleController.php`** - Created example controller

### Integrated Booking Controllers
6. **`app/Http/Controllers/BookingController.php`** - Added webhook calls for booking form submissions
7. **`app/Http/Controllers/AppointmentController.php`** - Added webhook calls for appointment scheduling
8. **`app/Http/Controllers/AppointmentBookingController.php`** - Added webhook calls for appointment booking operations
9. **`app/Http/Controllers/CalendarEventController.php`** - Added webhook calls for event creation

## Integration Points

The booking system webhooks integrate seamlessly with the existing CRM webhook infrastructure:

- Uses the same `ModuleWebhookService` for HTTP requests
- Follows the same error handling and logging patterns
- Uses the same module integration configuration
- Supports the same testing interface at `/webhook-test`

## Detailed Integration Summary

### BookingController Integration
- **`store()` method**: Dispatches `booking.booking_form_submitted` when authenticated users create bookings
- **`publicStore()` method**: Dispatches `booking.booking_form_submitted` when public users create bookings
- **Webhook Data**: Includes form data, client information, and booking type (public/authenticated)

### AppointmentController Integration
- **`store()` method**: Dispatches `booking.appointment_scheduled` when appointments are created
- **Webhook Data**: Includes appointment details, client name, date/time, timezone, and calendar event info

### AppointmentBookingController Integration
- **`store()` method**: Dispatches `booking.appointment_scheduled` when appointment bookings are created
- **`update()` method**:
  - Dispatches `booking.appointment_rescheduled` when date/time changes
  - Dispatches `booking.appointment_location_changed` when location changes
- **`destroy()` method**: Dispatches `booking.appointment_cancelled` when appointments are deleted
- **Webhook Data**: Includes comprehensive appointment details, old/new values for changes

### CalendarEventController Integration
- **`store()` method**: Dispatches `booking.event_created` when calendar events are created
- **Webhook Data**: Includes complete event details, availability settings, and slot generation results

## Testing

You can test the booking system webhooks using:

1. **Web Interface**: Navigate to `/webhook-test` and select booking actions
2. **Artisan Command**: Use `php artisan webhook:test-all` (if extended)
3. **Manual Testing**: Use the example controller methods

## Next Steps

To fully integrate these webhook actions into your booking system:

1. Replace the example data structures with your actual booking models
2. Add webhook dispatch calls to your existing booking controllers
3. Test the webhooks with your integrated modules
4. Monitor webhook logs for any issues
5. Update your booking system documentation to reference these webhook actions
