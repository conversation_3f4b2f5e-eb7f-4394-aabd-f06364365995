@extends('layouts.admin')
@section('page-title')
    {{__('Dashboard')}}
@endsection
@section('action-btn')
<div class="float-end">
    <a href="#"
       class="btn btn-sm text-white d-flex align-items-center gap-1"
       data-bs-toggle="modal"
       data-bs-target="#dashboardSwitcherModal"
       data-bs-toggle="tooltip"
       title="{{ __('Change Dashboard') }}"
       style="background: linear-gradient(135deg, #1b5e20, #0d47a1); border: none;">
        <i class="ti ti-layout-dashboard"></i>
        <span class="d-none d-md-inline"><i class="ti ti-switch-vertical" style="margin-right: 5px;"></i>{{ __('Switch') }}</span>
    </a>
</div>

    <!-- Dashboard Switcher Modal -->
    <div class="modal fade" id="dashboardSwitcherModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('Select Dashboard') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="dashboard-option card h-100 cursor-pointer" data-dashboard="account">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <i class="ti ti-wallet fs-1 text-primary"></i>
                                    </div>
                                    <h5 class="mb-0">{{ __('Account Dashboard') }}</h5>
                                    <small class="text-muted">{{ __('View financial overview') }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="dashboard-option card h-100 cursor-pointer" data-dashboard="crm">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <i class="ti ti-users fs-1 text-info"></i>
                                    </div>
                                    <h5 class="mb-0">{{ __('CRM Dashboard') }}</h5>
                                    <small class="text-muted">{{ __('Customer relationship management') }}</small>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="col-md-6">
                            <div class="dashboard-option card h-100 cursor-pointer" data-dashboard="hrm">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <i class="ti ti-id fs-1 text-warning"></i>
                                    </div>
                                    <h5 class="mb-0">{{ __('HRM Dashboard') }}</h5>
                                    <small class="text-muted">{{ __('Human resource management') }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="dashboard-option card h-100 cursor-pointer" data-dashboard="pos">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <i class="ti ti-cash fs-1 text-success"></i>
                                    </div>
                                    <h5 class="mb-0">{{ __('POS Dashboard') }}</h5>
                                    <small class="text-muted">{{ __('Point of sale system') }}</small>
                                </div>
                            </div>
                        </div> -->
                        <div class="col-md-6">
                            <div class="dashboard-option card h-100 cursor-pointer" data-dashboard="project">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <i class="fas fa-project-diagram fs-1 text-success"></i>
                                    </div>
                                    <h5 class="mb-0">{{ __('Project Dashboard') }}</h5>
                                    <small class="text-muted">{{ __('Project management') }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('script-page')
<script>
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Dashboard Switcher
    document.addEventListener('DOMContentLoaded', function() {
        // Get all dashboard options
        const dashboardOptions = document.querySelectorAll('.dashboard-option');
        
        // Add click event to each option
        dashboardOptions.forEach(option => {
            option.addEventListener('click', function() {
                const dashboardType = this.getAttribute('data-dashboard');
                
                // Store the selected dashboard in localStorage
                localStorage.setItem('selectedDashboard', dashboardType);
                
                // Determine the route based on dashboard type
                let route = '{{ route("dashboard") }}';
                
                switch(dashboardType) {
                    case 'crm':
                        route = '{{ route("crm.dashboard") }}';
                        break;
                    case 'hrm':
                        route = '{{ route("hrm.dashboard") }}';
                        break;
                    case 'pos':
                        route = '{{ route("pos.dashboard") }}';
                        break;
                    case 'project':
                        route = '{{ route("project.dashboard") }}';
                        break;
                        // Default is account dashboard
                }
                
                // Redirect to the selected dashboard
                window.location.href = route;
            });
        });
        
        // Highlight current dashboard in modal when opened
        const modal = document.getElementById('dashboardSwitcherModal');
        if (modal) {
            modal.addEventListener('show.bs.modal', function () {
                const currentPath = window.location.pathname;
                dashboardOptions.forEach(option => {
                    const dashboardType = option.getAttribute('data-dashboard');
                    if (
                        (dashboardType === 'account' && currentPath.includes('account-dashboard')) ||
                        (dashboardType === 'crm' && currentPath.includes('crm-dashboard')) ||
                        (dashboardType === 'hrm' && currentPath.includes('hrm-dashboard')) ||
                        (dashboardType === 'pos' && currentPath.includes('pos-dashboard')) ||
                        (dashboardType === 'project' && currentPath.includes('project-dashboard'))
                    ) {
                        option.classList.add('border-primary');
                    } else {
                        option.classList.remove('border-primary');
                    }
                });
            });
        }
    });
</script>
@endpush
@push('script-page')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Chart type options for each chart
const chartTypeOptions = ['pie', 'bar', 'line', 'doughnut', 'horizontalBar'];
const chartTypeLabels = {
    pie: 'Pie',
    bar: 'Bar',
    line: 'Line',
    doughnut: 'Donut',
    horizontalBar: 'Horizontal Bar'
};
// Default chart data for each chart (should match your real data)
const chartDataSets = {
    leadsPieChart: {
        labels: ['Leads', 'Deals', 'Contracts'],
        data: [{{ $crm_data['total_leads'] ?? 0 }}, {{ $crm_data['total_deals'] ?? 0 }}, {{ $crm_data['total_contracts'] ?? 0 }}],
        backgroundColor: ['#2e7d32', '#4caf50', '#81c784']
    },
    dealsPieChart: {
        labels: ['Leads', 'Deals', 'Contracts'],
        data: [{{ $crm_data['total_leads'] ?? 0 }}, {{ $crm_data['total_deals'] ?? 0 }}, {{ $crm_data['total_contracts'] ?? 0 }}],
        backgroundColor: ['#2e7d32', '#4caf50', '#81c784']
    },
    contractsPieChart: {
        labels: ['Leads', 'Deals', 'Contracts'],
        data: [{{ $crm_data['total_leads'] ?? 0 }}, {{ $crm_data['total_deals'] ?? 0 }}, {{ $crm_data['total_contracts'] ?? 0 }}],
        backgroundColor: ['#2e7d32', '#4caf50', '#81c784']
    }
};

let currentChartKey = null;
let previewChart = null;
let mainCharts = {};

function getStoredChartType(chartKey) {
    return localStorage.getItem('chartType_' + chartKey) || 'doughnut';
}
function setStoredChartType(chartKey, type) {
    localStorage.setItem('chartType_' + chartKey, type);
}

function getChartJsType(type) {
    if(type === 'horizontalBar') return 'bar'; // Chart.js v3+ uses 'bar' with indexAxis: 'y'
    return type;
}
function getChartJsOptions(type) {
    if(type === 'horizontalBar') {
        return {
            indexAxis: 'y',
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { position: 'right' } }
        };
    }
    return {
        responsive: true,
        maintainAspectRatio: false,
        plugins: { legend: { position: 'right' } }
    };
}

function renderChartJs(ctx, chartKey, type) {
    const dataSet = chartDataSets[chartKey];
    return new Chart(ctx, {
        type: getChartJsType(type),
        data: {
            labels: dataSet.labels,
            datasets: [{
                data: dataSet.data,
                backgroundColor: dataSet.backgroundColor
            }]
        },
        options: getChartJsOptions(type)
    });
}

// Open modal with correct chart type and preview
$(document).on('click', '.chart-settings-btn', function() {
    currentChartKey = $(this).data('chart');
    const savedType = getStoredChartType(currentChartKey);
    $('.chart-type-option').removeClass('active');
    $(`.chart-type-option[data-type="${savedType}"]`).addClass('active');
    // Render preview
    setTimeout(() => {
        if(previewChart) previewChart.destroy();
        const ctx = document.getElementById('chartPreviewCanvas').getContext('2d');
        previewChart = renderChartJs(ctx, currentChartKey, savedType);
    }, 200);
});
// Change preview on chart type select
$(document).on('click', '.chart-type-option', function() {
    $('.chart-type-option').removeClass('active');
    $(this).addClass('active');
    const type = $(this).data('type');
    if(previewChart) previewChart.destroy();
    const ctx = document.getElementById('chartPreviewCanvas').getContext('2d');
    previewChart = renderChartJs(ctx, currentChartKey, type);
});
// Save and update main chart
$('#saveChartTypeBtn').on('click', function() {
    const selectedType = $('.chart-type-option.active').data('type');
    setStoredChartType(currentChartKey, selectedType);
    // Update main chart
    if(mainCharts[currentChartKey]) mainCharts[currentChartKey].destroy();
    const ctx = document.getElementById(currentChartKey).getContext('2d');
    mainCharts[currentChartKey] = renderChartJs(ctx, currentChartKey, selectedType);
    $('#chartSettingsModal').modal('hide');
});
// Initialize all main charts on page load
function initAllMainCharts() {
    Object.keys(chartDataSets).forEach(chartKey => {
        const type = getStoredChartType(chartKey);
        const ctx = document.getElementById(chartKey);
        if(ctx) {
            mainCharts[chartKey] = renderChartJs(ctx.getContext('2d'), chartKey, type);
        }
    });
}
// Load Chart.js if not loaded, then init
if (typeof Chart === 'undefined') {
    var script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
    script.onload = initAllMainCharts;
    document.head.appendChild(script);
} else {
    initAllMainCharts();
}
</script>
@endpush

@push('script-page')
<script src="{{ asset('assets/js/plugins/apexcharts.min.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get the data from PHP
        const leadsData = {{ $crm_data['total_leads'] ?? 0 }};
        const dealsData = {{ $crm_data['total_deals'] ?? 0 }};
        const contractsData = {{ $crm_data['total_contracts'] ?? 0 }};
        
        // Calculate percentages
        const total = leadsData + dealsData + contractsData;
        const leadsPercent = total > 0 ? Math.round((leadsData / total) * 100) : 0;
        const dealsPercent = total > 0 ? Math.round((dealsData / total) * 100) : 0;
        const contractsPercent = total > 0 ? Math.round((contractsData / total) * 100) : 0;

        // Common chart options
        const chartOptions = {
            chart: {
                type: 'donut',
                height: 300,
                toolbar: { show: false },
                fontFamily: 'inherit',
                animations: { enabled: true }
            },
            colors: ['#2e7d32', '#4caf50', '#81c784'],
            legend: {
                position: 'bottom',
                horizontalAlign: 'center',
                fontSize: '13px',
                itemMargin: { horizontal: 10, vertical: 5 },
                markers: { width: 10, height: 10, radius: 5 }
            },
            plotOptions: {
                pie: {
                    donut: {
                        size: '70%',
                        labels: {
                            show: true,
                            total: { show: true, label: 'Total', fontSize: '16px', color: '#374151' },
                            value: { fontSize: '24px', color: '#111827', fontWeight: 700 }
                        }
                    }
                }
            },
            dataLabels: { enabled: false },
            stroke: { width: 0 },
            tooltip: {
                y: {
                    formatter: function(val) { return val; }
                }
            }
        };

        // Initialize charts when DOM is ready
            // Leads Chart
            const leadsChart = new ApexCharts(document.querySelector("#leadsPieChart"), {
                ...chartOptions,
                series: [leadsData, dealsData, contractsData],
                labels: ['Leads', 'Deals', 'Contracts'],
                plotOptions: {
                    pie: {
                        donut: {
                            size: '70%',
                            labels: {
                                show: true,
                                total: { 
                                    show: true, 
                                    label: 'Total', 
                                    fontSize: '16px',
                                    color: '#374151',
                                    formatter: () => total
                                },
                                value: {
                                    formatter: () => leadsPercent + '%',
                                    fontSize: '24px',
                                    fontWeight: 700,
                                    color: '#111827'
                                }
                            }
                        }
                    }
                }
            });
            leadsChart.render();

            // Deals Chart
            const dealsChart = new ApexCharts(document.querySelector("#dealsPieChart"), {
                ...chartOptions,
                series: [leadsData, dealsData, contractsData],
                labels: ['Leads', 'Deals', 'Contracts'],
                plotOptions: {
                    pie: {
                        donut: {
                            size: '70%',
                            labels: {
                                show: true,
                                total: { 
                                    show: true, 
                                    label: 'Total', 
                                    fontSize: '16px',
                                    color: '#374151',
                                    formatter: () => total
                                },
                                value: {
                                    formatter: () => dealsPercent + '%',
                                    fontSize: '24px',
                                    fontWeight: 700,
                                    color: '#111827'
                                }
                            }
                        }
                    }
                }
            });
            dealsChart.render();

            // Contracts Chart
            const contractsChart = new ApexCharts(document.querySelector("#contractsPieChart"), {
                ...chartOptions,
                series: [leadsData, dealsData, contractsData],
                labels: ['Leads', 'Deals', 'Contracts'],
                plotOptions: {
                    pie: {
                        donut: {
                            size: '70%',
                            labels: {
                                show: true,
                                total: { 
                                    show: true, 
                                    label: 'Total', 
                                    fontSize: '16px',
                                    color: '#374151',
                                    formatter: () => total
                                },
                                value: {
                                    formatter: () => contractsPercent + '%',
                                    fontSize: '24px',
                                    fontWeight: 700,
                                    color: '#111827'
                                }
                            }
                        }
                    }
                }
            });
            contractsChart.render();

        // Handle window resize
        window.addEventListener('resize', function() {
            setTimeout(function() {
                leadsChart.updateOptions({
                    chart: { height: 300 }
                });
                dealsChart.updateOptions({
                    chart: { height: 300 }
                });
                contractsChart.updateOptions({
                    chart: { height: 300 }
                });
            }, 300);
        });
    });
</script>

<style>
    .apexcharts-canvas {
        margin: 0 auto;
    }
    .card {
        border: none;
        border-radius: 0.75rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    .card-body {
        padding: 1.25rem;
    }
    .chart-container {
        position: relative;
        height: 280px;
        width: 100%;
    }
    .apexcharts-legend {
        padding: 10px 0 0;
        margin-top: 10px;
        border-top: 1px solid #eee;
    }
    .apexcharts-legend-text {
        color: #6B7280 !important;
        font-size: 13px !important;
    }
</style>
@endpush

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item">{{__('CRM')}}</li>
@endsection
@section('content')
<div class="row mb-4 gy-3">
    <div class="col-xl-4 col-sm-6 col-12 crm-dash-card">
        <div class="crm-card-inner card mb-0 p-3 d-flex flex-column justify-content-between" style="height: 100%; box-shadow: 0 4px 20px rgba(46, 125, 50, 0.2); border-left: 4px solid #2e7d32; background-color: #ECF5F3;">
            <!-- Top Content -->
            <div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <i class="fas fa-user-alt fa-2x" style="color: #0caf60;"></i><h5 style="margin-right: 100px;">{{__('Top Engagement Overview')}}</h5>
                    <div class="text-end d-flex align-items-center justify-content-end gap-2">
                        <h4 class="mb-0">{{ $crm_data['total_leads'] }}</h4>
                    </div>
                </div>
            </div>
            <!-- Bottom H2 Link -->
            <div class="mt-4 pt-2">
                <h2 class="h5 text-black m-0">
                    <a href="{{ route('leads.index') }}" class="text-black" style="margin-left: 30px;">
                        {{ __('Lead ') }}<i class="fas fa-external-link-alt" style="font-size: 0.8rem;"></i>
                    </a>
                </h2>
            </div>
        </div>
    </div>
    <div class="col-xl-4 col-sm-6 col-12 crm-dash-card">
        <div class="crm-card-inner card mb-0 p-3 d-flex flex-column justify-content-between" style="height: 100%; box-shadow: 0 4px 20px rgba(46, 125, 50, 0.2); border-left: 4px solid #2e7d32; background-color: #ECF5F3;">
            <!-- Top Content -->
            <div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <i class="fas fa-handshake fa-2x" style="color: #0caf60;"></i><h5 style="margin-right: 100px;">{{__('Top Performing Sources')}}</h5>
                    <div class="text-end d-flex align-items-center justify-content-end gap-2">
                        <h4 class="mb-0">{{ $crm_data['total_deals'] }}</h4>
                    </div>
                </div>
            </div>
            <!-- Bottom H2 Link -->
            <div class="mt-4 pt-2">
                <h2 class="h5 text-black m-0">
                    <a href="{{ route('deals.index') }}" class="text-black" style="margin-left: 30px;">
                        {{ __('Deal ') }}<i class="fas fa-external-link-alt" style="font-size: 0.8rem;"></i>
                    </a>
                </h2>
            </div>
        </div>
    </div>
    <div class="col-xl-4 col-sm-6 col-12 crm-dash-card">
        <div class="crm-card-inner card mb-0 p-3 d-flex flex-column justify-content-between" style="height: 100%; box-shadow: 0 4px 20px rgba(46, 125, 50, 0.2); border-left: 4px solid #2e7d32; background-color: #ECF5F3;">
            <!-- Top Content -->
            <div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <i class="fas fa-file-contract fa-2x" style="color: #0caf60;"></i><h5 style="margin-right: 100px;">{{__('Top Engagement Overview')}}</h5>
                    <div class="text-end d-flex align-items-center justify-content-end gap-2">
                        <h4 class="mb-0">{{ $crm_data['total_contracts'] }}</h4>
                    </div>
                </div>
            </div>
            <!-- Bottom H2 Link -->
            <div class="mt-4 pt-2">
                <h2 class="h5 text-black m-0">
                    <a href="{{ route('contract.index') }}" class="text-black" style="margin-left: 30px;">
                        {{ __('Contract ') }}<i class="fas fa-external-link-alt" style="font-size: 0.8rem;"></i>
                    </a>
                </h2>
            </div>
        </div>
    </div>
</div>

<!-- CRM Pie Charts Section -->
<div class="row mt-4" id="crm-charts">
    <!-- Leads by Status -->
    <div class="col-xxl-4 col-md-4 mb-4">
        <div class="card h-100" style="border-left: 4px solid #2e7d32; box-shadow: 0 4px 20px rgba(46, 125, 50, 0.1);">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold"><i class="fas fa-user-tag me-2"></i>{{ __('Leads by Status') }}</h6>
                <div class="d-flex align-items-center gap-2">
                    <span class="badge rounded-pill" style="background-color: rgba(46, 125, 50, 0.1); color: #2e7d32; font-weight: 600;">
                        {{ $crm_data['total_leads'] }} {{ __('Total') }}
                    </span>
                    <button class="btn btn-link p-0 chart-settings-btn" data-bs-toggle="modal" data-bs-target="#chartSettingsModal" data-chart="leadsPieChart" title="{{ __('Chart Settings') }}">
                        <i class="fas fa-cog fa-lg" style="color: #2e7d32;"></i>
                    </button>
                </div>
            </div>
            <div class="card-body p-4">
                <div class="chart-container" style="height: 280px;">
                    <canvas id="leadsPieChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <!-- Deals by Stage -->
    <div class="col-xxl-4 col-md-4 mb-4">
        <div class="card h-100" style="border-left: 4px solid #2e7d32; box-shadow: 0 4px 20px rgba(46, 125, 50, 0.1);">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold"><i class="fas fa-project-diagram me-2"></i>{{ __('Deals by Stage') }}</h6>
                <div class="d-flex align-items-center gap-2">
                    <span class="badge rounded-pill" style="background-color: rgba(46, 125, 50, 0.1); color: #2e7d32; font-weight: 600;">
                        {{ $crm_data['total_deals'] }} {{ __('Total') }}
                    </span>
                    <button class="btn btn-link p-0 chart-settings-btn" data-bs-toggle="modal" data-bs-target="#chartSettingsModal" data-chart="dealsPieChart" title="{{ __('Chart Settings') }}">
                        <i class="fas fa-cog fa-lg" style="color: #2e7d32;"></i>
                    </button>
                </div>
            </div>
            <div class="card-body p-4">
                <div class="chart-container" style="height: 280px;">
                    <canvas id="dealsPieChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <!-- Contracts by Type -->
    <div class="col-xxl-4 col-md-4 mb-4">
        <div class="card h-100" style="border-left: 4px solid #2e7d32; box-shadow: 0 4px 20px rgba(46, 125, 50, 0.1);">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold"><i class="fas fa-file-signature me-2"></i>{{ __('Contracts by Type') }}</h6>
                <div class="d-flex align-items-center gap-2">
                    <span class="badge rounded-pill" style="background-color: rgba(46, 125, 50, 0.1); color: #2e7d32; font-weight: 600;">
                        {{ $crm_data['total_contracts'] }} {{ __('Total') }}
                    </span>
                    <button class="btn btn-link p-0 chart-settings-btn" data-bs-toggle="modal" data-bs-target="#chartSettingsModal" data-chart="contractsPieChart" title="{{ __('Chart Settings') }}">
                        <i class="fas fa-cog fa-lg" style="color: #2e7d32;"></i>
                    </button>
                </div>
            </div>
            <div class="card-body p-4">
                <div class="chart-container" style="height: 280px;">
                    <canvas id="contractsPieChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Chart Settings Modal (Generic, place after all chart cards) -->
<div class="modal fade" id="chartSettingsModal" tabindex="-1" aria-labelledby="chartSettingsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="chartSettingsModalLabel">{{ __('Chart Settings') }}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-md-4 mb-3">
            <label class="form-label">{{ __('Chart Type') }}</label>
            <div class="d-flex flex-column gap-2">
              <button type="button" class="btn btn-outline-primary chart-type-option" data-type="pie">Pie</button>
              <button type="button" class="btn btn-outline-primary chart-type-option" data-type="bar">Bar</button>
              <button type="button" class="btn btn-outline-primary chart-type-option" data-type="line">Line</button>
              <button type="button" class="btn btn-outline-primary chart-type-option" data-type="doughnut">Donut</button>
              <button type="button" class="btn btn-outline-primary chart-type-option" data-type="horizontalBar">Horizontal Bar</button>
            </div>
          </div>
          <div class="col-md-8">
            <label class="form-label">{{ __('Live Preview') }}</label>
            <div class="border rounded p-2 bg-light" style="min-height: 320px;">
              <canvas id="chartPreviewCanvas" style="width:100%;height:300px;"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
        <button type="button" class="btn btn-success" id="saveChartTypeBtn">{{ __('Save Changes') }}</button>
      </div>
    </div>
  </div>
</div>
    <div class="row">
        <div class="col-lg-6 mb-4" >
            <div class="card h-100 mb-0" style="border-top: 4px solid #2e7d32;">
                <div class="card-header">
                    <h5><i class="fas fa-handshake" style="color: #0caf60;"></i>{{__(' Lead Status')}}</h5>
                </div>
                <div class="card-body">
                    <div class="row ">
                        @foreach($crm_data['lead_status'] as $status => $val)
                            <div class="col-md-6 col-sm-6 mb-4">
                                <div class="align-items-start">
                                    <div class="ms-2">
                                        <p class="text-sm mb-2">{{$val['lead_stage']}}</p>
                                        <h3 class="mb-2 text-primary">{{ $val['lead_percentage'] }}%</h3>
                                        <div class="progress mb-0">
                                            <div class="progress-bar bg-primary" style="width:{{$val['lead_percentage']}}%;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-4">
            <div class="card h-100 mb-0" style="border-top: 4px solid #2e7d32;">
                <div class="card-header">
                    <h5><i class="fas fa-handshake" style="color: #0caf60;"></i>{{__(' Deal Status')}}</h5>
                </div>
                <div class="card-body">
                    <div class="row ">
                        @foreach($crm_data['deal_status'] as $status => $val)
                            <div class="col-md-6 col-sm-6 mb-4">
                                <div class="align-items-start">
                                    <div class="ms-2">
                                        <p class="text-sm mb-2">{{$val['deal_stage']}}</p>
                                        <h3 class="mb-2 text-primary">{{ $val['deal_percentage'] }}%</h3>
                                        <div class="progress mb-0">
                                            <div class="progress-bar bg-primary" style="width:{{$val['deal_percentage']}}%;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xxl-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mt-1 mb-0"><i class="fas fa-file-contract" style="color: #0caf60;"></i>{{__(' Latest Contract')}}</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                            <tr>
                                <th></th>
                                <th>{{__('Subject')}}</th>
                                @if(\Auth::user()->type!='client')
                                <th>{{__('Client')}}</th>
                                @endif
                                <th>{{__('Project')}}</th>
                                <th>{{__('Contract Type')}}</th>
                                <th>{{__('Contract Value')}}</th>
                                <th>{{__('Start Date')}}</th>
                                <th>{{__('End Date')}}</th>
                            </tr>
                            </thead>
                            <tbody>
                            @forelse($crm_data['latestContract'] as $contract)
                                <tr>
                                    <td>
                                        <a href="{{route('contract.show',$contract->id)}}" class="btn btn-outline-primary">{{\Auth::user()->contractNumberFormat($contract->id)}}</a>
                                    </td>
                                    <td>{{ $contract->subject}}</td>
                                    @if(\Auth::user()->type!='client')
                                        <td>{{ !empty($contract->clients)?$contract->clients->name:'-' }}</td>
                                    @endif
                                    <td>{{ !empty($contract->projects)?$contract->projects->project_name:'-' }}</td>
                                    <td>{{ !empty($contract->types)?$contract->types->name:'' }}</td>
                                    <td>{{ \Auth::user()->priceFormat($contract->value) }}</td>
                                    <td>{{ \Auth::user()->dateFormat($contract->start_date )}}</td>
                                    <td>{{ \Auth::user()->dateFormat($contract->end_date )}}</td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="8">
                                        <div class="text-center">
                                            <h6>{{__('There is no latest contract')}}</h6>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
