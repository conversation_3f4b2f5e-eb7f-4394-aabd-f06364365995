<div class="row g-4">
    @if($taskFiles->count() > 0 || $projectAttachments->count() > 0)
        <!-- Task Files Section -->
        @if($taskFiles->count() > 0)
            <div class="col-12">
                <div class="modern-card">
                    <div class="p-4">
                        <div class="d-flex align-items-center justify-content-between mb-4">
                            <div>
                                <h5 class="mb-1 fw-bold text-dark">{{ __('Task Files') }}</h5>
                                <small class="text-muted">{{ $taskFiles->count() }} {{ __('files from tasks') }}</small>
                            </div>
                            <div class="info-icon">
                                <i class="ti ti-file-text"></i>
                            </div>
                        </div>
                        <div class="row g-3">
                            @foreach($taskFiles as $file)
                                @php
                                    $extension = pathinfo($file->name, PATHINFO_EXTENSION);
                                    $iconClass = match(strtolower($extension)) {
                                        'pdf' => 'ti-file-type-pdf text-danger',
                                        'doc', 'docx' => 'ti-file-type-doc text-primary',
                                        'xls', 'xlsx' => 'ti-file-type-xls text-success',
                                        'jpg', 'jpeg', 'png', 'gif' => 'ti-photo text-warning',
                                        'zip', 'rar' => 'ti-file-zip text-info',
                                        default => 'ti-file text-muted'
                                    };
                                @endphp
                                <div class="col-md-6 col-lg-4">
                                    <div class="file-item p-3 rounded-3" style="background: rgba(255, 255, 255, 0.7); border: 1px solid rgba(255, 255, 255, 0.3); transition: all 0.3s ease;">
                                        <div class="d-flex align-items-start gap-3">
                                            <div class="info-icon bg-white" style="width: 48px; height: 48px;">
                                                <i class="ti {{ $iconClass }}" style="font-size: 1.5rem;"></i>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1 fw-semibold text-dark">{{ $file->name }}</h6>
                                                <small class="text-muted d-block">{{ $file->file_size }}</small>
                                                <small class="text-muted d-block">
                                                    <i class="ti ti-checklist me-1"></i>{{ $file->task->name ?? 'Unknown Task' }}
                                                </small>
                                                <small class="text-muted d-block">
                                                    <i class="ti ti-calendar me-1"></i>{{ $file->created_at->format('M d, Y') }}
                                                </small>
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center mt-3">
                                            <div class="d-flex gap-2">
                                                <a href="{{ asset(Storage::url('uploads/tasks/'.$file->file)) }}" 
                                                   class="btn btn-sm btn-outline-primary" download>
                                                    <i class="ti ti-download"></i>
                                                </a>
                                                @if(in_array(strtolower($extension), ['jpg', 'jpeg', 'png', 'gif']))
                                                    <a href="{{ asset(Storage::url('uploads/tasks/'.$file->file)) }}" 
                                                       class="btn btn-sm btn-outline-info" target="_blank">
                                                        <i class="ti ti-eye"></i>
                                                    </a>
                                                @endif
                                            </div>
                                            @auth('web')
                                                @can('delete project task')
                                                    <a href="#" class="btn btn-sm btn-outline-danger delete-comment-file"
                                                       data-url="{{ route('comment.destroy.file', [$project->id, $file->task_id, $file->id]) }}">
                                                        <i class="ti ti-trash"></i>
                                                    </a>
                                                @endcan
                                            @endauth
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Project Attachments Section -->
        @if($projectAttachments->count() > 0)
            <div class="col-12">
                <div class="modern-card">
                    <div class="p-4">
                        <div class="d-flex align-items-center justify-content-between mb-4">
                            <div>
                                <h5 class="mb-1 fw-bold text-dark">{{ __('Project Attachments') }}</h5>
                                <small class="text-muted">{{ $projectAttachments->count() }} {{ __('project files') }}</small>
                            </div>
                            <div class="info-icon">
                                <i class="ti ti-paperclip"></i>
                            </div>
                        </div>
                        <div class="row g-3">
                            @foreach($projectAttachments as $attachment)
                                @php
                                    $file = \App\Models\Utility::get_file('uploads/tasks/');
                                    $extension = pathinfo($attachment->name, PATHINFO_EXTENSION);
                                    $iconClass = match(strtolower($extension)) {
                                        'pdf' => 'ti-file-type-pdf text-danger',
                                        'doc', 'docx' => 'ti-file-type-doc text-primary',
                                        'xls', 'xlsx' => 'ti-file-type-xls text-success',
                                        'jpg', 'jpeg', 'png', 'gif' => 'ti-photo text-warning',
                                        'zip', 'rar' => 'ti-file-zip text-info',
                                        default => 'ti-file text-muted'
                                    };
                                @endphp
                                <div class="col-md-6 col-lg-4">
                                    <div class="file-item p-3 rounded-3" style="background: rgba(255, 255, 255, 0.7); border: 1px solid rgba(255, 255, 255, 0.3); transition: all 0.3s ease;">
                                        <div class="d-flex align-items-start gap-3">
                                            <div class="info-icon bg-white" style="width: 48px; height: 48px;">
                                                <i class="ti {{ $iconClass }}" style="font-size: 1.5rem;"></i>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1 fw-semibold text-dark">{{ $attachment->name }}</h6>
                                                <small class="text-muted d-block">{{ $attachment->file_size }}</small>
                                                <small class="text-muted d-block">
                                                    <i class="ti ti-folder me-1"></i>{{ __('Project File') }}
                                                </small>
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center mt-3">
                                            <div class="d-flex gap-2">
                                                <a href="{{ $file . $attachment->file }}" 
                                                   class="btn btn-sm btn-outline-primary" download>
                                                    <i class="ti ti-download"></i>
                                                </a>
                                                @if(in_array(strtolower($extension), ['jpg', 'jpeg', 'png', 'gif']))
                                                    <a href="{{ $file . $attachment->file }}" 
                                                       class="btn btn-sm btn-outline-info" target="_blank">
                                                        <i class="ti ti-eye"></i>
                                                    </a>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        @endif
    @else
        <!-- Empty State -->
        <div class="col-12">
            <div class="text-center py-5">
                <div class="info-icon-large d-inline-flex mb-3">
                    <i class="ti ti-files"></i>
                </div>
                <h6 class="text-muted mb-2">{{ __('No Files Found') }}</h6>
                <p class="text-muted small mb-4">{{ __('Project files and task attachments will appear here when uploaded') }}</p>
                @can('create project task')
                    <a href="#" class="action-btn-modern action-btn-primary"
                       data-url="{{ route('projects.tasks.create', [$project->id, 1]) }}"
                       data-ajax-popup="true" data-size="lg">
                        <i class="ti ti-plus me-2"></i>{{ __('Create Task') }}
                    </a>
                @endcan
            </div>
        </div>
    @endif
</div>

@if($taskFiles->count() > 0 || $projectAttachments->count() > 0)
    <div class="d-flex justify-content-between align-items-center mt-4">
        <div class="d-flex align-items-center gap-4">
            <div class="d-flex align-items-center gap-2">
                <div class="info-icon-small">
                    <i class="ti ti-files"></i>
                </div>
                <span class="fw-semibold">{{ $taskFiles->count() + $projectAttachments->count() }} {{ __('Total Files') }}</span>
            </div>
            @if($taskFiles->count() > 0)
                <div class="d-flex align-items-center gap-2">
                    <div class="info-icon-small bg-primary bg-opacity-10">
                        <i class="ti ti-file-text text-primary"></i>
                    </div>
                    <span class="text-primary fw-semibold">{{ $taskFiles->count() }} {{ __('Task Files') }}</span>
                </div>
            @endif
            @if($projectAttachments->count() > 0)
                <div class="d-flex align-items-center gap-2">
                    <div class="info-icon-small bg-info bg-opacity-10">
                        <i class="ti ti-paperclip text-info"></i>
                    </div>
                    <span class="text-info fw-semibold">{{ $projectAttachments->count() }} {{ __('Project Files') }}</span>
                </div>
            @endif
        </div>
    </div>
@endif

<style>
    .file-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        background: rgba(255, 255, 255, 0.9) !important;
    }
</style>
