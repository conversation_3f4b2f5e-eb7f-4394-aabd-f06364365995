<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use App\Models\PricingPlan;
use App\Models\ModuleIntegration;
use Illuminate\Support\Facades\Gate;

// Initialize Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Automatish Specific Permission Test ===\n\n";

try {
    // Test 1: Create a pricing plan WITHOUT automatish
    echo "1. Creating test pricing plan WITHOUT automatish...\n";
    $planWithoutAutomatish = PricingPlan::create([
        'name' => 'Test Plan Without Automatish',
        'description' => 'Test plan for permission testing',
        'price' => 0.00,
        'status' => 'active',
        'duration' => 'monthly',
        'module_permissions' => [
            'crm' => ['manage lead', 'view lead'],
            'account' => ['manage customer', 'view customer']
            // Note: NO automatish module
        ]
    ]);
    echo "✅ Created plan: {$planWithoutAutomatish->name} (ID: {$planWithoutAutomatish->id})\n\n";

    // Test 2: Create a pricing plan WITH automatish
    echo "2. Creating test pricing plan WITH automatish...\n";
    $planWithAutomatish = PricingPlan::create([
        'name' => 'Test Plan With Automatish',
        'description' => 'Test plan with automatish access',
        'price' => 10.00,
        'status' => 'active',
        'duration' => 'monthly',
        'module_permissions' => [
            'crm' => ['manage lead', 'view lead'],
            'account' => ['manage customer', 'view customer'],
            'automatish' => ['access automatish']
        ]
    ]);
    echo "✅ Created plan: {$planWithAutomatish->name} (ID: {$planWithAutomatish->id})\n\n";

    // Test 3: Create test users
    echo "3. Creating test users...\n";
    $userWithoutAutomatish = User::create([
        'name' => 'Test User Without Automatish',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
        'type' => 'company',
        'plan' => $planWithoutAutomatish->id
    ]);
    echo "✅ Created user without automatish: {$userWithoutAutomatish->name}\n";

    $userWithAutomatish = User::create([
        'name' => 'Test User With Automatish',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
        'type' => 'company',
        'plan' => $planWithAutomatish->id
    ]);
    echo "✅ Created user with automatish: {$userWithAutomatish->name}\n\n";

    // Test 4: Test permissions for user WITHOUT automatish
    echo "4. Testing user WITHOUT automatish access...\n";
    $hasPermission = $userWithoutAutomatish->hasModulePermission('automatish', 'access automatish');
    echo "   - hasModulePermission('automatish', 'access automatish'): " . ($hasPermission ? 'YES' : 'NO') . "\n";
    
    $hasGateAccess = Gate::forUser($userWithoutAutomatish)->check('access automatish');
    echo "   - Gate::check('access automatish'): " . ($hasGateAccess ? 'YES' : 'NO') . "\n";
    
    if (!$hasPermission && !$hasGateAccess) {
        echo "✅ CORRECT: User without automatish plan cannot access automatish\n";
    } else {
        echo "❌ ERROR: User without automatish plan can access automatish (should not happen)\n";
    }
    echo "\n";

    // Test 5: Test permissions for user WITH automatish
    echo "5. Testing user WITH automatish access...\n";
    $hasPermission = $userWithAutomatish->hasModulePermission('automatish', 'access automatish');
    echo "   - hasModulePermission('automatish', 'access automatish'): " . ($hasPermission ? 'YES' : 'NO') . "\n";
    
    $hasGateAccess = Gate::forUser($userWithAutomatish)->check('access automatish');
    echo "   - Gate::check('access automatish'): " . ($hasGateAccess ? 'YES' : 'NO') . "\n";
    
    if ($hasPermission && $hasGateAccess) {
        echo "✅ CORRECT: User with automatish plan can access automatish\n";
    } else {
        echo "❌ ERROR: User with automatish plan cannot access automatish (should have access)\n";
    }
    echo "\n";

    // Test 6: Test sidebar visibility
    echo "6. Testing sidebar visibility...\n";
    $automatishModule = ModuleIntegration::where('name', 'Automatish')->where('enabled', true)->first();
    
    if ($automatishModule) {
        echo "   User WITHOUT automatish - Should show in sidebar: " . 
             (Gate::forUser($userWithoutAutomatish)->check('access automatish') ? 'YES' : 'NO') . "\n";
        echo "   User WITH automatish - Should show in sidebar: " . 
             (Gate::forUser($userWithAutomatish)->check('access automatish') ? 'YES' : 'NO') . "\n";
    }

    // Cleanup
    echo "\n7. Cleaning up test data...\n";
    $userWithoutAutomatish->delete();
    $userWithAutomatish->delete();
    $planWithoutAutomatish->delete();
    $planWithAutomatish->delete();
    echo "✅ Test data cleaned up\n";

} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Test Complete ===\n";
