<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('appointment_bookings', function (Blueprint $table) {
            $table->id(); // Primary key
            $table->foreignId('event_id')->constrained('calendar_events')->onDelete('cascade'); // Foreign key to calendar_events
            $table->string('event_location'); // Event location type (in_person, zoom, etc.)
            $table->text('event_location_value')->nullable(); // Physical address or meeting link
            $table->date('event_date'); // Date of the appointment
            $table->string('time_zone'); // Timezone for the appointment
            $table->string('time_slots'); // Time slot for the appointment
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('appointment_bookings');
    }
};
