<?php
   use Illuminate\Database\Migrations\Migration;
   use Illuminate\Database\Schema\Blueprint;
   use Illuminate\Support\Facades\Schema;
   return new class extends Migration
   {
    public function up(): void
    {
        Schema::table('calendar_events', function (Blueprint $table) {
            if (!Schema::hasColumn('calendar_events', 'custom_field')) {
                $table->string('custom_field')->nullable()->after('require_phone')->comment('Stores custom field configurations');
            }
            if (!Schema::hasColumn('calendar_events', 'custom_field_value')) {
                $table->text('custom_field_value')->nullable()->after('custom_field')->comment('Stores values for custom field configurations');
            }
        });
    }
       public function down(): void
       {
           Schema::table('calendar_events', function (Blueprint $table) {
               $table->dropColumn('custom_field');
               $table->dropColumn('custom_field_value');
           });
       }
   };