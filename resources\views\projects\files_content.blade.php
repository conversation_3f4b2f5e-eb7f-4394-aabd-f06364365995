<div class="files-manager">
    <!-- Files Toolbar -->
    <div class="files-toolbar">
        <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center gap-3">
                <h6 class="mb-0">{{ __('Files') }}</h6>
                <div class="breadcrumb-container">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0" id="files-breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="#" onclick="navigateToFolder(null)" class="text-decoration-none">
                                    <i class="ti ti-home"></i> {{ __('Root') }}
                                </a>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
            <div class="d-flex align-items-center gap-2">
                <!-- View Toggle -->
                <div class="btn-group" role="group" aria-label="View toggle">
                    <button type="button" class="btn btn-sm btn-outline-secondary active" id="grid-view-btn" onclick="toggleView('grid')" title="{{ __('Grid View') }}">
                        <i class="ti ti-grid-dots"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="list-view-btn" onclick="toggleView('list')" title="{{ __('List View') }}">
                        <i class="ti ti-list"></i>
                    </button>
                </div>

                <div class="vr"></div>

                <button class="btn btn-sm btn-outline-primary" onclick="createFolder()" title="{{ __('New Folder') }}">
                    <i class="ti ti-folder-plus"></i> {{ __('New Folder') }}
                </button>
                <button class="btn btn-sm btn-primary" onclick="uploadFiles()" title="{{ __('Upload Files') }}">
                    <i class="ti ti-upload"></i> {{ __('Upload') }}
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="refreshFiles()" title="{{ __('Refresh') }}">
                    <i class="ti ti-refresh"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Files Grid -->
    <div class="files-grid" id="files-grid">
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">{{ __('Loading...') }}</span>
            </div>
            <p class="mt-2 text-muted">{{ __('Loading files...') }}</p>
        </div>
    </div>
</div>

<!-- Hidden File Input -->
<input type="file" id="file-upload-input" multiple style="display: none;">

<!-- Create Folder Modal -->
<div class="modal fade" id="createFolderModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Create New Folder') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="create-folder-form">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="folder-name" class="form-label">{{ __('Folder Name') }}</label>
                        <input type="text" class="form-control" id="folder-name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="folder-icon" class="form-label">{{ __('Folder Icon') }}</label>
                        <div class="folder-icon-grid">
                            <div class="folder-icon-option active" data-icon="ti-folder-filled">
                                <i class="ti ti-folder-filled"></i>
                                <span>{{ __('Default') }}</span>
                            </div>
                            <div class="folder-icon-option" data-icon="ti-folder-code">
                                <i class="ti ti-folder-code"></i>
                                <span>{{ __('Code') }}</span>
                            </div>
                            <div class="folder-icon-option" data-icon="ti-photo">
                                <i class="ti ti-photo"></i>
                                <span>{{ __('Images') }}</span>
                            </div>
                            <div class="folder-icon-option" data-icon="ti-video">
                                <i class="ti ti-video"></i>
                                <span>{{ __('Videos') }}</span>
                            </div>
                            <div class="folder-icon-option" data-icon="ti-music">
                                <i class="ti ti-music"></i>
                                <span>{{ __('Audio') }}</span>
                            </div>
                            <div class="folder-icon-option" data-icon="ti-file-text">
                                <i class="ti ti-file-text"></i>
                                <span>{{ __('Documents') }}</span>
                            </div>
                            <div class="folder-icon-option" data-icon="ti-archive">
                                <i class="ti ti-archive"></i>
                                <span>{{ __('Archive') }}</span>
                            </div>
                            <div class="folder-icon-option" data-icon="ti-settings">
                                <i class="ti ti-settings"></i>
                                <span>{{ __('Settings') }}</span>
                            </div>
                            <div class="folder-icon-option" data-icon="ti-users">
                                <i class="ti ti-users"></i>
                                <span>{{ __('Team') }}</span>
                            </div>
                            <div class="folder-icon-option" data-icon="ti-star">
                                <i class="ti ti-star"></i>
                                <span>{{ __('Important') }}</span>
                            </div>
                            <div class="folder-icon-option" data-icon="ti-lock">
                                <i class="ti ti-lock"></i>
                                <span>{{ __('Private') }}</span>
                            </div>
                            <div class="folder-icon-option" data-icon="ti-share">
                                <i class="ti ti-share"></i>
                                <span>{{ __('Shared') }}</span>
                            </div>
                            <div class="folder-icon-option" data-icon="ti-calendar">
                                <i class="ti ti-calendar"></i>
                                <span>{{ __('Events') }}</span>
                            </div>
                            <div class="folder-icon-option" data-icon="ti-briefcase">
                                <i class="ti ti-briefcase"></i>
                                <span>{{ __('Work') }}</span>
                            </div>
                            <div class="folder-icon-option" data-icon="ti-heart">
                                <i class="ti ti-heart"></i>
                                <span>{{ __('Favorites') }}</span>
                            </div>
                            <div class="folder-icon-option" data-icon="ti-download">
                                <i class="ti ti-download"></i>
                                <span>{{ __('Downloads') }}</span>
                            </div>
                        </div>
                        <input type="hidden" id="folder-icon-input" name="icon" value="ti-folder-filled">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ __('Preview') }}</label>
                        <div class="folder-preview">
                            <div class="folder-preview-icon">
                                <i class="ti ti-folder-filled" id="folder-preview-icon" style="color: #3b82f6;"></i>
                            </div>
                            <div class="folder-preview-name">{{ __('Sample Folder') }}</div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="folder-color" class="form-label">{{ __('Folder Color') }}</label>
                        <div class="d-flex gap-2">
                            <input type="color" class="form-control form-control-color" id="folder-color" name="color" value="#3b82f6">
                            <div class="color-presets d-flex gap-1">
                                <button type="button" class="color-preset" data-color="#3b82f6" style="background: #3b82f6;"></button>
                                <button type="button" class="color-preset" data-color="#10b981" style="background: #10b981;"></button>
                                <button type="button" class="color-preset" data-color="#f59e0b" style="background: #f59e0b;"></button>
                                <button type="button" class="color-preset" data-color="#ef4444" style="background: #ef4444;"></button>
                                <button type="button" class="color-preset" data-color="#8b5cf6" style="background: #8b5cf6;"></button>
                                <button type="button" class="color-preset" data-color="#06b6d4" style="background: #06b6d4;"></button>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="folder-description" class="form-label">{{ __('Description') }} ({{ __('Optional') }})</label>
                        <textarea class="form-control" id="folder-description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('Create Folder') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- File/Folder Context Menu -->
<div class="context-menu" id="context-menu">
    <div class="context-menu-item" onclick="downloadItem()">
        <i class="ti ti-download"></i> {{ __('Download') }}
    </div>
    <div class="context-menu-item" onclick="renameItem()">
        <i class="ti ti-edit"></i> {{ __('Rename') }}
    </div>
    <div class="context-menu-divider"></div>
    <div class="context-menu-item text-danger" onclick="deleteItem()">
        <i class="ti ti-trash"></i> {{ __('Delete') }}
    </div>
</div>

<!-- Rename Modal -->
<div class="modal fade" id="renameModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rename-modal-title">{{ __('Rename') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="rename-form">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="rename-input" class="form-label">{{ __('New Name') }}</label>
                        <input type="text" class="form-control" id="rename-input" name="name" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('Rename') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>
