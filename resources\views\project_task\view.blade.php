<div class="modal-body task-detail-modern" id="{{$task->id}}">
    <!-- Task Header -->
    <div class="task-header-modern mb-4">
        <div class="task-header-content">
            <!-- Task Meta Information -->
            <div class="task-meta-section">
                <div class="task-badges-row">
                    @if($task->priority)
                        <div class="priority-badge-modern priority-{{ $task->priority }}">
                            <i class="ti ti-flag-filled"></i>
                            <span>{{ __(\App\Models\ProjectTask::$priority[$task->priority]) }}</span>
                        </div>
                    @endif
                    @if($task->stage)
                        <div class="status-badge-modern">
                            <i class="ti ti-circle-dot"></i>
                            <span>{{ $task->stage->name }}</span>
                        </div>
                    @endif
                    <div class="task-id-badge">
                        <i class="ti ti-hash"></i>
                        <span>{{ $task->id }}</span>
                    </div>
                </div>
            </div>

            <!-- Task Title and Description -->
            <div class="task-main-content">
                <div class="task-title-row">
                    <h3 class="task-title-modern">{{ $task->name }}</h3>
                    <div class="task-actions-modern">
                        <div class="action-buttons-group">
                            <!-- Edit Action -->
                            @can('edit project task')
                                <button type="button" class="btn-action-modern btn-edit" data-url="{{ route('projects.tasks.edit', [$task->project_id, $task->id]) }}" data-ajax-popup="true" data-size="lg" data-bs-toggle="tooltip" title="{{ __('Edit Task') }}">
                                    <i class="ti ti-edit"></i>
                                </button>
                            @endcan

                            <!-- Delete Action -->
                            @can('delete project task')
                                <button type="button" class="btn-action-modern btn-delete" data-url="{{ route('projects.tasks.destroy', [$task->project_id, $task->id]) }}" data-confirm="{{ __('Are you sure?') }}" data-bs-toggle="tooltip" title="{{ __('Delete Task') }}">
                                    <i class="ti ti-trash"></i>
                                </button>
                            @endcan
                        </div>
                    </div>
                </div>

                @if($task->description)
                    <div class="task-description-modern">
                        <p>{{ $task->description }}</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Task Details Grid -->
    <div class="task-details-grid mb-4">
        <div class="row g-3">
            <div class="col-md-6">
                <div class="detail-card">
                    <div class="detail-icon">
                        <i class="ti ti-clock"></i>
                    </div>
                    <div class="detail-content">
                        <span class="detail-label">{{ __('Estimated Hours') }}</span>
                        <span class="detail-value">{{ (!empty($task->estimated_hrs)) ? number_format($task->estimated_hrs) . 'h' : '-' }}</span>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="detail-card">
                    <div class="detail-icon">
                        <i class="ti ti-target"></i>
                    </div>
                    <div class="detail-content">
                        <span class="detail-label">{{ __('Milestone') }}</span>
                        <span class="detail-value">{{ (!empty($task->milestone)) ? $task->milestone->title : '-' }}</span>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="detail-card">
                    <div class="detail-icon">
                        <i class="ti ti-calendar"></i>
                    </div>
                    <div class="detail-content">
                        <span class="detail-label">{{ __('Due Date') }}</span>
                        <span class="detail-value">{{ $task->end_date ? \App\Models\Utility::getDateFormated($task->end_date) : '-' }}</span>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="detail-card">
                    <div class="detail-icon">
                        <i class="ti ti-users"></i>
                    </div>
                    <div class="detail-content">
                        <span class="detail-label">{{ __('Assignees') }}</span>
                        <div class="assignees-list">
                            @if($task->users()->count() > 0)
                                @foreach($task->users()->take(3) as $user)
                                    <div class="user-avatar-text" data-bs-toggle="tooltip" title="{{ $user->name }}">
                                        {{ substr($user->name, 0, 1) }}
                                    </div>
                                @endforeach
                                @if($task->users()->count() > 3)
                                    <span class="more-users">+{{ $task->users()->count() - 3 }}</span>
                                @endif
                            @else
                                <span class="text-muted">{{ __('Unassigned') }}</span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if($allow_progress == 'false')
            <div class="progress-section mt-3">
                <div class="detail-card">
                    <div class="detail-icon">
                        <i class="ti ti-progress"></i>
                    </div>
                    <div class="detail-content">
                        <span class="detail-label">{{ __('Progress') }}</span>
                        <div class="progress-wrapper">
                            <div class="progress-bar-container">
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar" role="progressbar" style="width: {{ $task->progress }}%; background-color: var(--theme-color);" aria-valuenow="{{ $task->progress }}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <span class="progress-text">{{ $task->progress }}%</span>
                            </div>
                            <input type="range" class="task_progress custom-range mt-2" value="{{ $task->progress }}" id="task_progress" name="progress" data-url="{{ route('change.progress',[$task->project_id,$task->id]) }}">
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- Time Tracker Section -->
    <div class="task-section">
        <div class="section-header">
            <div class="d-flex align-items-center gap-2">
                <i class="ti ti-clock section-icon"></i>
                <h6 class="section-title mb-0">{{ __('Time Tracker') }}</h6>
                <span class="time-display" id="timeDisplay">00:00:00</span>
            </div>
        </div>

        <div class="section-content">
            <div class="time-tracker-container">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="timer-info">
                        <div class="current-session">
                            <span class="timer-label">{{__('Current Session')}}:</span>
                            <span class="timer-value" id="currentSessionTime">00:00:00</span>
                        </div>
                        <div class="total-time mt-1">
                            <span class="timer-label">{{__('Total Time')}}:</span>
                            <span class="timer-value" id="totalTime">00:00:00</span>
                        </div>
                    </div>
                    <div class="timer-controls">
                        <button class="btn btn-success btn-sm" id="startTimerBtn" data-task-id="{{$task->id}}" data-project-id="{{$task->project_id}}">
                            <i class="ti ti-play"></i> {{__('Start Timer')}}
                        </button>
                        <button class="btn btn-danger btn-sm" id="stopTimerBtn" data-task-id="{{$task->id}}" data-project-id="{{$task->project_id}}" style="display: none;">
                            <i class="ti ti-stop"></i> {{__('Stop Timer')}}
                        </button>
                    </div>
                </div>

                <div class="timer-status mt-2" id="timerStatus">
                    <small class="text-muted">{{__('Click Start Timer to begin tracking time for this task')}}</small>
                </div>

                <div class="timer-debug mt-2" style="display: none;">
                    <button class="btn btn-sm btn-warning" id="cleanupTimerBtn" data-task-id="{{$task->id}}" data-project-id="{{$task->project_id}}">
                        <i class="ti ti-refresh"></i> {{__('Fix Timer Data')}}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Checklist Section -->
    <div class="task-section">
        <div class="section-header">
            <div class="d-flex align-items-center gap-2">
                <i class="ti ti-checklist section-icon"></i>
                <h6 class="section-title mb-0">{{ __('Checklist') }}</h6>
                <span class="checklist-progress">{{ $task->checklist->where('status', 1)->count() }}/{{ $task->checklist->count() }}</span>
            </div>
            <button class="btn btn-sm btn-ghost" data-bs-toggle="collapse" href="#form-checklist" role="button" aria-expanded="false" aria-controls="form-checklist" data-bs-toggle="tooltip" title="{{__('Add item')}}">
                <i class="ti ti-plus"></i>
            </button>
        </div>

        <div class="section-content">
            <form id="form-checklist" class="collapse add-item-form" data-action="{{route('checklist.store',[$task->project_id,$task->id])}}">
                <div class="add-item-container">
                    @csrf
                    <div class="d-flex gap-2">
                        <input type="text" name="name" required class="form-control form-control-sm" placeholder="{{__('Add checklist item...')}}"/>
                        <button class="btn btn-sm btn-primary" type="button" id="checklist_submit" data-bs-toggle="tooltip" title="{{__('Create')}}">
                            <i class="ti ti-check"></i>
                        </button>
                    </div>
                </div>
            </form>

            <div class="checklist-items" id="checklist">
                @foreach($task->checklist as $checklist)
                    <div class="checklist-item" data-id="{{ $checklist->id }}">
                        <div class="d-flex align-items-center gap-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="check-item-{{ $checklist->id }}" @if($checklist->status) checked @endif data-url="{{route('checklist.update',[$task->project_id,$checklist->id])}}">
                            </div>
                            <label class="checklist-label {{ $checklist->status ? 'completed' : '' }}" for="check-item-{{ $checklist->id }}">
                                {{ $checklist->name }}
                            </label>
                            <div class="checklist-actions ms-auto">
                                <button class="btn btn-sm btn-ghost delete-checklist" data-url="{{ route('checklist.destroy',[$task->project_id,$checklist->id]) }}" data-bs-toggle="tooltip" title="{{__('Delete')}}">
                                    <i class="ti ti-trash text-danger"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                @endforeach

                @if($task->checklist->count() == 0)
                    <div class="empty-state">
                        <i class="ti ti-checklist"></i>
                        <p class="text-muted mb-0">{{ __('No checklist items yet') }}</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
    <!-- Attachments Section -->
    {{-- <div class="task-section">
        <div class="section-header">
            <div class="d-flex align-items-center gap-2">
                <i class="ti ti-paperclip section-icon"></i>
                <h6 class="section-title mb-0">{{ __('Attachments') }}</h6>
                <span class="attachment-count">{{ $task->taskFiles->count() }}</span>
            </div>
            <button class="btn btn-sm btn-ghost" data-bs-toggle="collapse" href="#add_file" role="button" aria-expanded="false" aria-controls="add_file" data-bs-toggle="tooltip" title="{{__('Add attachment')}}">
                <i class="ti ti-plus"></i>
            </button>
        </div>

        <div class="section-content">
            <form id="add_file" class="collapse add-item-form">
                <div class="add-item-container">
                    @csrf
                    <div class="d-flex gap-2">
                        <input type="file" name="task_attachment" id="task_attachment" class="form-control form-control-sm" onchange="document.getElementById('blah').src = window.URL.createObjectURL(this.files[0])" required/>
                        <button class="btn btn-sm btn-primary" type="button" id="file_attachment_submit" data-action="{{ route('comment.store.file',[$task->project_id,$task->id]) }}" data-bs-toggle="tooltip" title="{{__('Upload')}}">
                            <i class="ti ti-upload"></i>
                        </button>
                    </div>
                    <img id="blah" src="" class="img_preview mt-2" style="max-width: 100px; max-height: 100px; display: none;" />
                </div>
            </form>

            <div class="attachments-list" id="comments-file">
                @foreach($task->taskFiles as $file)
                    <div class="attachment-item" data-id="{{ $file->id }}">
                        <div class="d-flex align-items-center gap-3">
                            <div class="file-icon">
                                @php
                                    $extension = pathinfo($file->name, PATHINFO_EXTENSION);
                                    $iconClass = match(strtolower($extension)) {
                                        'pdf' => 'ti-file-type-pdf',
                                        'doc', 'docx' => 'ti-file-type-doc',
                                        'xls', 'xlsx' => 'ti-file-type-xls',
                                        'jpg', 'jpeg', 'png', 'gif' => 'ti-photo',
                                        'zip', 'rar' => 'ti-file-zip',
                                        default => 'ti-file'
                                    };
                                @endphp
                                <i class="ti {{ $iconClass }}"></i>
                            </div>
                            <div class="file-info flex-grow-1">
                                <div class="file-name">{{ $file->name }}</div>
                                <div class="file-size text-muted">{{ $file->file_size }}</div>
                            </div>
                            <div class="file-actions">
                                <a href="{{asset(Storage::url('uploads/tasks/'.$file->file))}}" download class="btn btn-sm btn-ghost" data-bs-toggle="tooltip" title="{{__('Download')}}">
                                    <i class="ti ti-download"></i>
                                </a>
                                @auth('web')
                                    <button class="btn btn-sm btn-ghost delete-comment-file" data-url="{{ route('comment.destroy.file',[$task->project_id,$task->id,$file->id]) }}" data-bs-toggle="tooltip" title="{{__('Delete')}}">
                                        <i class="ti ti-trash text-danger"></i>
                                    </button>
                                @endauth
                            </div>
                        </div>
                    </div>
                @endforeach

                @if($task->taskFiles->count() == 0)
                    <div class="empty-state">
                        <i class="ti ti-paperclip"></i>
                        <p class="text-muted mb-0">{{ __('No attachments yet') }}</p>
                    </div>
                @endif
            </div>
        </div>
    </div> --}}
    <!-- Activity Section -->
    <div class="task-section">
        <div class="section-header">
            <div class="d-flex align-items-center gap-2">
                <i class="ti ti-activity section-icon"></i>
                <h6 class="section-title mb-0">{{ __('Activity') }}</h6>
                <span class="activity-count">{{ $task->activity_log()->count() }}</span>
            </div>
        </div>

        <div class="section-content">
            <div class="activity-timeline" id="activity">
                @foreach($task->activity_log() as $activity)
                    @php $user = \App\Models\User::find($activity->user_id); @endphp
                    <div class="activity-item">
                        <div class="activity-avatar">
                            <div class="user-avatar-text">
                                {{ $user ? substr($user->name, 0, 1) : '?' }}
                            </div>
                        </div>
                        <div class="activity-content">
                            <div class="activity-header">
                                <span class="activity-type">{{ __($activity->log_type) }}</span>
                                <span class="activity-time">{{ $activity->created_at->diffForHumans() }}</span>
                            </div>
                            <div class="activity-description">{!! $activity->getRemark() !!}</div>
                        </div>
                    </div>
                @endforeach

                @if($task->activity_log()->count() == 0)
                    <div class="empty-state">
                        <i class="ti ti-activity"></i>
                        <p class="text-muted mb-0">{{ __('No activity yet') }}</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
    {{-- Modern Comments Section --}}
    <div id="comments" class="row mt-4">
        <div class="col-12">
            <div class="card modern-comments-section">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5>{{__('Comments')}} <span class="badge bg-secondary ms-2">{{ $task->comments->where('parent_id', null)->count() }}</span></h5>
                </div>
                <div class="card-body">
                    {{-- Modern Comment Form --}}
                    <div class="modern-comment-form">
                        <div class="d-flex gap-3">
                            <div class="comment-avatar">
                                {{substr(\Auth::user()->name, 0, 1)}}
                            </div>
                            <div class="flex-grow-1">
                                <form method="post" action="{{route('task.comment.store',[$task->project_id,$task->id])}}" id="modernCommentForm">
                                    @csrf
                                    <textarea class="modern-comment-input" name="comment" id="modernCommentTextarea" placeholder="{{__('Write a comment...')}}" required></textarea>
                                    <div class="comment-form-actions">
                                        <div></div>
                                        <div class="comment-submit-actions">
                                            <button type="button" class="btn-modern btn-modern-secondary" id="cancelModernComment">
                                                {{__('Cancel')}}
                                            </button>
                                            <button type="submit" class="btn-modern btn-modern-primary" id="submitModernComment">
                                                {{__('Comment')}}
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    {{-- Modern Comments List --}}
                    <div id="modernCommentsList">
                        @if(!empty($task->comments) && $task->comments->where('parent_id', null)->count() > 0)
                            @foreach($task->comments->where('parent_id', null)->sortByDesc('created_at') as $comment)
                                @php $user = \App\Models\User::find($comment->user_id); @endphp
                                <div class="modern-comment-item d-flex" data-comment-id="{{$comment->id}}">
                                    <div class="comment-avatar">
                                        {{substr($user->name ?? 'U', 0, 1)}}
                                    </div>
                                    <div class="comment-main-content">
                                        <div class="comment-header-modern">
                                            <div class="comment-author-info">
                                                <span class="comment-author-name">{{$user->name ?? __('Unknown User')}}</span>
                                                <span class="comment-timestamp">{{$comment->created_at->diffForHumans()}}</span>
                                            </div>
                                            <div class="dropdown">
                                                <button class="comment-menu-btn" type="button" data-bs-toggle="dropdown">
                                                    <i class="ti ti-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item text-danger delete-comment" href="#" data-url="{{ route('comment.destroy',[$task->project_id,$task->id,$comment->id]) }}" onclick="deleteComment(this, event)">
                                                            <i class="ti ti-trash"></i> {{__('Delete')}}
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>

                                        <div class="comment-text-modern">
                                            {{$comment->comment}}
                                        </div>

                                        {{-- Reaction Buttons --}}
                                        <div class="comment-reactions mt-2">
                                            <div class="btn-group mb-2" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary reaction-btn"
                                                        data-comment-id="{{$comment->id}}"
                                                        data-reaction="like">
                                                    👍 Like
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger reaction-btn"
                                                        data-comment-id="{{$comment->id}}"
                                                        data-reaction="love">
                                                    ❤️ Love
                                                </button>
                                                {{-- Reply Button --}}
                                                <button type="button" class="btn btn-sm btn-outline-secondary reply-btn"
                                                        data-comment-id="{{$comment->id}}"
                                                        data-user-name="{{$user->name ?? 'User'}}">
                                                    <i class="ti ti-corner-down-right"></i> Reply
                                                </button>
                                            </div>

                                            {{-- Reaction Counts --}}
                                            <div class="reaction-counts" id="reaction-counts-{{$comment->id}}">
                                                @php
                                                    $reactions = $comment->comment_reaction ?? [];
                                                    $counts = [];
                                                    foreach ($reactions as $reaction_data) {
                                                        $reaction = $reaction_data['reaction'];
                                                        if (!isset($counts[$reaction])) {
                                                            $counts[$reaction] = 0;
                                                        }
                                                        $counts[$reaction]++;
                                                    }
                                                @endphp

                                                @if(!empty($counts))
                                                    @foreach($counts as $reaction => $count)
                                                        <span class="badge bg-light text-dark me-1">
                                                            @if($reaction == 'like') 👍 @elseif($reaction == 'love') ❤️ @endif
                                                            {{$count}}
                                                        </span>
                                                    @endforeach
                                                @endif
                                            </div>
                                        </div>

                                        {{-- Reply Form (Hidden by default) --}}
                                        <div class="reply-form mt-3" id="reply-form-{{$comment->id}}" style="display: none;">
                                            <div class="card bg-light">
                                                <div class="card-body p-3">
                                                    <form>
                                                        <div class="d-flex gap-2">
                                                            <div class="flex-shrink-0">
                                                                <div class="theme-avtar bg-secondary" style="width: 30px; height: 30px; font-size: 12px;">
                                                                    <span>{{substr(\Auth::user()->name ?? 'U', 0, 1)}}</span>
                                                                </div>
                                                            </div>
                                                            <div class="flex-grow-1">
                                                                <textarea class="form-control reply-textarea" rows="2" placeholder="{{__('Write a reply...')}}" data-comment-id="{{$comment->id}}"></textarea>
                                                                <div class="d-flex justify-content-end gap-2 mt-2">
                                                                    <button type="button" class="btn btn-sm btn-secondary cancel-reply" data-comment-id="{{$comment->id}}">{{__('Cancel')}}</button>
                                                                    <button type="button" class="btn btn-sm btn-primary submit-reply" data-comment-id="{{$comment->id}}">{{__('Reply')}}</button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>

                                        {{-- SHOW EXISTING REPLIES FROM DATABASE --}}
                                        <div class="replies mt-3 ms-4" id="replies-{{$comment->id}}">
                                            @php
                                                $replies = $task->comments->where('parent_id', $comment->id)->sortBy('created_at');
                                            @endphp

                                            @if($replies && count($replies) > 0)
                                                @foreach($replies as $reply)
                                                    @php $replyUser = \App\Models\User::find($reply->user_id); @endphp
                                                    <div class="reply-item border-start border-2 border-primary ps-3 mb-2">
                                                        <div class="d-flex">
                                                            <div class="flex-shrink-0">
                                                                <div class="theme-avtar bg-secondary" style="width: 30px; height: 30px; font-size: 12px;">
                                                                    <span>{{substr($replyUser->name ?? 'U', 0, 1)}}</span>
                                                                </div>
                                                            </div>
                                                            <div class="flex-grow-1 ms-2">
                                                                <div class="d-flex justify-content-between align-items-start">
                                                                    <div>
                                                                        <h6 class="mb-1 small">{{$replyUser->name ?? __('Unknown User')}}</h6>
                                                                        <small class="text-muted">{{$reply->created_at->diffForHumans()}}</small>
                                                                    </div>
                                                                    <div class="dropdown">
                                                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" style="font-size: 10px;">
                                                                            <i class="ti ti-dots-vertical"></i>
                                                                        </button>
                                                                        <ul class="dropdown-menu">
                                                                            <li>
                                                                                <a class="dropdown-item text-danger delete-comment" href="#" data-url="{{ route('comment.destroy',[$task->project_id,$task->id,$reply->id]) }}" onclick="deleteComment(this, event)">
                                                                                    <i class="ti ti-trash"></i> {{__('Delete')}}
                                                                                </a>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </div>
                                                                <p class="mb-0 mt-1 small">{!! preg_replace('/@(\w+)/', '<span class="mentioned-user">@$1</span>', $reply->comment) !!}</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="no-comments-state" id="noCommentsMessage">
                                <div class="no-comments-icon">💬</div>
                                <div class="no-comments-text">{{__('No comments yet')}}</div>
                                <div class="no-comments-subtext">{{__('Be the first to share your thoughts')}}</div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{-- Task Comment System JavaScript --}}
<script>
    // Update checklist progress counter function (define it globally)
    function updateChecklistProgress() {
        const total = $('.checklist-item').length;
        const completed = $('.checklist-item input[type="checkbox"]:checked').length;
        $('.checklist-progress').text(completed + '/' + total);
        console.log('Progress updated:', completed + '/' + total);
    }

    // Handle checkbox change function (define it globally)
    function handleCheckboxChange(checkboxElement) {
        const $checkbox = $(checkboxElement);
        const label = $checkbox.closest('.checklist-item').find('.checklist-label');
        const url = $checkbox.data('url');

        console.log('Checkbox changed:', $checkbox.is(':checked'));
        console.log('Checkbox URL:', url);

        if ($checkbox.is(':checked')) {
            label.addClass('completed');
        } else {
            label.removeClass('completed');
        }

        // Update progress counter
        updateChecklistProgress();

        // Save state to server
        if (url) {
            const token = $('meta[name="csrf-token"]').attr('content');
            console.log('Sending checkbox update to server...');
            console.log('URL:', url);
            console.log('Method: POST');
            console.log('Token:', token);
            console.log('Checkbox checked:', $checkbox.is(':checked'));

            $.ajax({
                url: url,
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': token,
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                data: {
                    _token: token
                },
                success: function(response) {
                    console.log('Checklist item updated successfully:', response);
                    if (response.success) {
                        console.log('Server confirmed update successful');
                    } else {
                        console.error('Server returned success=false:', response);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error updating checklist item:', error);
                    console.error('Status:', status);
                    console.error('Response Text:', xhr.responseText);
                    console.error('Status Code:', xhr.status);

                    // Revert checkbox state on error
                    $checkbox.prop('checked', !$checkbox.is(':checked'));
                    if ($checkbox.is(':checked')) {
                        label.addClass('completed');
                    } else {
                        label.removeClass('completed');
                    }
                    updateChecklistProgress();
                }
            });
        } else {
            console.error('No URL found for checkbox update');
        }
    }

    // Handle checklist delete function (define it globally)
    function handleChecklistDelete(deleteButton) {
        const $button = $(deleteButton);
        const $item = $button.closest('.checklist-item');
        const url = $button.data('url');

        console.log('Delete checklist button clicked');
        console.log('Delete URL:', url);
        console.log('Item found:', $item.length > 0);

        if (!url) {
            console.error('No delete URL found for checklist item');
            return;
        }

        const token = $('meta[name="csrf-token"]').attr('content');
        console.log('Sending delete request...');

        $.ajax({
            url: url,
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': token,
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            data: {
                _token: token
            },
            success: function(response) {
                console.log('Delete response:', response);
                if (response.success) {
                    console.log('Delete successful, removing item from DOM');
                    $item.fadeOut(300, function() {
                        $(this).remove();
                        updateChecklistProgress();

                        // Show empty state if no items left
                        if ($('.checklist-item').length === 0) {
                            $('#checklist').html(`
                                <div class="empty-state">
                                    <i class="ti ti-checklist"></i>
                                    <p class="text-muted mb-0">{{ __('No checklist items yet') }}</p>
                                </div>
                            `);
                        }
                    });
                } else {
                    console.error('Server returned success=false:', response);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error deleting checklist item:', error);
                console.error('Status:', status);
                console.error('Response Text:', xhr.responseText);
                console.error('Status Code:', xhr.status);
            }
        });
    }

    // Time Tracker Functions (define globally) - Check if already declared
    if (typeof window.timerInterval === 'undefined') {
        window.timerInterval = null;
        window.timerStartTime = null;
        window.timerElapsedSeconds = 0;
    }

    function formatTime(seconds) {
        // Ensure seconds is never negative
        seconds = Math.max(0, parseInt(seconds) || 0);

        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    function validateTimerResponse(timer) {
        if (!timer) return null;

        // Fix any negative values
        const validatedTimer = {
            ...timer,
            elapsed_seconds: Math.max(0, parseInt(timer.elapsed_seconds) || 0),
            total_task_time: Math.max(0, parseInt(timer.total_task_time) || 0)
        };

        // Fix formatted times if they contain negative values
        if (validatedTimer.formatted_time && validatedTimer.formatted_time.includes('-')) {
            validatedTimer.formatted_time = formatTime(validatedTimer.total_task_time);
        }

        if (validatedTimer.total_task_time_formatted && validatedTimer.total_task_time_formatted.includes('-')) {
            validatedTimer.total_task_time_formatted = formatTime(validatedTimer.total_task_time);
        }

        console.log('Timer validation:', {
            original: timer,
            validated: validatedTimer
        });

        return validatedTimer;
    }

    function updateTimerDisplay() {
        if (window.timerStartTime) {
            const now = new Date();
            const currentSessionSeconds = Math.floor((now - window.timerStartTime) / 1000);
            const totalSeconds = window.timerElapsedSeconds + currentSessionSeconds;

            $('#currentSessionTime').text(formatTime(currentSessionSeconds));
            $('#timeDisplay').text(formatTime(totalSeconds));
        }
    }

    function startTimerDisplay(startTime, elapsedSeconds) {
        window.timerStartTime = new Date(startTime);
        window.timerElapsedSeconds = elapsedSeconds;

        // Update immediately
        updateTimerDisplay();

        // Update every second
        window.timerInterval = setInterval(updateTimerDisplay, 1000);

        // Update UI state
        $('#startTimerBtn').hide();
        $('#stopTimerBtn').show();
        $('.time-tracker-container').addClass('timer-running');
        $('#timerStatus').html('<small class="text-info"><i class="ti ti-clock"></i> Timer is running...</small>');
    }

    function stopTimerDisplay() {
        if (window.timerInterval) {
            clearInterval(window.timerInterval);
            window.timerInterval = null;
        }

        window.timerStartTime = null;
        window.timerElapsedSeconds = 0; // Reset for next session

        // Update UI state
        $('#startTimerBtn').show();
        $('#stopTimerBtn').hide();
        $('.time-tracker-container').removeClass('timer-running');

        // Reset current session display
        $('#currentSessionTime').text('00:00:00');

        console.log('Timer display stopped and reset');
    }

    function loadTimerStatus() {
        const taskId = $('#startTimerBtn').data('task-id');
        const projectId = $('#startTimerBtn').data('project-id');

        console.log('Loading timer status for task:', taskId);

        $.ajax({
            url: `/projects/${projectId}/task/${taskId}/timer/status`,
            method: 'GET',
            success: function(response) {
                console.log('Timer status response:', response);
                console.log('Debug details:', {
                    totalTimeSeconds: response.total_time_seconds,
                    totalTimeFormatted: response.total_time_formatted,
                    isRunning: response.is_running,
                    timer: response.timer,
                    debugEntriesCount: response.debug_entries_count
                });

                if (response.success) {
                    // Check if there are negative time issues
                    if (response.total_time_seconds < 0 || response.total_time_formatted.includes('-')) {
                        console.warn('Negative time detected, showing cleanup button');
                        $('.timer-debug').show();
                    } else {
                        $('.timer-debug').hide();
                    }

                    // Update total time
                    $('#totalTime').text(response.total_time_formatted);

                    if (response.is_running && response.timer) {
                        // Timer is running
                        console.log('Starting timer display with:', {
                            startTime: response.timer.start_time,
                            elapsedSeconds: response.timer.elapsed_seconds
                        });
                        startTimerDisplay(response.timer.start_time, response.timer.elapsed_seconds);
                    } else {
                        // Timer is not running
                        console.log('No running timer, showing total time:', response.total_time_formatted);
                        stopTimerDisplay();
                        $('#currentSessionTime').text('00:00:00');
                        $('#timeDisplay').text(response.total_time_formatted);
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading timer status:', error);
            }
        });
    }

    $(document).ready(function() {
        // Cancel button
        $('#cancelModernComment').click(function() {
            $('#modernCommentTextarea').val('');
        });

        // Auto-resize textarea
        $('#modernCommentTextarea').on('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.max(60, this.scrollHeight) + 'px';
        });

        // Test if delete buttons are being detected
        console.log('Delete comment buttons found:', $('.delete-comment').length);

        // Test checklist elements
        console.log('Checklist submit button found:', $('#checklist_submit').length);
        console.log('Checklist form found:', $('#form-checklist').length);
        console.log('Checklist container found:', $('#checklist').length);
        console.log('Checklist checkboxes found:', $('.checklist-item input[type="checkbox"]').length);

        // Add direct event handlers to existing checkboxes
        $('.checklist-item input[type="checkbox"]').each(function(index) {
            console.log('Checkbox', index, 'URL:', $(this).data('url'));
        });

        // Direct event handler for checkboxes
        $('.checklist-item input[type="checkbox"]').on('change', function() {
            console.log('Direct checkbox handler triggered');
            handleCheckboxChange(this);
        });

        // Test delete buttons
        console.log('Delete checklist buttons found:', $('.delete-checklist').length);

        // Add direct event handlers to existing delete buttons
        $('.delete-checklist').each(function(index) {
            console.log('Delete button', index, 'URL:', $(this).data('url'));
        });

        // Direct event handler for delete buttons
        $('.delete-checklist').on('click', function(e) {
            e.preventDefault();
            console.log('Direct delete checklist handler triggered');
            handleChecklistDelete(this);
        });

        // Timer functionality
        console.log('Timer buttons found - Start:', $('#startTimerBtn').length, 'Stop:', $('#stopTimerBtn').length);

        // Load initial timer status
        loadTimerStatus();

        // Start timer button
        $('#startTimerBtn').on('click', function() {
            const taskId = $(this).data('task-id');
            const projectId = $(this).data('project-id');

            console.log('Start timer clicked for task:', taskId);

            $.ajax({
                url: `/projects/${projectId}/task/${taskId}/timer/start`,
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    console.log('Start timer response:', response);
                    if (response.success) {
                        // Validate and fix timer data
                        const validatedTimer = validateTimerResponse(response.timer);

                        if (validatedTimer) {
                            // Update total time display before starting timer
                            if (validatedTimer.total_task_time_formatted) {
                                $('#totalTime').text(validatedTimer.total_task_time_formatted);
                            }

                            startTimerDisplay(validatedTimer.start_time, validatedTimer.elapsed_seconds);

                            // Update status
                            $('#timerStatus').html('<small class="text-info"><i class="ti ti-clock"></i> Timer started!</small>');
                        } else {
                            console.error('Invalid timer data received');
                        }
                    } else {
                        console.error('Start timer failed:', response);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error starting timer:', error);
                    console.error('Response:', xhr.responseText);
                }
            });
        });

        // Stop timer button
        $('#stopTimerBtn').on('click', function() {
            const taskId = $(this).data('task-id');
            const projectId = $(this).data('project-id');

            console.log('Stop timer clicked for task:', taskId);

            // Calculate how long the timer has been running
            if (window.timerStartTime) {
                const now = new Date();
                const actualElapsed = Math.floor((now - window.timerStartTime) / 1000);
                console.log('Timer has been running for:', actualElapsed, 'seconds');
                console.log('Timer start time was:', window.timerStartTime.toISOString());
                console.log('Current time is:', now.toISOString());
            }

            $.ajax({
                url: `/projects/${projectId}/task/${taskId}/timer/stop`,
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    console.log('Stop timer response:', response);
                    if (response.success) {
                        // Validate and fix timer data
                        const validatedTimer = validateTimerResponse(response.timer);

                        if (validatedTimer) {
                            stopTimerDisplay();

                            // Update total time displays - use the corrected total task time
                            const totalTimeFormatted = validatedTimer.total_task_time_formatted || validatedTimer.formatted_time || '00:00:00';
                            console.log('Updating total time to:', totalTimeFormatted);

                            $('#totalTime').text(totalTimeFormatted);
                            $('#timeDisplay').text(totalTimeFormatted);

                            // Reset current session time
                            $('#currentSessionTime').text('00:00:00');

                            // Update status
                            $('#timerStatus').html('<small class="text-success">Timer stopped and time saved!</small>');
                            setTimeout(() => {
                                $('#timerStatus').html('<small class="text-muted">Click Start Timer to begin tracking time</small>');
                            }, 3000);
                        } else {
                            console.error('Invalid timer data received');
                        }
                    } else {
                        console.error('Stop timer failed:', response);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error stopping timer:', error);
                    console.error('Response:', xhr.responseText);
                }
            });
        });

        // Cleanup timer data button
        $('#cleanupTimerBtn').on('click', function() {
            const taskId = $(this).data('task-id');
            const projectId = $(this).data('project-id');

            console.log('Cleanup timer data clicked for task:', taskId);

            $.ajax({
                url: `/projects/${projectId}/task/${taskId}/timer/cleanup`,
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    console.log('Cleanup response:', response);
                    if (response.success) {
                        // Reload timer status after cleanup
                        loadTimerStatus();
                        $('#timerStatus').html('<small class="text-success">Timer data fixed successfully!</small>');
                        setTimeout(() => {
                            $('#timerStatus').html('<small class="text-muted">Timer ready</small>');
                        }, 3000);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error cleaning up timer data:', error);
                    console.error('Response:', xhr.responseText);
                }
            });
        });

        // Alternative delete handler using different approach
        $('.delete-comment').on('click', function(e) {
            console.log('Alternative delete handler triggered');
        });

        // Test direct click handler for checklist
        $('#checklist_submit').on('click', function(e) {
            e.preventDefault();
            console.log('Direct checklist submit handler triggered');

            const form = $('#form-checklist');
            const input = form.find('input[name="name"]');
            const name = input.val().trim();

            console.log('Form found:', form.length > 0);
            console.log('Input found:', input.length > 0);
            console.log('Input value:', name);

            if (!name) {
                console.log('No name entered, focusing input');
                input.focus();
                return;
            }

            const url = form.data('action');
            console.log('Form action URL:', url);

            if (!url) {
                console.error('No form action URL found');
                return;
            }

            const token = $('meta[name="csrf-token"]').attr('content');
            console.log('CSRF token:', token);

            console.log('Sending AJAX request...');
            $.ajax({
                url: url,
                method: 'POST',
                data: {
                    name: name,
                    _token: token
                },
                success: function(response) {
                    console.log('AJAX success response:', response);
                    if (response.success) {
                        // Add new checklist item to the list
                        const updateUrl = '{{route("checklist.update",[$task->project_id,"__ID__"])}}'.replace('__ID__', response.checklist.id);
                        const deleteUrl = '{{route("checklist.destroy",[$task->project_id,"__ID__"])}}'.replace('__ID__', response.checklist.id);

                        console.log('Update URL:', updateUrl);
                        console.log('Delete URL:', deleteUrl);

                        const newItem = `
                            <div class="checklist-item" data-id="${response.checklist.id}">
                                <div class="d-flex align-items-center gap-3">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="check-item-${response.checklist.id}" data-url="${updateUrl}">
                                    </div>
                                    <label class="checklist-label" for="check-item-${response.checklist.id}">
                                        ${response.checklist.name}
                                    </label>
                                    <div class="checklist-actions ms-auto">
                                        <button class="btn btn-sm btn-ghost delete-checklist" data-url="${deleteUrl}" data-bs-toggle="tooltip" title="{{__('Delete')}}">
                                            <i class="ti ti-trash text-danger"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;

                        // Remove empty state if it exists
                        $('.empty-state').remove();

                        // Add new item to checklist
                        $('#checklist').append(newItem);
                        console.log('New item added to DOM');

                        // Bind event handlers to the new elements
                        const newCheckbox = $(`#check-item-${response.checklist.id}`);
                        newCheckbox.on('change', function() {
                            console.log('New checkbox direct handler triggered');
                            handleCheckboxChange(this);
                        });

                        const newDeleteButton = $(`.checklist-item[data-id="${response.checklist.id}"] .delete-checklist`);
                        newDeleteButton.on('click', function(e) {
                            e.preventDefault();
                            console.log('New delete button direct handler triggered');
                            handleChecklistDelete(this);
                        });

                        // Clear input and collapse form
                        input.val('');
                        form.collapse('hide');

                        // Update progress counter
                        updateChecklistProgress();
                    } else {
                        console.error('Server returned success=false:', response);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', error);
                    console.error('Status:', status);
                    console.error('Response:', xhr.responseText);
                }
            });
        });
    });

    // Reply functionality
    $(document).on('click', '.reply-btn', function() {
        let commentId = $(this).data('comment-id');
        let userName = $(this).data('user-name');

        // Hide all other reply forms
        $('.reply-form').hide();

        // Show this reply form
        $(`#reply-form-${commentId}`).show();

        // Focus on textarea and add @mention
        let textarea = $(`#reply-form-${commentId} .reply-textarea`);
        textarea.focus();
        textarea.val(`@${userName} `);
    });

    $(document).on('click', '.cancel-reply', function() {
        let commentId = $(this).data('comment-id');
        $(`#reply-form-${commentId}`).hide();
        $(`#reply-form-${commentId} .reply-textarea`).val('');
    });

    $(document).on('click', '.submit-reply', function() {
        let commentId = $(this).data('comment-id');
        let replyText = $(`#reply-form-${commentId} .reply-textarea`).val();

        if (replyText.trim() === '') {
            alert('Please enter a reply');
            return;
        }

        // Submit reply to backend
        submitReplyToBackend(commentId, replyText);
    });

    // Add this new function to actually save to database
    function submitReplyToBackend(parentId, replyText) {
        $.ajax({
            url: '{{route("task.comment.store",[$task->project_id,$task->id])}}',
            method: 'POST',
            data: {
                comment: replyText,
                parent_id: parentId,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                console.log('Reply saved successfully');
                // Hide reply form and clear textarea
                $(`#reply-form-${parentId}`).hide();
                $(`#reply-form-${parentId} .reply-textarea`).val('');
                // Reload page to show new reply
                location.reload();
            },
            error: function(xhr, status, error) {
                console.error('Reply error:', xhr.responseText);
                console.log('Data sent:', {
                    comment: replyText,
                    parent_id: parentId
                });
                alert('Error submitting reply');
            }
        });
    }

    // Reaction functionality
    $(document).on('click', '.reaction-btn', function() {
        let commentId = $(this).data('comment-id');
        let reaction = $(this).data('reaction');

        console.log('Reaction clicked:', {commentId, reaction});

        $.ajax({
            url: '/projects/{{$task->project_id}}/comment/{{$task->id}}/react/' + commentId,
            method: 'POST',
            data: {
                reaction: reaction,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                console.log('Reaction response:', response);
                if (response.success) {
                    updateReactionCounts(commentId, response.reactions);
                }
            },
            error: function(xhr, status, error) {
                console.error('Reaction error:', xhr.responseText);
                console.error('Status:', status);
                console.error('Error:', error);
            }
        });
    });

    function updateReactionCounts(commentId, reactions) {
        console.log('Updating reaction counts:', {commentId, reactions});
        let container = $(`#reaction-counts-${commentId}`);
        console.log('Container found:', container.length > 0);
        container.empty();

        Object.entries(reactions).forEach(([reaction, count]) => {
            let emoji = reaction === 'like' ? '👍' : '❤️';
            let badge = `<span class="badge bg-light text-dark me-1">${emoji} ${count}</span>`;
            container.append(badge);
            console.log('Added badge:', badge);
        });
    }

    // Direct delete function called by onclick
    function deleteComment(element, event) {
        event.preventDefault();
        event.stopPropagation();
        console.log('Direct delete function called');

        const url = $(element).data('url');
        console.log('Delete URL:', url);

        if (!url) {
            console.error('No delete URL found');
            alert('Error: No delete URL found');
            return;
        }

        if (confirm('{{__("Are you sure you want to delete this comment?")}}')) {
            const commentItem = $(element).closest('.modern-comment-item, .reply-item');
            console.log('Comment item found:', commentItem.length > 0);

            // Close the dropdown
            $(element).closest('.dropdown-menu').prev('.dropdown-toggle').dropdown('hide');

            $.ajax({
                url: url,
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                    'Content-Type': 'application/json'
                },
                success: function(response) {
                    console.log('Delete response:', response);
                    if (response.success) {
                        commentItem.fadeOut(300, function() {
                            $(this).remove();
                            // Update comment count
                            const currentCount = parseInt($('.badge').text()) || 0;
                            $('.badge').text(Math.max(0, currentCount - 1));

                            // Show no comments message if no comments left
                            if ($('.modern-comment-item').length === 0) {
                                $('#modernCommentsList').html('<div class="no-comments-state" id="noCommentsMessage"><div class="no-comments-icon">💬</div><div class="no-comments-text">{{__("No comments yet")}}</div><div class="no-comments-subtext">{{__("Be the first to share your thoughts")}}</div></div>');
                            }
                        });
                    } else {
                        alert(response.message || '{{__("Error deleting comment")}}');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error deleting comment:', xhr.responseText);
                    console.error('Status:', status);
                    console.error('Error:', error);

                    let errorMessage = '{{__("Error deleting comment")}}';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    alert(errorMessage);
                }
            });
        }
    }
</script>
@push('script-page')
    <script>
        // Use immediate function to ensure it runs when modal content is loaded
        (function() {
            console.log('🔍 Task comment script loading...');

            // Function to initialize comment functionality
            function initializeCommentForm() {
                console.log('🔧 Initializing comment form...');

                // Initialize tooltips
                $('[data-bs-toggle="tooltip"]').tooltip();

                // Debug: Check if elements exist
                console.log('Toggle button found:', $('#toggleCommentForm').length);
                console.log('Comment form found:', $('#commentForm').length);
                console.log('Modal body found:', $('.modal-body').length);

                // Remove any existing event handlers to prevent duplicates
                $(document).off('click', '#toggleCommentForm');
                $(document).off('click', '#cancelComment');
                $(document).off('submit', '#inlineCommentForm');

                // Toggle comment form with event delegation
                $(document).on('click', '#toggleCommentForm', function(e) {
                    console.log('🟢 Add Comment button clicked!');
                    e.preventDefault();
                    e.stopPropagation();

                    const form = $('#commentForm');
                    const button = $(this);

                    console.log('Form element:', form);
                    console.log('Form visible:', form.is(':visible'));
                    console.log('Button element:', button);

                    if (form.is(':visible')) {
                        console.log('Hiding form...');
                        form.slideUp(300);
                        button.html('<i class="ti ti-plus"></i> {{__("Add Comment")}}');
                    } else {
                        console.log('Showing form...');
                        form.slideDown(300);
                        button.html('<i class="ti ti-minus"></i> {{__("Cancel")}}');
                        setTimeout(function() {
                            $('#commentTextarea').focus();
                        }, 350);
                    }
                });
            }

            // Initialize immediately if DOM is ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initializeCommentForm);
            } else {
                initializeCommentForm();
            }

            // Also initialize when modal is shown (for AJAX loaded content)
            $(document).on('shown.bs.modal', '#commonModal', function() {
                console.log('🔄 Modal shown, reinitializing comment form...');
                setTimeout(initializeCommentForm, 100);
            });

            // Initialize on jQuery ready as backup
            $(document).ready(function() {
                console.log('🔄 jQuery ready, initializing comment form...');
                initializeCommentForm();
            });
        })();

        // Use event delegation for all comment-related actions
        $(document).ready(function() {
            // Cancel comment
            $(document).on('click', '#cancelComment', function(e) {
                e.preventDefault();
                $('#commentForm').slideUp(300);
                $('#toggleCommentForm').html('<i class="ti ti-plus"></i> {{__("Add Comment")}}');
                $('#commentTextarea').val('');
            });

            // Submit comment form
            $(document).on('submit', '#inlineCommentForm', function(e) {
                e.preventDefault();

                const form = $(this);
                const textarea = $('#commentTextarea');
                const comment = textarea.val().trim();
                const submitBtn = $('#submitComment');

                if (!comment) {
                    alert('{{__("Please enter a comment")}}');
                    return;
                }

                // Show loading state
                submitBtn.prop('disabled', true).html('<i class="ti ti-loader ti-spin"></i> {{__("Adding...")}}');

                $.ajax({
                    url: form.attr('action'),
                    method: 'POST',
                    data: form.serialize(),
                    success: function(response) {
                        if (response.success) {
                            // Reset form
                            textarea.val('');
                            $('#commentForm').slideUp(300);
                            $('#toggleCommentForm').html('<i class="ti ti-plus"></i> {{__("Add Comment")}}');

                            // Show success message
                            if (typeof show_toastr === 'function') {
                                show_toastr('{{__("Success")}}', '{{__("Comment added successfully")}}', 'success');
                            }

                            // Remove "no comments" message if it exists
                            $('#noCommentsMessage').remove();

                            // Create new comment HTML
                            const comment = response.comment;
                            const userName = '{{ \Auth::user()->name }}';
                            const userInitial = userName.charAt(0).toUpperCase();

                            const commentHtml = `
                                <div class="comment-item border-bottom pb-3 mb-3">
                                    <div class="d-flex">
                                        <div class="flex-shrink-0">
                                            <div class="theme-avtar bg-primary">
                                                <span>${userInitial}</span>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1">${userName}</h6>
                                                    <small class="text-muted">{{__('just now')}}</small>
                                                </div>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                        <i class="ti ti-dots-vertical"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a class="dropdown-item text-danger delete-comment" href="#" data-url="${comment.deleteUrl}">
                                                                <i class="ti ti-trash"></i> {{__('Delete')}}
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <div class="comment-text mt-2">
                                                <p class="mb-0">${comment.comment}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `;

                            // Add new comment to the top of the comments list
                            $('#commentsList').prepend(commentHtml);

                            // Update comment count
                            const currentCount = parseInt($('.badge').text()) || 0;
                            $('.badge').text(currentCount + 1);
                        } else {
                            // Show error message
                            if (typeof show_toastr === 'function') {
                                show_toastr('{{__("Error")}}', response.message || '{{__("Error adding comment")}}', 'error');
                            }
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error adding comment:', error);

                        // Show error message using toastr if available
                        if (typeof show_toastr === 'function') {
                            show_toastr('{{__("Error")}}', '{{__("Error adding comment. Please try again.")}}', 'error');
                        } else {
                            alert('{{__("Error adding comment. Please try again.")}}');
                        }
                    },
                    complete: function() {
                        // Reset button state
                        submitBtn.prop('disabled', false).html('<i class="ti ti-send"></i> {{__("Add Comment")}}');
                    }
                });
            });

            // Handle delete comment
            $(document).on('click', '.delete-comment', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Delete comment clicked');

                const $this = $(this);
                const url = $this.data('url');
                console.log('Delete URL:', url);

                if (!url) {
                    console.error('No delete URL found');
                    alert('Error: No delete URL found');
                    return;
                }

                if (confirm('{{__("Are you sure you want to delete this comment?")}}')) {
                    const commentItem = $this.closest('.modern-comment-item, .reply-item');
                    console.log('Comment item found:', commentItem.length > 0);

                    // Close the dropdown
                    $this.closest('.dropdown-menu').prev('.dropdown-toggle').dropdown('hide');

                    $.ajax({
                        url: url,
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                            'Content-Type': 'application/json'
                        },
                        success: function(response) {
                            console.log('Delete response:', response);
                            if (response.success) {
                                commentItem.fadeOut(300, function() {
                                    $(this).remove();
                                    // Update comment count
                                    const currentCount = parseInt($('.badge').text()) || 0;
                                    $('.badge').text(Math.max(0, currentCount - 1));

                                    // Show no comments message if no comments left
                                    if ($('.modern-comment-item').length === 0) {
                                        $('#modernCommentsList').html('<div class="no-comments-state" id="noCommentsMessage"><div class="no-comments-icon">💬</div><div class="no-comments-text">{{__("No comments yet")}}</div><div class="no-comments-subtext">{{__("Be the first to share your thoughts")}}</div></div>');
                                    }
                                });
                            } else {
                                alert(response.message || '{{__("Error deleting comment")}}');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('Error deleting comment:', xhr.responseText);
                            console.error('Status:', status);
                            console.error('Error:', error);

                            let errorMessage = '{{__("Error deleting comment")}}';
                            if (xhr.responseJSON && xhr.responseJSON.message) {
                                errorMessage = xhr.responseJSON.message;
                            }
                            alert(errorMessage);
                        }
                    });
                }
            });

            // Enhanced button interactions
            $('.btn-action-modern').on('mouseenter', function() {
                $(this).addClass('hover-effect');
            }).on('mouseleave', function() {
                $(this).removeClass('hover-effect');
            });

            // Smooth badge hover effects
            $('.priority-badge-modern, .status-badge-modern').on('mouseenter', function() {
                $(this).addClass('badge-hover');
            }).on('mouseleave', function() {
                $(this).removeClass('badge-hover');
            });

            // Delete button confirmation
            $('.btn-delete').on('click', function(e) {
                e.preventDefault();
                const confirmMessage = $(this).data('confirm') || 'Are you sure you want to delete this task?';
                const deleteUrl = $(this).data('url');
                const button = $(this);

                if (confirm(confirmMessage)) {
                    // Add loading state
                    button.addClass('loading').prop('disabled', true);
                    button.find('i').removeClass('ti-trash').addClass('ti-loader');

                    // Make AJAX call to delete the task
                    $.ajax({
                        url: deleteUrl,
                        type: 'DELETE',
                        dataType: 'JSON',
                        data: {
                            "_token": "{{ csrf_token() }}"
                        },
                        success: function(data) {
                            // Show success state
                            button.removeClass('loading').addClass('success');
                            button.find('i').removeClass('ti-loader').addClass('ti-check');

                            // Show success message
                            show_toastr('{{ __('Success') }}', '{{ __('Task Deleted Successfully!') }}', 'success');

                            // Close modal after short delay
                            setTimeout(() => {
                                $('.modal').modal('hide');
                                // Refresh the page or update the task list
                                if (typeof load_task === 'function') {
                                    location.reload(); // Reload to update the task list
                                }
                            }, 1000);
                        },
                        error: function(xhr) {
                            // Reset button state
                            button.removeClass('loading').prop('disabled', false);
                            button.find('i').removeClass('ti-loader').addClass('ti-trash');

                            // Show error message
                            const response = xhr.responseJSON;
                            const errorMessage = response && response.message ? response.message : '{{ __('Something went wrong!') }}';
                            show_toastr('{{ __('Error') }}', errorMessage, 'error');
                        }
                    });
                }
            });

            // Handle progress slider updates
            $('.task_progress').on('input change', function() {
                const value = $(this).val();
                $(this).closest('.progress-wrapper').find('.progress-text').text(value + '%');
                $(this).closest('.progress-wrapper').find('.progress-bar').css('width', value + '%');
            });

            // Handle checklist item completion (event delegation)
            $(document).on('change', '.checklist-item input[type="checkbox"]', function() {
                console.log('Event delegation checkbox handler triggered');
                handleCheckboxChange(this);
            });

            // Handle adding new checklist items
            $(document).on('click', '#checklist_submit', function(e) {
                e.preventDefault();
                console.log('Checklist submit button clicked');

                const form = $('#form-checklist');
                const input = form.find('input[name="name"]');
                const name = input.val().trim();

                console.log('Form found:', form.length > 0);
                console.log('Input found:', input.length > 0);
                console.log('Input value:', name);

                if (!name) {
                    console.log('No name entered, focusing input');
                    input.focus();
                    return;
                }

                const url = form.data('action');
                console.log('Form action URL:', url);

                if (!url) {
                    console.error('No form action URL found');
                    return;
                }

                console.log('Sending AJAX request...');
                $.ajax({
                    url: url,
                    method: 'POST',
                    data: {
                        name: name,
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        console.log('AJAX success response:', response);
                        if (response.success) {
                            // Add new checklist item to the list
                            const updateUrl = '{{route("checklist.update",[$task->project_id,"__ID__"])}}'.replace('__ID__', response.checklist.id);
                            const deleteUrl = '{{route("checklist.destroy",[$task->project_id,"__ID__"])}}'.replace('__ID__', response.checklist.id);

                            console.log('Update URL:', updateUrl);
                            console.log('Delete URL:', deleteUrl);

                            const newItem = `
                                <div class="checklist-item" data-id="${response.checklist.id}">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="check-item-${response.checklist.id}" data-url="${updateUrl}">
                                        </div>
                                        <label class="checklist-label" for="check-item-${response.checklist.id}">
                                            ${response.checklist.name}
                                        </label>
                                        <div class="checklist-actions ms-auto">
                                            <button class="btn btn-sm btn-ghost delete-checklist" data-url="${deleteUrl}" data-bs-toggle="tooltip" title="{{__('Delete')}}">
                                                <i class="ti ti-trash text-danger"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            `;

                            // Remove empty state if it exists
                            $('.empty-state').remove();

                            // Add new item to checklist
                            $('#checklist').append(newItem);
                            console.log('New item added to DOM');

                            // Clear input and collapse form
                            input.val('');
                            form.collapse('hide');

                            // Update progress counter
                            updateChecklistProgress();
                        } else {
                            console.error('Server returned success=false:', response);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', error);
                        console.error('Status:', status);
                        console.error('Response:', xhr.responseText);
                    }
                });
            });

            // Handle Enter key in checklist input
            $(document).on('keypress', '#form-checklist input[name="name"]', function(e) {
                if (e.which === 13) { // Enter key
                    $('#checklist_submit').click();
                }
            });

            // Handle deleting checklist items (event delegation)
            $(document).on('click', '.delete-checklist', function(e) {
                e.preventDefault();
                console.log('Event delegation delete handler triggered');
                handleChecklistDelete(this);
            });

            // Function moved to global scope above

            // Handle file input preview
            $('#task_attachment').on('change', function() {
                const file = this.files[0];
                const preview = $('#blah');

                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        if (file.type.startsWith('image/')) {
                            preview.attr('src', e.target.result).show();
                        } else {
                            preview.hide();
                        }
                    };
                    reader.readAsDataURL(file);
                } else {
                    preview.hide();
                }
            });


            // Smooth scroll for long content
            $('.task-detail-modern').on('scroll', function() {
                const scrollTop = $(this).scrollTop();
                if (scrollTop > 50) {
                    $('.task-header').addClass('scrolled');
                } else {
                    $('.task-header').removeClass('scrolled');
                }
            });

            // Color picker functionality (existing)
            $(".colorPickSelector").colorPick({
                'onColorSelected': function () {
                    var task_id = this.element.parents('.side-modal').attr('id');
                    var color = this.color;

                    if (task_id) {
                        this.element.css({'backgroundColor': color});
                        $.ajax({
                            url: '{{ route('update.task.priority.color') }}',
                            method: 'PATCH',
                            data: {
                                'task_id': task_id,
                                'color': color,
                            },
                            success: function (data) {
                                $('.task-list-items').find('#' + task_id).attr('style', 'border-left:2px solid ' + color + ' !important');
                            }
                        });
                    }
                }
            });

            // Initialize progress counter on load
            updateChecklistProgress();
        });
    </script>
@endpush
