<div class="modern-card">
    <div class="table-responsive">
        <table class="table align-items-center">
            <thead>
                <tr>
                    <th scope="col">{{ __('Task Name') }}</th>
                    <th scope="col">{{ __('Stage') }}</th>
                    <th scope="col">{{ __('Priority') }}</th>
                    <th scope="col">{{ __('Assigned To') }}</th>
                    <th scope="col">{{ __('End Date') }}</th>
                    <th scope="col">{{ __('Progress') }}</th>
                    <th scope="col" class="text-end">{{ __('Actions') }}</th>
                </tr>
            </thead>
            <tbody>
                @if(count($tasks) > 0)
                    @foreach($tasks as $task)
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="info-icon-small me-3">
                                        <i class="ti ti-checklist"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1 fw-semibold">
                                            <a href="#" class="dashboard-link text-dark"
                                               data-url="{{ route('projects.tasks.show', [$project->id, $task->id]) }}"
                                               data-ajax-popup="true" data-size="lg"
                                               data-bs-original-title="{{ $task->name }}">
                                                {{ $task->name }}
                                            </a>
                                        </h6>
                                        @if($task->description)
                                            <small class="text-muted">{{ Str::limit($task->description, 50) }}</small>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ $task->stage->name ?? '-' }}</span>
                            </td>
                            <td>
                                @if($task->priority)
                                    <span class="badge bg-{{ \App\Models\ProjectTask::$priority_color[$task->priority] }}">
                                        {{ __(\App\Models\ProjectTask::$priority[$task->priority]) }}
                                    </span>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                @if($task->users->count() > 0)
                                    <div class="d-flex align-items-center">
                                        @foreach($task->users->take(3) as $user)
                                            <div class="user-avatar-text bg-primary text-white me-1" 
                                                 style="width: 28px; height: 28px; font-size: 12px;"
                                                 data-bs-toggle="tooltip" title="{{ $user->name }}">
                                                {{ substr($user->name, 0, 1) }}
                                            </div>
                                        @endforeach
                                        @if($task->users->count() > 3)
                                            <small class="text-muted ms-2">+{{ $task->users->count() - 3 }}</small>
                                        @endif
                                    </div>
                                @else
                                    <span class="text-muted">{{ __('Unassigned') }}</span>
                                @endif
                            </td>
                            <td>
                                @if($task->end_date)
                                    <span class="text-{{ \Carbon\Carbon::parse($task->end_date)->isPast() ? 'danger' : 'muted' }}">
                                        {{ \App\Models\Utility::getDateFormated($task->end_date) }}
                                    </span>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                @php
                                    $percentage = $task->taskProgress();
                                @endphp
                                <div class="d-flex align-items-center">
                                    <div class="progress me-2" style="width: 80px; height: 6px;">
                                        <div class="progress-bar bg-{{ $percentage['color'] }}" 
                                             style="width: {{ $percentage['percentage'] }}%"></div>
                                    </div>
                                    <small class="text-muted">{{ $percentage['percentage'] }}%</small>
                                </div>
                            </td>
                            <td class="text-end">
                                <div class="btn-group">
                                    @can('view project task')
                                        <a href="#" class="btn btn-sm btn-outline-primary dashboard-link"
                                           data-url="{{ route('projects.tasks.show', [$project->id, $task->id]) }}"
                                           data-ajax-popup="true" data-size="lg"
                                           data-bs-toggle="tooltip" title="{{ __('View') }}">
                                            <i class="ti ti-eye"></i>
                                        </a>
                                    @endcan
                                    @can('edit project task')
                                        <a href="#" class="btn btn-sm btn-outline-warning"
                                           data-url="{{ route('projects.tasks.edit', [$project->id, $task->id]) }}"
                                           data-ajax-popup="true" data-size="lg"
                                           data-bs-toggle="tooltip" title="{{ __('Edit') }}">
                                            <i class="ti ti-pencil"></i>
                                        </a>
                                    @endcan
                                    @can('delete project task')
                                        <a href="#" class="btn btn-sm btn-outline-danger bs-pass-para"
                                           data-confirm="{{ __('Are You Sure?') }}"
                                           data-text="{{ __('This action can not be undone. Do you want to continue?') }}"
                                           data-confirm-yes="delete-form-{{ $task->id }}"
                                           data-bs-toggle="tooltip" title="{{ __('Delete') }}">
                                            <i class="ti ti-trash"></i>
                                        </a>
                                        {!! Form::open(['method' => 'DELETE', 'route' => ['projects.tasks.destroy', $project->id, $task->id], 'id' => 'delete-form-' . $task->id, 'style' => 'display:none']) !!}
                                        {!! Form::close() !!}
                                    @endcan
                                </div>
                            </td>
                        </tr>
                    @endforeach
                @else
                    <tr>
                        <td colspan="7" class="text-center py-5">
                            <div class="info-icon-large d-inline-flex mb-3">
                                <i class="ti ti-checklist"></i>
                            </div>
                            <h6 class="text-muted mb-2">{{ __('No Tasks Found') }}</h6>
                            <p class="text-muted small mb-0">{{ __('Tasks will appear here when created') }}</p>
                        </td>
                    </tr>
                @endif
            </tbody>
        </table>
    </div>
</div>

@if(count($tasks) > 0)
    <div class="d-flex justify-content-between align-items-center mt-4">
        <div class="d-flex align-items-center gap-4">
            <div class="d-flex align-items-center gap-2">
                <div class="info-icon-small">
                    <i class="ti ti-checklist"></i>
                </div>
                <span class="fw-semibold">{{ count($tasks) }} {{ __('Total Tasks') }}</span>
            </div>
            <div class="d-flex align-items-center gap-2">
                <div class="info-icon-small bg-success bg-opacity-10">
                    <i class="ti ti-check text-success"></i>
                </div>
                <span class="text-success fw-semibold">
                    {{ $tasks->where('stage.name', 'Done')->count() }} {{ __('Completed') }}
                </span>
            </div>
        </div>
        @can('create project task')
            <a href="#" class="action-btn-modern action-btn-primary"
               data-url="{{ route('projects.tasks.create', [$project->id, 1]) }}"
               data-ajax-popup="true" data-size="lg">
                <i class="ti ti-plus me-2"></i>{{ __('Add Task') }}
            </a>
        @endcan
    </div>
@endif
