@extends('layouts.admin')
@section('page-title')
    {{ __('Edit Employee') }}
@endsection
@section('breadcrumb')
    <li class="breadcrumb-item">
        <a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a>
    </li>
    <li class="breadcrumb-item"><a href="{{ route('employee.index') }}">{{ __('Employee') }}</a></li>
    <li class="breadcrumb-item">{{ $employee->name }}</li>
@endsection


@section('content')
    <div class="row">
        {{ Form::model($employee, ['route' => ['employee.update', $employee->id], 'method' => 'PUT', 'enctype' => 'multipart/form-data', 'class' => 'needs-validation', 'novalidate']) }}
        <div class="row">
            <div class="col-md-12">
                <div class="card emp_details">
                    <div class="card-header">
                        <h6 class="mb-0">{{ __('Personal Detail') }}</h6>
                    </div>
                    <div class="card-body employee-detail-edit-body">

                        <div class="row">
                            <div class="form-group col-md-6">
                                {!! Form::label('name', __('Name'), ['class' => 'form-label']) !!}<x-required></x-required>
                                {!! Form::text('name', null, [
                                    'class' => 'form-control',
                                    'required' => 'required',
                                    'placeholder' => __('Enter employee name'),
                                ]) !!}
                            </div>
                            <div class="col-md-6">
                                <x-mobile label="{{ __('Phone') }}" name="phone" value="{{ $employee->phone }}" required
                                    placeholdeer="Enter employee phone"></x-mobile>

                            </div>
                            <div class="form-group col-md-6">

                                {!! Form::label('dob', __('Date of Birth'), ['class' => 'form-label']) !!}<x-required></x-required>
                                {!! Form::date('dob', null, ['class' => 'form-control', 'required' => 'required', 'max' => date('Y-m-d')]) !!}

                            </div>
                            <div class="form-group col-md-6">
                                {!! Form::label('gender', __('Gender'), ['class' => 'form-label']) !!}<x-required></x-required>
                                <div class="d-flex radio-check">
                                    <div class="form-check form-check-inline form-group">
                                        <input type="radio" id="g_male" value="Male" name="gender"
                                            class="form-check-input" {{ $employee->gender == 'Male' ? 'checked' : '' }}
                                            required>
                                        <label class="form-check-label" for="g_male">{{ __('Male') }}</label>
                                    </div>
                                    <div class="form-check form-check-inline form-group">
                                        <input type="radio" id="g_female" value="Female" name="gender"
                                            class="form-check-input" {{ $employee->gender == 'Female' ? 'checked' : '' }}
                                            required>
                                        <label class="form-check-label" for="g_female">{{ __('Female') }}</label>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="form-group mb-0">
                            {!! Form::label('address', __('Address'), ['class' => 'form-label']) !!}<x-required></x-required>
                            {!! Form::textarea('address', null, [
                                'class' => 'form-control',
                                'rows' => 2,
                                'required' => 'required',
                                'placeholder' => __('Enter employee address'),
                            ]) !!}
                        </div>
                    </div>
                </div>
        </div>

        <!-- Module Permissions Section -->
        <hr>
        <div class="col-12 mt-4">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5>{{ __('Module Permissions') }}</h5>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-primary me-2" id="selectAllModules">{{ __('Select All Modules') }}</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="deselectAllModules">{{ __('Deselect All') }}</button>
                </div>
            </div>
            <hr>
            @foreach($availableModules as $moduleKey => $moduleData)
                @php
                    $createPermissions = [];
                    $readPermissions = [];
                    $deletePermissions = [];
                    $otherPermissions = [];
                    $employeePermissions = ($employee->user && $employee->user->module_permissions) ? ($employee->user->module_permissions[$moduleKey] ?? []) : [];
                    if (is_array($moduleData) && isset($moduleData['permissions'])) {
                        foreach($moduleData['permissions'] as $permission) {
                            if (str_contains($permission, 'create')) {
                                $createPermissions[] = $permission;
                            } elseif (str_contains($permission, 'view') || str_contains($permission, 'show') || str_contains($permission, 'dashboard')) {
                                $readPermissions[] = $permission;
                            } elseif (str_contains($permission, 'delete')) {
                                $deletePermissions[] = $permission;
                            } else {
                                $otherPermissions[] = $permission;
                            }
                        }
                    }
                @endphp
                <div class="card mb-3">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="form-check">
                                <input class="form-check-input module-checkbox" type="checkbox" id="module_{{ $moduleKey }}" name="modules[{{ $moduleKey }}]" data-module="{{ $moduleKey }}" {{ !empty($employeePermissions) ? 'checked' : '' }}>
                                <label class="form-check-label fw-bold" for="module_{{ $moduleKey }}">
                                    {{ is_array($moduleData) && isset($moduleData['name']) ? $moduleData['name'] : $moduleKey }}
                                </label>
                            </div>
                            <div class="btn-group" role="group" style="display: none;" id="selectButtons_{{ $moduleKey }}">
                                <button type="button" class="btn btn-sm btn-outline-success select-all-module" data-module="{{ $moduleKey }}">{{ __('Select All') }}</button>
                                <button type="button" class="btn btn-sm btn-outline-warning select-create" data-module="{{ $moduleKey }}">{{ __('Create') }}</button>
                                <button type="button" class="btn btn-sm btn-outline-info select-read" data-module="{{ $moduleKey }}">{{ __('Read') }}</button>
                                <button type="button" class="btn btn-sm btn-outline-danger select-delete" data-module="{{ $moduleKey }}">{{ __('Delete') }}</button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body permissions-section" id="permissions_{{ $moduleKey }}" style="display: {{ !empty($employeePermissions) ? 'block' : 'none' }};">
                        @if(count($createPermissions) > 0)
                            <div class="permission-group mb-4">
                                <h6 class="text-success mb-2"><i class="fas fa-plus-circle"></i> {{ __('Create Permissions') }}</h6>
                                <div class="row">
                                    @foreach($createPermissions as $permission)
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input permission-checkbox create-permission" type="checkbox" id="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}" name="permissions[{{ $moduleKey }}][]" value="{{ $permission }}" data-module="{{ $moduleKey }}" data-type="create" {{ in_array($permission, $employeePermissions) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}">{{ ucwords(str_replace('_', ' ', $permission)) }}</label>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                        @if(count($readPermissions) > 0)
                            <div class="permission-group mb-4">
                                <h6 class="text-info mb-2"><i class="fas fa-eye"></i> {{ __('Read/View Permissions') }}</h6>
                                <div class="row">
                                    @foreach($readPermissions as $permission)
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input permission-checkbox read-permission" type="checkbox" id="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}" name="permissions[{{ $moduleKey }}][]" value="{{ $permission }}" data-module="{{ $moduleKey }}" data-type="read" {{ in_array($permission, $employeePermissions) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}">{{ ucwords(str_replace('_', ' ', $permission)) }}</label>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                        @if(count($deletePermissions) > 0)
                            <div class="permission-group mb-4">
                                <h6 class="text-danger mb-2"><i class="fas fa-trash"></i> {{ __('Delete Permissions') }}</h6>
                                <div class="row">
                                    @foreach($deletePermissions as $permission)
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input permission-checkbox delete-permission" type="checkbox" id="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}" name="permissions[{{ $moduleKey }}][]" value="{{ $permission }}" data-module="{{ $moduleKey }}" data-type="delete" {{ in_array($permission, $employeePermissions) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}">{{ ucwords(str_replace('_', ' ', $permission)) }}</label>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                        @if(count($otherPermissions) > 0)
                            <div class="permission-group mb-4">
                                <h6 class="text-secondary mb-2"><i class="fas fa-cog"></i> {{ __('Other Permissions') }}</h6>
                                <div class="row">
                                    @foreach($otherPermissions as $permission)
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input permission-checkbox other-permission" type="checkbox" id="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}" name="permissions[{{ $moduleKey }}][]" value="{{ $permission }}" data-module="{{ $moduleKey }}" data-type="other" {{ in_array($permission, $employeePermissions) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="permission_{{ $moduleKey }}_{{ str_replace(' ', '_', $permission) }}">{{ ucwords(str_replace('_', ' ', $permission)) }}</label>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>
        <!-- End module permissions section -->
        <div class="float-end mt-3">
            @if (\Auth::user()->type != 'employee')
                <input type="button" value="{{ __('Cancel') }}"
                    onclick="location.href = '{{ route('employee.index') }}';" class="btn btn-secondary me-2">
            @endif
            <input type="submit" value="{{ __('Update') }}" class="btn btn-primary">
        </div>
        {!! Form::close() !!}
    </div>
@endsection

@push('script-page')
    <script>
        $(document).ready(function() {
            $('.module-checkbox').on('change', function() {
                var module = $(this).data('module');
                var permissionsSection = $('#permissions_' + module);
                var permissionCheckboxes = permissionsSection.find('.permission-checkbox');
                var selectButtons = $('#selectButtons_' + module);
                if ($(this).is(':checked')) {
                    permissionsSection.show();
                    selectButtons.show();
                } else {
                    permissionsSection.hide();
                    selectButtons.hide();
                    permissionCheckboxes.prop('checked', false);
                }
            });
            $('.permission-checkbox').on('change', function() {
                var module = $(this).data('module');
                var moduleCheckbox = $('#module_' + module);
                var permissionCheckboxes = $('input[name="permissions[' + module + '][]"]');
                var checkedPermissions = permissionCheckboxes.filter(':checked');
                var selectButtons = $('#selectButtons_' + module);
                if (checkedPermissions.length > 0) {
                    moduleCheckbox.prop('checked', true);
                    $('#permissions_' + module).show();
                    selectButtons.show();
                } else {
                    moduleCheckbox.prop('checked', false);
                    $('#permissions_' + module).hide();
                    selectButtons.hide();
                }
            });
            $('#selectAllModules').on('click', function() {
                $('.module-checkbox').prop('checked', true).trigger('change');
            });
            $('#deselectAllModules').on('click', function() {
                $('.module-checkbox').prop('checked', false).trigger('change');
            });
            $('.select-all-module').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .permission-checkbox').prop('checked', true);
            });
            $('.select-create').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .create-permission').prop('checked', true);
            });
            $('.select-read').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .read-permission').prop('checked', true);
            });
            $('.select-delete').on('click', function() {
                var module = $(this).data('module');
                $('#permissions_' + module + ' .delete-permission').prop('checked', true);
            });

            // Initialize permissions display for already selected modules on page load
            $('.module-checkbox:checked').each(function() {
                var module = $(this).data('module');
                var permissionsSection = $('#permissions_' + module);
                var selectButtons = $('#selectButtons_' + module);
                permissionsSection.show();
                selectButtons.show();
            });
        });
    </script>
@endpush
