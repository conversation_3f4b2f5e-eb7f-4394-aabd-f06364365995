<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('custom_fields', function (Blueprint $table) {
            // Drop the old column if it exists
            if (Schema::hasColumn('custom_fields', 'options')) {
                $table->dropColumn('options');
            }

            // Add new options column as JSON
            $table->json('options')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('custom_fields', function (Blueprint $table) {
            $table->dropColumn('options');
            $table->longText('options')->nullable(); // revert to longText if needed
        });
    }
};
